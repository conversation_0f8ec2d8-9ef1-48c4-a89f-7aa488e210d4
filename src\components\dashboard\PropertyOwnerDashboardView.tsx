'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/auth-context';
import { Location } from '@/types';
import { PropertyOwnerDashboardHeader } from '@/components/dashboard/PropertyOwnerDashboardHeader';
import { PropertyOwnerDashboardFilters } from '@/components/dashboard/PropertyOwnerDashboardFilters';
import { PropertyOwnerLocationGrid } from '@/components/dashboard/PropertyOwnerLocationGrid';
import { ProjectSkeletonGrid } from '@/components/dashboard/ProjectSkeleton';
import { EmptyLocationList } from '@/components/dashboard/EmptyLocationList';
import { NoLocationFound } from '@/components/dashboard/NoLocationFound';
import { usePropertyOwnerLocations } from '@/hooks/usePropertyOwnerLocations';
import { Routes } from '@/lib/routes';

export function PropertyOwnerDashboardView() {
  const { user } = useAuth();
  const router = useRouter();

  const [search, setSearch] = useState<string>('');
  const [status, setStatus] = useState<string>('all');
  const [locationType, setLocationType] = useState<string>('all');

  const {
    locationData,
    isLoading,
    isInitialLoading,
    isLoadingMore,
    hasMore,
    totalCount,
    hasFilters,
    fetchMoreData,
  } = usePropertyOwnerLocations({
    search,
    status,
    locationType,
  });

  const handleSubmitLocation = () => {
    router.push(Routes.ADMIN_CREATE_LOCATION);
  };

  const handleLocationEdit = (location: Location) => {
    router.push(`${Routes.ADMIN_EDIT_LOCATION}/${location.id}`);
  };

  const handleLocationDelete = (location: Location) => {
    // TODO: Implement delete functionality
    console.log('Delete location:', location.id);
  };

  const handleLocationView = (location: Location) => {
    router.push(`${Routes.ADMIN_EDIT_LOCATION}/${location.id}`);
  };

  return (
    <div className="w-full flex justify-center">
      <div className="space-y-6 xl:w-[70.5rem] p-6 xl:px-0 flex flex-col">
        <PropertyOwnerDashboardHeader userName={user?.firstName} />

        {isInitialLoading ? (
          <ProjectSkeletonGrid count={6} />
        ) : totalCount === 0 && !hasFilters ? (
          <EmptyLocationList onSubmitLocation={handleSubmitLocation} />
        ) : (
          <div className="flex flex-col w-full space-y-6 flex-1">
            <PropertyOwnerDashboardFilters
              search={search}
              onSearchChange={setSearch}
              locationType={locationType}
              onLocationTypeChange={setLocationType}
              status={status}
              onStatusChange={setStatus}
              onSubmitLocation={handleSubmitLocation}
            />

            {totalCount > 0 && (
              <p className="text-base font-medium leading-7 text-slate-950">
                {totalCount} {totalCount === 1 ? 'location' : 'locations'}
              </p>
            )}

            {locationData.length === 0 && !isLoading && hasFilters ? (
              <NoLocationFound />
            ) : (
              <PropertyOwnerLocationGrid
                locations={locationData}
                isLoading={isLoading}
                isLoadingMore={isLoadingMore}
                hasMore={hasMore}
                onLoadMore={fetchMoreData}
                onEdit={handleLocationEdit}
                onDelete={handleLocationDelete}
                onView={handleLocationView}
              />
            )}
          </div>
        )}
      </div>
    </div>
  );
}
