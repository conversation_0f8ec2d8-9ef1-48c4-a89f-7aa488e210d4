'use client';

import { Skeleton } from '@/components/ui/skeleton';

export default function Loading() {
  return (
    <div className="space-y-6 py-4 sm:py-6">
      {/* Header with search and filters */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center">
          <div className="relative">
            <Skeleton className="h-10 w-64" />
          </div>
          <div className="flex gap-2">
            <Skeleton className="h-10 w-32" />
            <Skeleton className="h-10 w-24" />
          </div>
        </div>
        <Skeleton className="h-10 w-40" />
      </div>

      {/* Locations table */}
      <div className="border shadow-sm rounded-md">
        <div className="py-0 border-0 gap-0 rounded-md">
          <div className="h-[3.875rem] flex items-center px-6 bg-[#FAFAFA] rounded-md">
            <Skeleton className="h-6 w-32" />
          </div>
          <div className="px-0">
            <div className="border-t">
              <div className="overflow-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b">
                      <th className="px-6 py-3 text-left bg-[#FAFAFA]">
                        <Skeleton className="h-4 w-20" />
                      </th>
                      <th className="px-6 py-3 text-left bg-[#FAFAFA]">
                        <Skeleton className="h-4 w-24" />
                      </th>
                      <th className="px-6 py-3 text-left bg-[#FAFAFA]">
                        <Skeleton className="h-4 w-16" />
                      </th>
                      <th className="px-6 py-3 text-left bg-[#FAFAFA]">
                        <Skeleton className="h-4 w-20" />
                      </th>
                      <th className="px-6 py-3 text-left bg-[#FAFAFA]">
                        <Skeleton className="h-4 w-12" />
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {Array.from({ length: 6 }).map((_, i) => (
                      <tr key={`s-${i}`} className="border-b">
                        <td className="px-6 py-4">
                          <div className="flex items-center gap-2">
                            <Skeleton className="h-12 w-12 rounded" />
                            <div className="space-y-1">
                              <Skeleton className="h-4 w-32" />
                              <Skeleton className="h-3 w-24" />
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <Skeleton className="h-4 w-40" />
                        </td>
                        <td className="px-6 py-4">
                          <div className="flex gap-1">
                            {Array.from({ length: 2 }).map((_, j) => (
                              <Skeleton key={j} className="h-6 w-16" />
                            ))}
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <Skeleton className="h-4 w-20" />
                        </td>
                        <td className="px-6 py-4">
                          <Skeleton className="h-8 w-8" />
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Pagination */}
      <div className="flex items-center justify-center">
        <div className="flex items-center space-x-2">
          <Skeleton className="h-9 w-9" />
          <div className="flex space-x-1">
            {Array.from({ length: 3 }).map((_, i) => (
              <Skeleton key={i} className="h-9 w-9" />
            ))}
          </div>
          <Skeleton className="h-9 w-9" />
        </div>
      </div>
    </div>
  );
}
