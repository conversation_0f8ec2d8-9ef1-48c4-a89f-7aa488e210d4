'use client';

import { PaginationState } from '@tanstack/react-table';
import { Button } from '@/components/ui/button';
import UserEditModal from '@/components/features/users/UserEditModal';
import ConfirmDialog from '@/components/shared/ConfirmDialog';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { Plus } from 'lucide-react';
import DataTablePagination from '@/components/shared/DataTablePagination';
import Loading from './loading';
import AddUserModal from '@/components/features/users/AddUserModal';
import { User } from '@/types/user';
import {
  useUsers,
  useUserRequests,
  useDeleteUser,
  useUpdateUserStatus,
} from '@/hooks/api/useUsers';
import { useDebounce } from '@/hooks/useDebounce';
import { UsersViewToggle } from './UsersViewToggle';
import { UsersFilters } from './UsersFilters';
import { UsersTable, useUsersColumns } from './UsersTable';
import { useRouter, useSearchParams } from 'next/navigation';

const LIMIT = 8;

export default function Users() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isAddUserModalOpen, setIsAddUserModalOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isRejectUserDialogOpen, setIsRejectUserDialogOpen] = useState(false);
  const [isAcceptUserDialogOpen, setIsAcceptUserDialogOpen] = useState(false);
  const [userToDelete, setUserToDelete] = useState<User | null>(null);
  const [view, setView] = useState<'active' | 'requests'>(() => {
    const viewParam = searchParams.get('view');
    return viewParam === 'requests' ? 'requests' : 'active';
  });
  const [search, setSearch] = useState<string>('');
  const [status, setStatus] = useState<string>('all');
  const [role, setRole] = useState<string>('all');
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: LIMIT,
  });

  // Debounce search
  const debouncedSearch = useDebounce(search, 300);

  // Mutations
  const deleteUserMutation = useDeleteUser();
  const updateUserStatusMutation = useUpdateUserStatus();

  // Queries
  const isActiveView = view === 'active';
  const {
    data: usersResponse,
    isLoading: loadingUsers,
    isFetching: refreshingUsers,
  } = useUsers(
    {
      page: pagination.pageIndex + 1,
      search: debouncedSearch || undefined,
      role: role !== 'all' ? role : undefined,
      status: isActiveView && status !== 'all' ? status : undefined,
      limit: LIMIT,
    },
    { enabled: isActiveView }
  );

  const {
    data: requestsResponse,
    isLoading: loadingRequests,
    isFetching: refreshingRequests,
  } = useUserRequests(
    {
      page: pagination.pageIndex + 1,
      search: debouncedSearch || undefined,
      role: role !== 'all' ? role : undefined,
      limit: LIMIT,
    },
    { enabled: !isActiveView }
  );

  // Get initial requests count for badge (only fetch once)
  const { data: initialRequestsResponse } = useUserRequests(
    { page: 1, limit: LIMIT },
    { enabled: true }
  );

  const userList = useMemo(() => usersResponse?.data ?? [], [usersResponse]);
  const totalItems = usersResponse?.meta.totalItems ?? 0;
  const requestsList = useMemo(
    () => requestsResponse?.data ?? [],
    [requestsResponse]
  );
  const requestsTotalItems = requestsResponse?.meta.totalItems ?? 0;
  const requestsCount = initialRequestsResponse?.meta.totalItems ?? 0;

  const loading = isActiveView ? loadingUsers : loadingRequests;
  const refreshing = isActiveView ? refreshingUsers : refreshingRequests;
  const confirmSubmitting =
    deleteUserMutation.isPending || updateUserStatusMutation.isPending;

  const handleEdit = useCallback(
    (userId: string) => () => {
      const user = userList.find(u => u.id === userId);
      if (user) {
        setSelectedUser(user);
        setIsEditModalOpen(true);
      }
    },
    [userList]
  );

  const handleDelete = useCallback(
    (userId: string) => () => {
      const user = userList.find(u => u.id === userId);
      if (user) {
        setUserToDelete(user);
        setIsDeleteDialogOpen(true);
      }
    },
    [userList]
  );

  const acceptUser = useCallback(
    (userId: string) => {
      const user = requestsList.find(u => u.id === userId);
      if (user) {
        setSelectedUser(user);
        setIsAcceptUserDialogOpen(true);
      }
    },
    [requestsList]
  );

  const rejectUser = useCallback(
    (userId: string) => {
      const user = requestsList.find(u => u.id === userId);
      if (user) {
        setSelectedUser(user);
        setIsRejectUserDialogOpen(true);
      }
    },
    [requestsList]
  );

  const handleConfirmDelete = async () => {
    if (userToDelete) {
      try {
        await deleteUserMutation.mutateAsync(userToDelete.id);
        setUserToDelete(null);
        setIsDeleteDialogOpen(false);
      } catch {
        // Error handling is done in the mutation
      }
    }
  };

  const handleRejectUser = async () => {
    if (selectedUser) {
      try {
        await updateUserStatusMutation.mutateAsync({
          id: selectedUser.id,
          type: 'reject',
        });
        setIsRejectUserDialogOpen(false);
        setSelectedUser(null);
      } catch {
        // Error handling is done in the mutation
      }
    }
  };

  const handleAcceptUser = async () => {
    if (selectedUser) {
      try {
        await updateUserStatusMutation.mutateAsync({
          id: selectedUser.id,
          type: 'accept',
        });
        setIsAcceptUserDialogOpen(false);
        setSelectedUser(null);
      } catch {
        // Error handling is done in the mutation
      }
    }
  };

  // Sync view to URL (?view=active|requests)
  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    if (view === 'active') {
      params.delete('view');
    } else {
      params.set('view', view);
    }
    const query = params.toString();
    router.replace(query ? `?${query}` : '?', { scroll: false });
  }, [view, router]);

  // Reset pagination when view, search, role, or status changes
  useEffect(() => {
    setPagination(prev => ({ ...prev, pageIndex: 0 }));
  }, [view, debouncedSearch, role, status]);

  const pageCount = useMemo(
    () =>
      Math.max(
        Math.ceil((isActiveView ? totalItems : requestsTotalItems) / LIMIT),
        1
      ),
    [isActiveView, totalItems, requestsTotalItems]
  );

  const columns = useUsersColumns({
    isActiveView,
    refreshing,
    handleEdit,
    handleDelete,
    rejectUser,
    acceptUser,
  });

  return (
    <>
      <h1 className="text-2xl font-bold">Users</h1>
      <div className="text-base">
        Invite new users, update roles, and review requests.
      </div>
      <UsersViewToggle
        view={view}
        requestsCount={requestsCount}
        onViewChange={setView}
      />
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <UsersFilters
          search={search}
          role={role}
          status={status}
          isActiveView={isActiveView}
          onSearchChange={setSearch}
          onRoleChange={setRole}
          onStatusChange={setStatus}
        />
        <Button
          onClick={() => setIsAddUserModalOpen(true)}
          variant="default"
          className="w-full sm:w-[12.8125rem] h-[2.625rem]"
        >
          <Plus /> Create New User
        </Button>
      </div>
      {loading ? (
        <Loading isActive={isActiveView} />
      ) : (
        <UsersTable
          data={isActiveView ? userList : requestsList}
          loading={loading}
          refreshing={refreshing}
          isActiveView={isActiveView}
          columns={columns}
          pagination={pagination}
          pageCount={pageCount}
          onPaginationChange={setPagination}
        />
      )}
      <DataTablePagination
        page={pagination.pageIndex + 1}
        totalPages={pageCount}
        onPageChange={nextPage =>
          setPagination(prev => ({ ...prev, pageIndex: nextPage - 1 }))
        }
      />

      <UserEditModal
        user={selectedUser}
        open={isEditModalOpen}
        onOpenChange={setIsEditModalOpen}
      />
      <AddUserModal
        open={isAddUserModalOpen}
        onOpenChange={setIsAddUserModalOpen}
      />
      <ConfirmDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
        title="Remove user?"
        description="This user will be removed from the platform and lose access immediately."
        confirmText="Remove"
        cancelText="Keep user"
        onConfirm={handleConfirmDelete}
        loading={confirmSubmitting}
      />
      <ConfirmDialog
        open={isRejectUserDialogOpen}
        onOpenChange={setIsRejectUserDialogOpen}
        title="Reject user invite?"
        description="This action will decline the request to join the platform. The user will not gain access unless a new invite is sent."
        confirmText="Reject invite"
        cancelText="Cancel"
        onConfirm={handleRejectUser}
        loading={confirmSubmitting}
      />
      <ConfirmDialog
        open={isAcceptUserDialogOpen}
        onOpenChange={setIsAcceptUserDialogOpen}
        title="Accept user invite?"
        description="This request will grant the user access to the platform. You can edit their role or remove access later if needed."
        confirmText="Accept invite"
        cancelText="Keep user"
        onConfirm={handleAcceptUser}
        loading={confirmSubmitting}
      />
    </>
  );
}
