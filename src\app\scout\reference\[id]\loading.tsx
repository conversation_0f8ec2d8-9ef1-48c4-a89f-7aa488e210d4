import { Skeleton } from '@/components/ui/skeleton';

export default function Loading() {
  return (
    <div className="w-full flex justify-center">
      <div className="space-y-6 xl:w-[70.5rem] p-6 xl:px-0">
        <div className="flex items-center gap-2">
          <Skeleton className="h-8 w-64" />
          <Skeleton className="h-6 w-6 rounded-full" />
        </div>
        <Skeleton className="h-5 w-96" />
        <div className="flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between">
          <div className="relative w-full lg:w-[22.4375rem]">
            <Skeleton className="h-10 w-full" />
          </div>
          <div className="flex gap-3 lg:flex-row lg:items-center lg:justify-end">
            <Skeleton className="h-10 w-[9.9375rem]" />
            <Skeleton className="h-10 w-[9.9375rem]" />
            <Skeleton className="h-10 w-[12.5625rem]" />
          </div>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.from({ length: 3 }).map((_, index) => (
            <div
              key={index}
              className="relative border rounded-lg shadow-sm bg-white flex flex-col"
            >
              <div className="relative bg-gray-200 flex items-center justify-center rounded-t-lg aspect-video">
                <Skeleton className="h-full w-full rounded-t-lg" />
              </div>
              <div className="p-4 flex-1 flex flex-col gap-1">
                <div className="flex justify-between items-center">
                  <Skeleton className="h-5 w-3/5" />
                </div>
                <Skeleton className="h-4 w-4/5" />
                <div className="mt-auto flex justify-start gap-1 pt-2">
                  <Skeleton className="h-6 w-16" />
                  <Skeleton className="h-6 w-20" />
                  <Skeleton className="h-6 w-12" />
                </div>
                <div className="mt-4 flex items-center justify-center gap-2">
                  <Skeleton className="h-9 w-[11rem]" />
                  <Skeleton className="h-9 w-[8.9375rem]" />
                </div>
              </div>
              {/* Comment count placeholder */}
              <Skeleton className="absolute left-0 bottom-6 ml-4 h-4 w-24" />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
