import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Card, CardContent } from '@/components/ui/card';
import { Heading } from '@/components/ui/typography';
import { getUserInitials } from '@/lib/utils';
import { Location } from '@/types';

export default function LocationCardInline({
  location,
}: {
  location: Location;
}) {
  return (
    <Card className="hover:shadow-lg transition-shadow py-0 mt-4 border-slate-200">
      <CardContent className="p-4 rounded-lg">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Avatar className="h-12 w-12 mr-3 rounded-lg">
              <AvatarImage
                src={location.coverImageUrl ?? undefined}
                alt="avatar"
              />
              <AvatarFallback className="text-xs rounded-lg bg-[#A3A3A3] text-white">
                {getUserInitials(location.title)}
              </AvatarFallback>
            </Avatar>
            <div className="">
              <Heading level={4} className="text-sm leading-5 line-clamp-2">
                {location.title}
              </Heading>
              <p className="text-sm font-medium text-[#737373]">
                {location.address}
              </p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
