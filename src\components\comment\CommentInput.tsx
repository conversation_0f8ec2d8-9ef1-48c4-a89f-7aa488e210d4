'use client';

import { useState } from 'react';
import { getUserInitials } from '@/lib/utils';
import { Avatar, AvatarFallback, AvatarImage } from '../ui/avatar';
import { Textarea } from '../ui/textarea';
import { Button } from '../ui/button';
import { User } from 'next-auth';
import { Loader2 } from 'lucide-react';

interface CommentInputProps {
  user: User | null;
  isCreating?: boolean;
  onComment: (comment: string) => void;
  onCancel?: () => void;
  placeholder?: string;
  showCancelButton?: boolean;
  showCloseButton?: boolean;
  onClose?: () => void;
}

export const CommentInput = ({
  user,
  isCreating = false,
  onComment,
  onCancel,
  placeholder = 'Add a comment...',
  showCancelButton = false,
  showCloseButton = false,
  onClose,
}: CommentInputProps) => {
  const [comment, setComment] = useState<string>('');

  const handleComment = () => {
    if (!comment.trim()) return;
    onComment(comment.trim());
    setComment('');
  };

  const handleCancel = () => {
    setComment('');
    onCancel?.();
  };

  return (
    <div className="flex flex-col gap-3 w-full">
      <div className="flex gap-3">
        <Avatar className="h-8 w-8">
          <AvatarImage src={user?.avatar ?? ''} alt="avatar" />
          <AvatarFallback className="text-xs bg-primary-50 text-primary-300">
            {getUserInitials(
              `${user?.firstName ?? ''} ${user?.lastName ?? ''}` || 'U'
            )}
          </AvatarFallback>
        </Avatar>
        <Textarea
          id="comment"
          value={comment}
          onChange={e => setComment(e.target.value)}
          placeholder={placeholder}
          className="w-full min-h-[100px] flex-1"
        />
      </div>
      <div className="flex gap-4 items-center justify-end">
        {showCancelButton && onCancel && (
          <Button
            onClick={handleCancel}
            variant="ghost"
            className="text-primary-300"
            size="lg"
          >
            Close
          </Button>
        )}
        <div className="flex gap-2">
          {showCloseButton && onClose && (
            <Button variant="link" onClick={onClose}>
              Close
            </Button>
          )}
          <Button
            onClick={handleComment}
            disabled={!comment || isCreating}
            size="lg"
          >
            Post
            {isCreating && <Loader2 className="w-4 h-4 animate-spin ml-2" />}
          </Button>
        </div>
      </div>
    </div>
  );
};
