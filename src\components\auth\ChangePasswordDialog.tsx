'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Key, Eye, EyeOff } from 'lucide-react';
import { authService } from '@/lib/services/auth-service';
import { toastError, toastSuccess } from '@/lib/toast';
import { ApiError } from '@/lib/api-client';

interface ChangePasswordDialogProps {
  trigger?: React.ReactNode;
  onSuccess?: () => void;
}

interface PasswordData {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

interface ValidationErrors {
  currentPassword?: string;
  newPassword?: string;
  confirmPassword?: string;
}

export function ChangePasswordDialog({
  trigger,
  onSuccess,
}: ChangePasswordDialogProps) {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [passwordData, setPasswordData] = useState<PasswordData>({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });
  const [errors, setErrors] = useState<ValidationErrors>({});
  const [hasAttemptedSubmit, setHasAttemptedSubmit] = useState(false);
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  // Validation function with detailed password requirements
  const validateField = (
    field: keyof PasswordData,
    value: string,
    data: PasswordData = passwordData
  ): string | undefined => {
    switch (field) {
      case 'currentPassword':
        if (!value) return 'Current password is required';
        break;
      case 'newPassword':
        if (!value) return 'New password is required';
        if (value.length < 8) return 'Password must be at least 8 characters';
        if (!/[A-Z]/.test(value))
          return 'Password must contain at least one uppercase letter';
        if (!/[a-z]/.test(value))
          return 'Password must contain at least one lowercase letter';
        if (!/\d/.test(value))
          return 'Password must contain at least one number';
        if (!/[!@#$%^&*(),.?":{}|<>]/.test(value))
          return 'Password must contain at least one special character';
        if (value === data.currentPassword)
          return 'New password must be different from current password';
        break;
      case 'confirmPassword':
        if (!value) return 'Please confirm your new password';
        if (value !== data.newPassword) return 'Passwords do not match';
        break;
    }
    return undefined;
  };

  // Validate all fields
  const validateForm = (): boolean => {
    const newErrors: ValidationErrors = {};
    let isValid = true;

    Object.keys(passwordData).forEach(key => {
      const field = key as keyof PasswordData;
      const error = validateField(field, passwordData[field]);
      if (error) {
        newErrors[field] = error;
        isValid = false;
      }
    });

    setErrors(newErrors);
    return isValid;
  };

  // Handle field change with real-time validation
  const handleFieldChange = (field: keyof PasswordData, value: string) => {
    const updatedData = { ...passwordData, [field]: value };
    setPasswordData(updatedData);

    // Validate the changed field in real-time
    // Skip validation for currentPassword unless submit was attempted
    if (field === 'currentPassword' && !hasAttemptedSubmit) {
      // Clear error for currentPassword if submit hasn't been attempted
      setErrors(prev => ({ ...prev, currentPassword: undefined }));
    } else {
      const error = validateField(field, value, updatedData);
      setErrors(prev => ({ ...prev, [field]: error }));
    }

    // If newPassword changed, re-validate confirmPassword
    if (field === 'newPassword' && updatedData.confirmPassword) {
      const confirmError = validateField(
        'confirmPassword',
        updatedData.confirmPassword,
        updatedData
      );
      setErrors(prev => ({ ...prev, confirmPassword: confirmError }));
    }

    // If currentPassword changed, re-validate newPassword (for "different from current" check)
    if (field === 'currentPassword' && updatedData.newPassword) {
      const newPasswordError = validateField(
        'newPassword',
        updatedData.newPassword,
        updatedData
      );
      setErrors(prev => ({ ...prev, newPassword: newPasswordError }));
    }
  };

  const handleSubmit = async () => {
    setHasAttemptedSubmit(true);
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      await authService.changePassword({
        currentPassword: passwordData.currentPassword,
        newPassword: passwordData.newPassword,
      });

      toastSuccess('Password changed successfully!');
      setOpen(false);
      setPasswordData({
        currentPassword: '',
        newPassword: '',
        confirmPassword: '',
      });
      setErrors({});
      setHasAttemptedSubmit(false);
      setShowCurrentPassword(false);
      setShowNewPassword(false);
      setShowConfirmPassword(false);

      onSuccess?.();
    } catch (error) {
      console.error('Error changing password:', error);
      toastError(
        (error as ApiError).response?.data?.message ??
          'Failed to change password'
      );
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    setOpen(false);
    setPasswordData({
      currentPassword: '',
      newPassword: '',
      confirmPassword: '',
    });
    setErrors({});
    setHasAttemptedSubmit(false);
    setShowCurrentPassword(false);
    setShowNewPassword(false);
    setShowConfirmPassword(false);
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="outline" className="flex items-center space-x-2">
            <Key className="h-4 w-4" />
            <span>Change Password</span>
          </Button>
        )}
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Change Password</DialogTitle>
          <DialogDescription>
            Enter your current password and choose a new one.
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="current-password">Current Password</Label>
            <div className="relative">
              <Input
                id="current-password"
                type={showCurrentPassword ? 'text' : 'password'}
                value={passwordData.currentPassword}
                onChange={e =>
                  handleFieldChange('currentPassword', e.target.value)
                }
                placeholder="Enter your current password"
                className={`pr-10 ${
                  hasAttemptedSubmit && errors.currentPassword
                    ? 'border-red-500'
                    : ''
                }`}
              />
              <button
                type="button"
                onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700 focus:outline-none"
                disabled={loading}
              >
                {showCurrentPassword ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </button>
            </div>
            {hasAttemptedSubmit && errors.currentPassword && (
              <p className="text-sm text-red-500">{errors.currentPassword}</p>
            )}
          </div>
          <div className="space-y-2">
            <Label htmlFor="new-password">New Password</Label>
            <div className="relative">
              <Input
                id="new-password"
                type={showNewPassword ? 'text' : 'password'}
                value={passwordData.newPassword}
                onChange={e => handleFieldChange('newPassword', e.target.value)}
                placeholder="Enter your new password"
                className={`pr-10 ${
                  errors.newPassword ? 'border-red-500' : ''
                }`}
              />
              <button
                type="button"
                onClick={() => setShowNewPassword(!showNewPassword)}
                className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700 focus:outline-none"
                disabled={loading}
              >
                {showNewPassword ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </button>
            </div>
            {errors.newPassword && (
              <p className="text-sm text-red-500">{errors.newPassword}</p>
            )}
          </div>
          <div className="space-y-2">
            <Label htmlFor="confirm-password">Confirm New Password</Label>
            <div className="relative">
              <Input
                id="confirm-password"
                type={showConfirmPassword ? 'text' : 'password'}
                value={passwordData.confirmPassword}
                onChange={e =>
                  handleFieldChange('confirmPassword', e.target.value)
                }
                placeholder="Confirm your new password"
                className={`pr-10 ${
                  errors.confirmPassword ? 'border-red-500' : ''
                }`}
              />
              <button
                type="button"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700 focus:outline-none"
                disabled={loading}
              >
                {showConfirmPassword ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </button>
            </div>
            {errors.confirmPassword && (
              <p className="text-sm text-red-500">{errors.confirmPassword}</p>
            )}
          </div>
          <div className="flex space-x-3 pt-4">
            <Button
              onClick={handleSubmit}
              disabled={loading}
              className="flex-1"
            >
              {loading ? 'Changing...' : 'Change Password'}
            </Button>
            <Button variant="outline" onClick={handleCancel} disabled={loading}>
              Cancel
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
