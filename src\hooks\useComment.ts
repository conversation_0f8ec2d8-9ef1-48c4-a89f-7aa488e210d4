import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useCommentAPI } from '@/hooks/useCommentAPI';
import { Comment } from '@/types';

export function useComment(
  open: boolean,
  referenceListId?: string,
  referenceItemId?: string
) {
  const {
    getReferenceItemComments,
    createReferenceItemComment,
    deleteReferenceItemComment,
    updateReferenceItemComment,
  } = useCommentAPI();
  const queryClient = useQueryClient();

  // Query key for comments
  const queryKey = ['comments', referenceListId, referenceItemId] as const;

  // Fetch comments query
  const {
    data: comments = [],
    isLoading,
    error: fetchError,
    refetch,
  } = useQuery<Comment[]>({
    queryKey,
    queryFn: async () => {
      if (!referenceListId || !referenceItemId) {
        return [];
      }
      return getReferenceItemComments(referenceListId, referenceItemId);
    },
    enabled: open && !!referenceListId && !!referenceItemId,
    staleTime: 1000 * 10, // 10 seconds
    gcTime: 1000 * 60 * 5, // 5 minutes
    placeholderData: () => queryClient.getQueryData<Comment[]>(queryKey) ?? [],
  });

  // Create comment mutation
  const {
    mutate: createComment,
    mutateAsync: createCommentAsync,
    isPending: isCreating,
    error: createError,
  } = useMutation<Comment, Error, string>({
    mutationFn: async (content: string) => {
      if (!referenceListId || !referenceItemId) {
        throw new Error('Reference list ID and item ID are required');
      }
      return createReferenceItemComment(
        referenceListId,
        referenceItemId,
        content
      );
    },
    onSuccess: (newComment: Comment) => {
      // Optimistically update the cache
      queryClient.setQueryData<Comment[]>(queryKey, (old = []) => [
        ...old,
        newComment,
      ]);

      // Invalidate to refetch and ensure consistency
      queryClient.invalidateQueries({ queryKey });
    },
    onError: (error: Error) => {
      // Refetch on error to ensure we have the latest data
      queryClient.invalidateQueries({ queryKey });
      // Error will be handled by the error boundary or toast in the component
      console.error('Error creating comment:', error);
    },
  });

  // Delete comment mutation

  const {
    mutate: deleteComment,
    mutateAsync: deleteCommentAsync,
    isPending: isDeleting,
    error: deleteError,
  } = useMutation<void, Error, string>({
    mutationFn: async (commentId: string) => {
      if (!referenceListId || !referenceItemId) {
        throw new Error('Reference list ID and item ID are required');
      }
      return deleteReferenceItemComment(
        referenceListId,
        referenceItemId,
        commentId
      );
    },
    onSuccess: (_: unknown, commentId: string) => {
      if (!referenceListId || !referenceItemId || !commentId) {
        throw new Error(
          'Reference list ID, item ID, and comment ID are required'
        );
      }
      // Optimistically update the cache
      queryClient.setQueryData<Comment[]>(queryKey, (old = []) =>
        old.filter(c => c.id !== commentId)
      );

      // Invalidate to refetch and ensure consistency
      queryClient.invalidateQueries({ queryKey });
    },
    onError: () => {
      // Refetch on error to ensure we have the latest data
      queryClient.invalidateQueries({ queryKey });
    },
  });

  // Update comment mutation
  const {
    mutate: updateComment,
    mutateAsync: updateCommentAsync,
    isPending: isUpdating,
    error: updateError,
  } = useMutation<Comment, Error, { commentId: string; content: string }>({
    mutationFn: async ({
      commentId,
      content,
    }: {
      commentId: string;
      content: string;
    }) => {
      if (
        !referenceListId ||
        !referenceItemId ||
        !commentId ||
        !content.trim()
      ) {
        throw new Error(
          'Reference list ID, item ID, comment ID, and content are required'
        );
      }
      return updateReferenceItemComment(
        referenceListId,
        referenceItemId,
        commentId,
        content.trim()
      );
    },
    onSuccess: (updatedComment: Comment) => {
      queryClient.setQueryData<Comment[]>(queryKey, (old = []) =>
        old.map(c => (c.id === updatedComment.id ? updatedComment : c))
      );
      queryClient.invalidateQueries({ queryKey });
    },
    onError: () => {
      queryClient.invalidateQueries({ queryKey });
    },
  });

  return {
    comments,
    isLoading,
    error: createError || deleteError || fetchError,
    refetch,
    createComment,
    createCommentAsync,
    deleteComment,
    deleteCommentAsync,
    isCreating,
    isDeleting,
    updateComment,
    updateCommentAsync,
    isUpdating,
    updateError,
  };
}
