'use client';
import { useCallback, useEffect, useMemo, useState } from 'react';
import {
  LocationFilters,
  useSearchLocationFilters,
  defaultFilters,
} from './search-location-context';
import isEqual from 'lodash/isEqual';
import LocationsGridView from './locations-grid-view';
import { Location } from '@/types';
import { Button } from '@/components/ui/button';
import LocationsMapView from './location-map-view';
import { useNavbar } from '@/contexts/navbar-context';
import { useSearchLocationPanel } from './search-location-context';
import { ChevronDown } from 'lucide-react';
import { useParams, useRouter } from 'next/navigation';
import { LocationMarkerIcon } from '@/lib/icons';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent } from '@/components/ui/tabs';
import { toastError, toastSuccess } from '@/lib/toast';
import { useLocationService, useReferenceService } from '@/hooks/use-services';
import { useReference } from '../reference-context';

const LIMIT = 6;
export default function SearchLocationsPage() {
  const locationService = useLocationService();
  const referenceService = useReferenceService();
  const { filters } = useSearchLocationFilters();
  const { setAdditionalItems, clearNavbarItems } = useNavbar();
  const { togglePanel } = useSearchLocationPanel();
  const params = useParams();
  const router = useRouter();
  const [locations, setLocations] = useState<Location[]>([]);
  const [page, setPage] = useState<number>(1);
  const [totalPage, setTotalPage] = useState<number>(0);
  const [totalItems, setTotalItems] = useState<number>(0);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [loading, setLoading] = useState<boolean>(true);
  const [loadingMore, setLoadingMore] = useState<boolean>(false);
  const [view, setView] = useState<'grid' | 'map'>('grid');
  const { reference, refreshReference } = useReference();

  useEffect(() => {
    if (reference) {
      document.title = `${reference.projectName} | Locations | Scoutr`;
    }
  }, [reference]);

  const hasFilters = useMemo(() => {
    return !isEqual(defaultFilters, filters);
  }, [filters]);

  const fetchLocations = useCallback(
    async (page: number, filters: LocationFilters) => {
      setLocations([]);

      // Do not fetch locations if the filters are empty
      if (!hasFilters) {
        return;
      }
      try {
        if (page === 1) setLoading(true);
        else setLoadingMore(true);

        const { query, where, category, subCategories, style, size } = filters;
        const sizeParam = (['small', 'medium', 'large'] as const).find(
          key => size[key]
        );
        const tagIds = [category, ...subCategories, ...style]
          .filter(Boolean)
          .join(',');
        const { country, city, state } = where;
        const response = await locationService.getLocations(
          page,
          query,
          undefined,
          tagIds,
          Object.keys(where).length
            ? {
                country: country,
                city: city,
                state: state,
              }
            : undefined,
          LIMIT,
          sizeParam
        );
        setLocations(prev =>
          page === 1 ? response.data : [...prev, ...response.data]
        );
        setTotalPage(response.meta.totalPages);
        setTotalItems(response.meta.totalItems);
        setHasMore(response.meta.currentPage < response.meta.totalPages);
      } catch {
        toastError('Failed to get locations');
      } finally {
        setLoading(false);
        setLoadingMore(false);
      }
    },
    [hasFilters, locationService]
  );

  const fetchMoreData = useCallback(() => {
    if (page >= totalPage || loadingMore) return;
    const nextPage = page + 1;
    setPage(nextPage);
  }, [page, totalPage, loadingMore]);

  const handleAddLocation = async (data: Location) => {
    try {
      let searchTagIds: string[] = [];
      if (filters.category) {
        searchTagIds = [filters.category];
      }
      if ([...filters.subCategories, ...filters.style].length) {
        searchTagIds = [
          ...searchTagIds,
          ...filters.subCategories,
          ...filters.style,
        ];
      }
      await referenceService.quickAddReferenceItems(params.id as string, {
        locationId: data.id,
        searchTagIds,
      });
      toastSuccess('Location added successfully.');
      // Refresh reference data in context
      await refreshReference();
    } catch {
      toastError('Failed to quick add location');
    }
  };

  useEffect(() => {
    // If we're already on page 1, force a refetch; otherwise resetting
    // to page 1 will trigger the fetch via the [page] effect below.
    if (page === 1) {
      fetchLocations(1, filters);
    } else {
      setPage(1);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [filters]);

  useEffect(() => {
    fetchLocations(page, filters);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [page]);

  // Set navbar items for this page
  useEffect(() => {
    // const referenceId = params.id as string;

    setAdditionalItems([
      {
        id: 'all-lists',
        label: 'All Lists',
        icon: <ChevronDown className="h-4 w-4" />,
        onClick: () => {
          router.push(`/scout/reference/${params.id}`);
        },
        variant: 'ghost',
        align: 'left',
        iconPosition: 'right',
      },
      {
        id: 'reference-list',
        label: 'Reference List',
        icon: <LocationMarkerIcon className="h-6 w-6 text-black size-6" />,
        onClick: () => {
          togglePanel();
        },
        variant: 'outline',
        align: 'right',
        badge: (
          <Badge className="bg-badge-secondary text-black">
            {reference?.items.length ?? 0}
          </Badge>
        ),
        className: 'text-foreground gap-4',
      },
    ]);

    // Cleanup navbar items when component unmounts
    return () => {
      clearNavbarItems();
    };
  }, [
    params.id,
    setAdditionalItems,
    clearNavbarItems,
    router,
    togglePanel,
    reference?.items.length,
  ]);

  return (
    <div className="space-y-2">
      {!hasFilters ? (
        <div className="w-full flex h-[calc(100vh-10.5rem)] items-center justify-center">
          <div className="space-y-12 flex flex-col items-center">
            <div>
              <LocationMarkerIcon className="h-40 w-40 text-black size-40" />
            </div>
            <div className="w-[27.5625rem] text-center">
              <h1 className="text-2xl font-bold mb-2">
                Start looking for locations
              </h1>
              <span className="text-base">
                Enter what you’re looking for, where, and when.
              </span>
            </div>
          </div>
        </div>
      ) : (
        <>
          <div className="mb-10 flex justify-between items-end">
            <div>
              <h1 className="text-2xl font-bold mb-2">Results</h1>
              {locations.length ? (
                <span className="text-base">
                  {`Showing ${totalItems} available locations.`}
                </span>
              ) : (
                <span className="text-base">
                  Showing 0 available locations.
                </span>
              )}
            </div>
            <div className="mt-6 flex items-center gap-2">
              <div className="inline-flex rounded-md p-1 bg-secondary gap-1">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setView('grid')}
                  className={`
                    w-10 h-6 hover:bg-white
                    ${
                      view === 'map'
                        ? 'border-0 shadow-none text-[#64748B] '
                        : 'bg-white'
                    }`}
                >
                  Grid
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setView('map')}
                  className={`
                    w-10 h-6 hover:bg-white
                    ${
                      view === 'grid'
                        ? 'border-0 shadow-none text-[#64748B] '
                        : 'bg-white'
                    }`}
                >
                  Map
                </Button>
              </div>
            </div>
          </div>
          {locations.length === 0 && !loading && (
            <div className="w-full flex h-[calc(100vh-17.5rem)] items-center justify-center">
              <div className="space-y-12 flex flex-col items-center">
                <div>
                  <LocationMarkerIcon className="h-40 w-40 text-black size-40" />
                </div>
                <div className="w-[27.5625rem] text-center">
                  <h1 className="text-2xl font-bold mb-2">
                    Oops! No locations found
                  </h1>
                  <span className="text-base">
                    Try a different name or adjust your filters to find what
                    you’re looking for.
                  </span>
                </div>
              </div>
            </div>
          )}
          <Tabs value={view}>
            <TabsContent value={'grid'}>
              <LocationsGridView
                locations={locations}
                fetchMoreData={fetchMoreData}
                hasMore={hasMore}
                onAddLocation={handleAddLocation}
                referenceId={params.id as string}
                loading={loading}
              />
            </TabsContent>
            <TabsContent value={'map'}>
              <LocationsMapView
                locations={locations}
                referenceId={params.id as string}
                onAddLocation={handleAddLocation}
              />
            </TabsContent>
          </Tabs>
          {/* {locations.length && isGridView && (
            <LocationsGridView
              locations={locations}
              fetchMoreData={fetchMoreData}
              hasMore={hasMore}
              onAddLocation={handleAddLocation}
            />
          )}
          {locations.length && !isGridView && <LocationsMapView locations={locations} />} */}
        </>
      )}
    </div>
  );
}
