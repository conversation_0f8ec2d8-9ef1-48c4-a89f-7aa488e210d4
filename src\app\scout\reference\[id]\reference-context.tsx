'use client';

import React, {
  createContext,
  useContext,
  useState,
  useCallback,
  useMemo,
  ReactNode,
  useEffect,
} from 'react';
import { useReferenceService } from '@/hooks/use-services';
import { toastError } from '@/lib/toast';
import { Reference } from '@/types/reference';

interface ReferenceContextType {
  // Reference data
  reference: Reference | undefined;
  isLoading: boolean;

  // Actions
  fetchReference: (search?: string) => Promise<void>;
  updateReference: (
    updater:
      | Reference
      | ((prev: Reference | undefined) => Reference | undefined)
  ) => void;
  refreshReference: (search?: string) => Promise<void>;
}

const ReferenceContext = createContext<ReferenceContextType | undefined>(
  undefined
);

interface ReferenceProviderProps {
  children: ReactNode;
  referenceId: string;
}

export function ReferenceProvider({
  children,
  referenceId,
}: ReferenceProviderProps) {
  const referenceService = useReferenceService();
  const [reference, setReference] = useState<Reference | undefined>();
  const [isLoading, setIsLoading] = useState(true);

  const fetchReference = useCallback(
    async (search?: string) => {
      try {
        setIsLoading(true);
        const response = await referenceService.getReference(
          referenceId,
          search
        );
        setReference(response);
        document.title = `${response.projectName} | Scoutr`;
      } catch {
        toastError('Failed to get reference details');
      } finally {
        setIsLoading(false);
      }
    },
    [referenceId, referenceService]
  );

  const updateReference = useCallback(
    (
      updater:
        | Reference
        | ((prev: Reference | undefined) => Reference | undefined)
    ) => {
      setReference(prev =>
        typeof updater === 'function' ? updater(prev) : updater
      );
    },
    []
  );

  const refreshReference = useCallback(
    async (search?: string) => {
      await fetchReference(search);
    },
    [fetchReference]
  );

  // Initial fetch
  useEffect(() => {
    if (!referenceId) return;
    fetchReference();
  }, [fetchReference, referenceId]);

  const value = useMemo<ReferenceContextType>(
    () => ({
      reference,
      isLoading,
      fetchReference,
      updateReference,
      refreshReference,
    }),
    [reference, isLoading, fetchReference, updateReference, refreshReference]
  );

  return (
    <ReferenceContext.Provider value={value}>
      {children}
    </ReferenceContext.Provider>
  );
}

export function useReference() {
  const context = useContext(ReferenceContext);
  if (context === undefined) {
    throw new Error('useReference must be used within a ReferenceProvider');
  }
  return context;
}
