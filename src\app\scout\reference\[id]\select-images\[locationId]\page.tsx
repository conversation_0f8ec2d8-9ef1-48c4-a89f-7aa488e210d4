'use client';
import { Photo } from '@/types/location';
import AllPhotoGrid from '@/components/AllPhotoGrid';
import { ArrowLeft } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { use, useCallback, useEffect, useState } from 'react';
import { Heading } from '@/components/ui/typography';
import { Button } from '@/components/ui/button';
import ConfirmDialog from '@/components/shared/ConfirmDialog';
import Loading from './loading';
import { AddCommentModal } from '@/components/comment/AddCommentModal';
import { toastError, toastSuccess } from '@/lib/toast';
import { useLocationService } from '@/hooks/use-services';
import { referenceService } from '@/lib/services';
import { useReference } from '../../reference-context';

export default function SelectImages({
  params,
}: {
  params: Promise<{ id: string; locationId: string }>;
}) {
  const router = useRouter();
  const locationService = useLocationService();
  const { id, locationId } = use(params);
  const { refreshReference } = useReference();
  const [images, setImages] = useState<Photo[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [discardDialogOpen, setDiscardDialogOpen] = useState<boolean>(false);
  const [imagesSelected, setImagesSelected] = useState<Photo[]>([]);
  const [addCommentDialogOpen, setAddCommentDialogOpen] =
    useState<boolean>(false);
  const [isAddingComment, setIsAddingComment] = useState<boolean>(false);

  const fetchAllImages = useCallback(async () => {
    try {
      setLoading(true);
      const response = await locationService.getLocationById(locationId);
      setImages(response.images);
    } catch {
      toastError('Failed to get all photos');
    } finally {
      setLoading(false);
    }
  }, [locationId, locationService]);

  const handleSelectImage = (image: Photo) => {
    setImagesSelected(prev => {
      const isExist = prev.find(p => p.id === image.id);
      return isExist ? prev.filter(p => p.id !== image.id) : [...prev, image];
    });
  };
  const handleConfirm = () => {
    setAddCommentDialogOpen(true);
  };

  const handleAddReference = async (comment: string) => {
    try {
      const imageIds = imagesSelected.map(img => img.id ?? '');
      await referenceService.addReferenceItems(id, {
        locationId,
        imageIds,
        comment,
      });
      setIsAddingComment(true);
      toastSuccess('Location added successfully.');
      // Refresh reference data in context
      await refreshReference();
      router.push(`/scout/reference/${id}/search-locations`);
    } catch {
      toastError('Failed to add reference');
    } finally {
      setIsAddingComment(false);
    }
  };

  useEffect(() => {
    if (!locationId) return;
    fetchAllImages();
  }, [fetchAllImages, locationId]);

  return (
    <>
      {loading && <Loading />}
      {!loading && (
        <div className="w-full flex justify-center">
          <div className="space-y-11 w-full xl:w-[70.5rem] p-11 xl:px-0">
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-4">
              <div
                onClick={() => {
                  router.push(
                    `/scout/reference/${id}/view-details/${locationId}`
                  );
                }}
                className="cursor-pointer inline-flex items-center gap-3 text-sm text-gray-600 hover:text-gray-900 transition-colors"
              >
                <ArrowLeft className="h-5 w-5" />
                <div className="flex flex-col justify-start">
                  <Heading level={3}>Select Images</Heading>
                  <p>Downtown Coffee Shop Scene</p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <p className="p-4">Change list</p>
                <Button
                  onClick={() => {
                    if (imagesSelected.length) {
                      setDiscardDialogOpen(true);
                    } else
                      router.push(
                        `/scout/reference/${id}/view-details/${locationId}`
                      );
                  }}
                  variant="outline"
                  className="w-full sm:w-fit h-10"
                >
                  Cancel
                </Button>
                <Button
                  disabled={!imagesSelected.length}
                  onClick={handleConfirm}
                  className="w-full sm:w-fit h-10"
                >
                  Confirm
                </Button>
              </div>
            </div>
            <AllPhotoGrid
              images={images}
              onClickImage={handleSelectImage}
              imagesSelected={imagesSelected}
            />
          </div>
        </div>
      )}
      <ConfirmDialog
        open={discardDialogOpen}
        onOpenChange={setDiscardDialogOpen}
        title="Discard this reference?"
        description="You’ve selected some images. If you cancel now, your changes will be lost."
        confirmText="Discard"
        cancelText="Keep editing"
        onConfirm={() =>
          router.push(`/scout/reference/${id}/view-details/${locationId}`)
        }
        onCancel={() => setDiscardDialogOpen(false)}
      />
      <AddCommentModal
        open={addCommentDialogOpen}
        onOpenChange={setAddCommentDialogOpen}
        onFinish={handleAddReference}
        loading={isAddingComment}
      />
    </>
  );
}
