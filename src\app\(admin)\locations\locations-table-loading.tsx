'use client';

import { Skeleton } from '@/components/ui/skeleton';
import { TableCell, TableRow } from '@/components/ui/table';

export default function LocationsTableLoading() {
  return (
    <>
      {Array.from({ length: 6 }).map((_, i) => (
        <TableRow key={`s-${i}`}>
          <TableCell className="px-6">
            <div className="flex items-center gap-4">
              <Skeleton className="h-12 w-16 rounded-lg" />
              <Skeleton className="h-4 w-32" />
            </div>
          </TableCell>
          <TableCell className="px-6">
            <Skeleton className="h-4 w-48" />
          </TableCell>
          <TableCell className="px-6">
            <div className="flex gap-2">
              <Skeleton className="h-4 w-20" />
              <Skeleton className="h-4 w-20" />
            </div>
          </TableCell>
          <TableCell className="px-6">
            <Skeleton className="h-4 w-48" />
          </TableCell>
        </TableRow>
      ))}
    </>
  );
}
