'use client';

import { Sidebar, SidebarContent } from '@/components/ui/sidebar';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Search, MapPin } from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';
import { useSearchLocationFilters } from './search-location-context';
import MultiSelect from '@/components/Select/MultiSelect';
import LocationPicker from '@/components/features/locations/LocationPicker';
import Image from 'next/image';
import { toastError } from '@/lib/toast';
import { TagOption } from '@/types';
import { useTagService } from '@/hooks/use-services';
import { DragScrollContainer } from '@/components/DragScrollContainer';
import { Chip } from '@/components/ui/chip';

export default function SidebarFilter() {
  const tagService = useTagService();
  const { filters, setFilters } = useSearchLocationFilters();
  const [category, setCategory] = useState<string | null>(filters.category);
  const [categoryOption, setCategoryOption] = useState<TagOption[]>([]);
  const [subCategoryOption, setSubCategoryOption] = useState<TagOption[]>([]);
  const [styleOption, setStyleOption] = useState<TagOption[]>([]);

  const fetchCategories = useCallback(async () => {
    try {
      const categories = await tagService.getAllTags({
        type: 'category',
      });
      setCategoryOption(
        categories.map(category => ({
          value: category.id,
          label: category.name,
          color: category.color,
        }))
      );
    } catch {
      toastError('Failed to get tag categories');
    }
  }, [tagService]);

  const fetchSubCategories = useCallback(async () => {
    try {
      const subCategories = await tagService.getAllTags({
        type: 'sub_category',
      });
      setSubCategoryOption(
        subCategories.map(subCategory => ({
          value: subCategory.id,
          label: subCategory.name,
          color: subCategory.color,
        }))
      );
    } catch {
      toastError('Failed to get tag sub-categories');
    }
  }, [tagService]);

  const fetchStyles = useCallback(async () => {
    try {
      const styles = await tagService.getAllTags({ type: 'style' });
      setStyleOption(
        styles.map(style => ({
          value: style.id,
          label: style.name,
          color: style.color,
        }))
      );
    } catch {
      toastError('Failed to get tag styles');
    }
  }, [tagService]);

  useEffect(() => {
    fetchCategories();
    fetchSubCategories();
    fetchStyles();
  }, [fetchCategories, fetchStyles, fetchSubCategories]);

  const handleAddressChange = (
    address?: string,
    latitude?: number,
    longitude?: number,
    placeId?: string,
    city?: string,
    state?: string,
    country?: string,
    postalCode?: string
  ) => {
    setFilters(prev => ({
      ...prev,
      where: {
        address,
        latitude: String(latitude),
        longitude: String(longitude),
        placeId,
        city,
        state,
        country,
        postalCode,
      },
    }));
  };

  return (
    <Sidebar
      className="border-r border-sidebar-border sticky top-0 h-full"
      style={{ '--sidebar-width': '22.5rem' } as React.CSSProperties}
    >
      <SidebarContent className="h-full p-4 space-y-6">
        <div className="space-y-2">
          <Label className="text-sm">What are you looking for?</Label>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="e.g. 'Warehouse', 'Coffee shop'"
              className="pl-9"
              value={filters.query}
              debounceMs={300}
              onChange={e =>
                setFilters(prev => ({ ...prev, query: e.target.value }))
              }
            />
          </div>
        </div>

        <div className="space-y-2">
          <Label className="text-sm">Where?</Label>
          <span className="text-xs text-muted-foreground">
            Enter city, state or area.
          </span>
          <div className="relative mt-1">
            <LocationPicker
              defaultAddress={{
                address: filters.where.address,
                lat: Number(filters.where.latitude),
                lng: Number(filters.where.longitude),
              }}
              icon={MapPin}
              onChange={handleAddressChange}
              fullAddressMode={false}
              placeholder="e.g. Los Angeles, CA"
            />
          </div>
        </div>

        <div className="space-y-3">
          <DragScrollContainer className="flex gap-2 w-full">
            {categoryOption.slice(0, 3).map(item => {
              return (
                <Chip
                  onClick={() => {
                    const nextCategory =
                      category === item.value ? '' : item.value;
                    setCategory(nextCategory);
                    setFilters(prev => ({
                      ...prev,
                      category: nextCategory,
                    }));
                  }}
                  selected={item.value === category}
                  key={item.value}
                  className="cursor-pointer w-[6.1602rem] h-[6.0625rem]"
                >
                  <div className="flex flex-col gap-1 items-center justify-center">
                    <Image
                      src="/assets/circle-placeholder.svg"
                      alt="Circle Placeholder"
                      width={42}
                      height={42}
                    />
                    <span className="truncate max-w-[5rem]">{item.label}</span>
                  </div>
                </Chip>
              );
            })}
          </DragScrollContainer>
        </div>

        <div className="space-y-2">
          <Label className="text-sm">Sub-category</Label>
          <MultiSelect
            options={subCategoryOption}
            value={filters.subCategories}
            onChange={vals =>
              setFilters(prev => ({ ...prev, subCategories: vals }))
            }
            placeholder="e.g. ‘Apartment’, ‘Restaurant’"
          />
        </div>

        <div className="space-y-2">
          <Label className="text-sm">Location style</Label>
          <MultiSelect
            options={styleOption}
            value={filters.style}
            onChange={vals => setFilters(prev => ({ ...prev, style: vals }))}
            placeholder="e.g. ‘Modern’, ‘Rustic’"
          />
        </div>

        <div className="space-y-3">
          <Label className="text-sm">Size</Label>
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="size-small"
                checked={filters.size.small}
                onCheckedChange={v =>
                  setFilters(prev => ({
                    ...prev,
                    size: { ...prev.size, small: Boolean(v) },
                  }))
                }
              />
              <Label htmlFor="size-small" className="text-sm">
                Small (&lt; 1,000 sq ft)
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="size-medium"
                checked={filters.size.medium}
                onCheckedChange={v =>
                  setFilters(prev => ({
                    ...prev,
                    size: { ...prev.size, medium: Boolean(v) },
                  }))
                }
              />
              <Label htmlFor="size-medium" className="text-sm">
                Medium (1,000–5,000 sq ft)
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="size-large"
                checked={filters.size.large}
                onCheckedChange={v =>
                  setFilters(prev => ({
                    ...prev,
                    size: { ...prev.size, large: Boolean(v) },
                  }))
                }
              />
              <Label htmlFor="size-large" className="text-sm">
                Large (&gt; 5,000 sq ft)
              </Label>
            </div>
          </div>
        </div>
      </SidebarContent>
    </Sidebar>
  );
}
