'use client';

import InfiniteScroll from 'react-infinite-scroll-component';
import { Location } from '@/types';
import LocationCard from '@/components/features/locations/LocationCard';
import { ProjectSkeletonGrid } from './ProjectSkeleton';

interface PropertyOwnerLocationGridProps {
  locations: Location[];
  isLoading: boolean;
  isLoadingMore: boolean;
  hasMore: boolean;
  onLoadMore: () => void;
  onEdit?: (location: Location) => void;
  onDelete?: (location: Location) => void;
  onView?: (location: Location) => void;
}

export function PropertyOwnerLocationGrid({
  locations,
  isLoading,
  isLoadingMore,
  hasMore,
  onLoadMore,
  onEdit,
  onDelete,
  onView,
}: PropertyOwnerLocationGridProps) {
  if (isLoading && !isLoadingMore) {
    return <ProjectSkeletonGrid count={6} />;
  }

  return (
    <div id="scrollableDiv" className="h-[72dvh] overflow-auto pr-4">
      <InfiniteScroll
        scrollableTarget="scrollableDiv"
        dataLength={locations.length}
        next={onLoadMore}
        hasMore={hasMore}
        loader={
          isLoadingMore ? (
            <div className="text-center py-4">Loading...</div>
          ) : null
        }
      >
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {locations.map((location, index) => (
            <LocationCard
              key={
                location.id ? `location-${location.id}` : `location-${index}`
              }
              location={location}
              onEdit={onEdit}
              onDelete={onDelete}
              onViewDetail={onView}
              showQuickAdd={false}
              statusBadgeText={[location.status]}
              showEditDeleteInTitle={true}
              viewButtonText="View location"
              hoverAction={true}
            />
          ))}
        </div>
      </InfiniteScroll>
    </div>
  );
}
