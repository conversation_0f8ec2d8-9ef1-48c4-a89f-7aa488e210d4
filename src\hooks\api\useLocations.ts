import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useLocationService, useTagService } from '../use-services';
import { TagOption } from '@/types/tag';
import { queryKeys } from '@/lib/query-keys';
import { toastError, toastSuccess } from '@/lib/toast';
import { ApiError } from '@/lib/api-client';
import { Address } from '@/app/scout/reference/[id]/search-locations/search-location-context';

interface UseLocationsFilters {
  page?: number;
  search?: string;
  status?: string;
  tagIds?: string;
  where?: Address;
  limit?: number;
  size?: string;
}

interface UseLocationsOptions {
  enabled?: boolean;
}

const LIMIT = 6;

/**
 * Hook for fetching locations with filters
 */
export function useLocations(
  filters?: UseLocationsFilters,
  options?: UseLocationsOptions
) {
  const locationService = useLocationService();

  return useQuery({
    queryKey: queryKeys.locations.list(filters),
    queryFn: async () => {
      return locationService.getLocations(
        filters?.page || 1,
        filters?.search,
        filters?.status,
        filters?.tagIds,
        filters?.where,
        filters?.limit || LIMIT,
        filters?.size
      );
    },
    enabled: options?.enabled !== false,
    staleTime: 1000 * 30, // 30 seconds
    gcTime: 1000 * 60 * 5, // 5 minutes
  });
}

export function useLocationCities() {
  const locationService = useLocationService();
  return useQuery({
    queryKey: queryKeys.locations.cities(),
    queryFn: async () => {
      return locationService.getLocationCities();
    },
  });
}

/**
 * Hook for fetching location requests (pending locations)
 */
export function useLocationRequests(
  filters?: Omit<UseLocationsFilters, 'status' | 'size'>,
  options?: UseLocationsOptions
) {
  const locationService = useLocationService();

  return useQuery({
    queryKey: queryKeys.locations.requests(filters),
    queryFn: async () => {
      return locationService.getLocationsRequests(
        filters?.page || 1,
        filters?.search,
        filters?.tagIds,
        filters?.where,
        filters?.limit || LIMIT
      );
    },
    enabled: options?.enabled !== false,
    staleTime: 1000 * 30, // 30 seconds
    gcTime: 1000 * 60 * 5, // 5 minutes
  });
}

/**
 * Hook for fetching tag options for location filters
 */
export function useTagOptions() {
  const tagService = useTagService();

  return useQuery({
    queryKey: [...queryKeys.tags.all, 'options'],
    queryFn: async () => {
      const response = await tagService.getTags({ limit: 100 });
      return response.data.map(
        (tag): TagOption => ({
          value: tag.id,
          label: tag.name,
          color: tag.color,
        })
      );
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
    gcTime: 1000 * 60 * 10, // 10 minutes
  });
}

/**
 * Hook for deleting a location
 */
export function useDeleteLocation() {
  const locationService = useLocationService();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      return locationService.deleteLocation(id);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.locations.all });
      toastSuccess('Location removed.');
    },
    onError: (error: unknown) => {
      const message =
        (error as ApiError).response?.data?.message ??
        'Failed to delete location';
      toastError(message);
    },
  });
}

/**
 * Hook for updating location status (accept/reject request)
 */
export function useUpdateLocationStatus() {
  const locationService = useLocationService();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      id,
      type,
    }: {
      id: string;
      type: 'accept' | 'reject';
    }) => {
      const status = type === 'accept' ? 'active' : 'inactive';
      return locationService.updateLocationStatus(id, status);
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.locations.all });
      const message =
        variables.type === 'accept'
          ? 'Location approved.'
          : 'Location rejected.';
      toastSuccess(message);
    },
    onError: (error: unknown) => {
      const apiError = error as ApiError;
      const message = apiError.response?.data?.message ?? 'An error occurred';
      toastError(message);
    },
  });
}
