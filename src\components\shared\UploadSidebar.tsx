'use client';

import { useState } from 'react';
import {
  X,
  CheckCircle2,
  AlertCircle,
  Loader2,
  ChevronDown,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import Image from 'next/image';

export interface UploadItem {
  id: string;
  file: File;
  progress: number;
  status: 'pending' | 'uploading' | 'success' | 'error';
  error?: string;
  url?: string;
  key?: string;
  previewImage?: string;
}

interface UploadSidebarProps {
  uploads: UploadItem[];
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onCancel?: (id: string) => void;
  onDismiss?: (id: string) => void;
}

export default function UploadSidebar({
  uploads,
  open,
  onOpenChange,
  onCancel,
  onDismiss,
}: UploadSidebarProps) {
  const [isCollapsed, setIsCollapsed] = useState(false);

  if (!open || uploads.length === 0) return null;

  const completedCount = uploads.filter(u => u.status === 'success').length;
  const hasCompleted = completedCount > 0;
  const titleText = hasCompleted
    ? `${completedCount} upload${completedCount === 1 ? '' : 's'} complete`
    : `${uploads.length} upload${uploads.length === 1 ? '' : 's'}`;

  return (
    <div className="fixed bottom-[77px] max-w-sm right-8 z-40 bg-white shadow-lg border border-border border-b-0 rounded-t-lg">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-neutral-200">
        <h2 className="text-base font-semibold text-neutral-900">
          {titleText}
        </h2>
        <div className="flex items-center gap-1">
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={() => setIsCollapsed(!isCollapsed)}
            className="h-6 w-6 p-0 hover:bg-transparent"
          >
            <ChevronDown
              className={cn(
                'h-4 w-4 text-neutral-500 transition-transform',
                isCollapsed && 'rotate-180'
              )}
            />
          </Button>
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={e => {
              e.preventDefault();
              e.stopPropagation();
              onOpenChange(false);
            }}
            className="h-6 w-6 p-0 hover:bg-transparent"
            aria-label="Close upload sidebar"
          >
            <X className="h-4 w-4 text-neutral-500 pointer-events-none" />
          </Button>
        </div>
      </div>

      {/* Content */}
      {!isCollapsed && (
        <div className="max-h-[300px] overflow-y-auto p-4">
          <div className="space-y-2">
            {[...uploads].reverse().map(upload => (
              <div
                key={upload.id}
                className="flex items-center gap-3 py-2 px-1 rounded hover:bg-neutral-50 transition-colors"
              >
                {/* Thumbnail */}
                <div className="relative w-8 h-8 rounded bg-neutral-200 flex-shrink-0 overflow-hidden">
                  {upload.previewImage ? (
                    <Image
                      src={upload.previewImage || ''}
                      alt={upload.file.name}
                      fill
                      className="object-cover"
                      unoptimized
                    />
                  ) : (
                    <div className="w-full h-full bg-neutral-300" />
                  )}
                </div>

                {/* File Info */}
                <div className="flex-1 min-w-0">
                  <p className="text-sm text-neutral-900 truncate">
                    {upload.file.name}
                  </p>
                  {upload.status === 'uploading' && (
                    <div className="mt-1">
                      <div className="w-full bg-neutral-200 rounded-full h-1">
                        <div
                          className="bg-primary-500 h-1 rounded-full transition-all duration-300"
                          style={{ width: `${upload.progress}%` }}
                        />
                      </div>
                    </div>
                  )}
                </div>

                {/* Status Icon */}
                <div className="flex-shrink-0">
                  {upload.status === 'pending' && (
                    <Loader2 className="h-4 w-4 text-neutral-400" />
                  )}
                  {upload.status === 'uploading' && (
                    <div className="flex items-center gap-1">
                      <Loader2 className="h-4 w-4 animate-spin text-primary-500" />
                      {onCancel && (
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={e => {
                            e.preventDefault();
                            e.stopPropagation();
                            onCancel(upload.id);
                          }}
                          className="h-5 w-5 p-0 hover:bg-transparent"
                        >
                          <X className="h-3 w-3 text-neutral-500" />
                        </Button>
                      )}
                    </div>
                  )}
                  {upload.status === 'success' && (
                    <CheckCircle2 className="h-5 w-5 text-green-600 flex-shrink-0" />
                  )}
                  {upload.status === 'error' && (
                    <div className="flex items-center gap-1">
                      <AlertCircle className="h-5 w-5 text-red-600 flex-shrink-0" />
                      {onDismiss && (
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={e => {
                            e.preventDefault();
                            e.stopPropagation();
                            onDismiss(upload.id);
                          }}
                          className="h-5 w-5 p-0 hover:bg-transparent"
                        >
                          <X className="h-3 w-3 text-neutral-500" />
                        </Button>
                      )}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
