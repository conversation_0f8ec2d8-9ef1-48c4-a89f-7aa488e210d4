import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { makeServerRequest } from '@/lib/api-client';
import { Reference } from '@/types';
import ShareReferenceProvider from './share-reference-provider';

export default async function ShareReferenceLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;
  const session = await getServerSession(authOptions);

  let reference: Reference | null = null;

  try {
    if (session?.accessToken) {
      // Authenticated request
      reference = await makeServerRequest<Reference>(
        `/reference-lists/${id}`,
        session.accessToken
      );
    } else {
      // Public share - try without auth (if API supports it)
      const baseUrl = process.env.NESTJS_API_URL || 'http://localhost:3001';
      const response = await fetch(`${baseUrl}/reference-lists/${id}/share`, {
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        reference = await response.json();
      }
    }
  } catch (error) {
    console.error('Failed to fetch reference:', error);
    // Reference will remain null, error will be handled in the page
  }

  return (
    <ShareReferenceProvider reference={reference}>
      {children}
    </ShareReferenceProvider>
  );
}
