.List {
  display: grid;
  grid-auto-rows: max-content;
  box-sizing: border-box;
  min-width: 21.875rem;
  grid-gap: 0.625rem;
  padding: 1.25rem;
  padding-bottom: 0;
  margin: 0.625rem;
  border-radius: 0.3125rem;
  min-height: 12.5rem;
  transition: background-color 350ms ease;
  grid-template-columns: repeat(var(--columns, 1), 1fr);

  &:after {
    content: '';
    height: 0.625rem;
    grid-column-start: span var(--columns, 1);
  }

  &.horizontal {
    width: 100%;
    grid-auto-flow: column;
  }
}
