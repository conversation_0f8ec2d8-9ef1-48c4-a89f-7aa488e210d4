export enum Role {
  SuperAdmin = 'super_admin',
  ProductionAdmin = 'production_admin',
  Scout = 'scout',
  PropertyOwner = 'property_owner',
}

export enum PropertyType {
  RESIDENTIAL = 'residential',
  COMMERCIAL = 'commercial',
  PUBLIC = 'public',
}

export enum TagType {
  BUILDING_TYPE = 'building_type',
  STYLE = 'style',
  STRUCTURE_TYPE = 'structure_type',
  INTERIOR_FEATURE = 'interior_feature',
  EXTERIOR_FEATURE = 'exterior_feature',
  FUNCTION_TYPE = 'function_type',
  SPACE_TYPE = 'space_type',
}

export enum FileErrorCode {
  UPLOAD_ERROR = 'Upload failed',
  VALIDATION_ERROR = 'Validation failed',
  FILE_SIZE_ERROR = 'File size too large',
  FILE_TYPE_ERROR = 'File type not supported',
  FILE_MAX_FILES_ERROR = 'Too many files selected',
  FILE_CANCELLED = 'Upload cancelled',
}

export enum FileStatusCode {
  UPLOADING = 'Uploading',
  UPLOADED = 'Uploaded',
  ERROR = 'Error',
}

export enum UserStatus {
  ACTIVE = 'active',
  DISABLED = 'disabled',
  PENDING = 'pending',
}
