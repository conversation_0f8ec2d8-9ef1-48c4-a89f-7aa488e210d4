'use client';

import { useCallback, useEffect, useState } from 'react';
import {
  APIProvider,
  Map,
  Marker,
  InfoWindow,
} from '@vis.gl/react-google-maps';
import { Location } from '@/types';
import { useRouter } from 'next/navigation';
import LocationCard from '@/components/features/locations/LocationCard';

interface LocationsMapViewProps {
  locations?: Location[];
  referenceId: string;
  onAddLocation: (data: Location) => void;
}

export default function LocationsMapView({
  locations = [],
  referenceId,
  onAddLocation,
}: LocationsMapViewProps) {
  const router = useRouter();
  const [selectedLocation, setSelectedLocation] = useState<Location | null>(
    null
  );
  const [mapCenter, setMapCenter] = useState({ lat: 34.0522, lng: -118.2437 }); // Default to LA
  const [mapZoom, setMapZoom] = useState(12); // Default zoom

  // returns integer zoom level for Web Mercator (Google Maps)
  // minLat, maxLat, minLng, maxLng in degrees
  // mapWidth, mapHeight in pixels
  // padding in pixels applied on each side (default 0)
  function calculateZoomForBounds(
    minLat: number,
    maxLat: number,
    minLng: number,
    maxLng: number,
    mapWidth: number,
    mapHeight: number,
    padding = 0
  ): number {
    // clamp inputs sanity
    if (mapWidth <= 0 || mapHeight <= 0) return 1;

    // convert degrees longitude difference into 0..360 positive span
    let lngDiff = maxLng - minLng;
    if (lngDiff < 0) lngDiff += 360; // crosses dateline

    // handle degenerate case (all markers at same point)
    const EPS = 1e-9;
    if (Math.abs(maxLat - minLat) < EPS && Math.abs(lngDiff) < EPS) {
      // arbitrary high zoom when single point (you can change)
      return 21;
    }

    // Web Mercator helper: convert latitude to "Mercator Y" (radians)
    function latToRadMercator(lat: number): number {
      const sin = Math.sin((lat * Math.PI) / 180);
      // Guard against numeric issues at poles
      const val = Math.log((1 + sin) / (1 - sin)) / 2;
      return val;
    }

    // Fractions of world covered by the spans
    const latFraction =
      (latToRadMercator(maxLat) - latToRadMercator(minLat)) / Math.PI; // range ~0..1
    const lngFraction = lngDiff / 360; // range 0..1

    // Effective available pixels after padding on both sides
    const effectiveWidth = Math.max(1, mapWidth - padding * 2);
    const effectiveHeight = Math.max(1, mapHeight - padding * 2);

    const WORLD_PX = 256; // tile size at zoom 0

    // compute zoom level that fits a fraction into given pixels:
    // zoom = floor( log2( (mapPx / WORLD_PX) / fraction ) )
    function zoomForFraction(mapPx: number, fraction: number): number {
      // if fraction is extremely small (almost zero), return a high zoom
      if (fraction <= 0) return 21;
      const result = Math.log2(mapPx / WORLD_PX / fraction);
      return Math.floor(result);
    }

    const zoomLng = zoomForFraction(
      effectiveWidth,
      Math.max(lngFraction, 1e-9)
    );
    const zoomLat = zoomForFraction(
      effectiveHeight,
      Math.max(latFraction, 1e-9)
    );

    // choose the smaller zoom so both lat & lng fit
    const rawZoom = Math.min(zoomLng, zoomLat);

    // clamp to typical Google Maps limits (0..21). adjust if you need different.
    const zoom = Math.max(0, Math.min(21, rawZoom));

    return zoom;
  }

  // Calculate center point and zoom for the map based on locations
  useEffect(() => {
    setSelectedLocation(null);
    const locationsWithCoords = locations.filter(location => {
      const lat = Number(location.latitude);
      const lng = Number(location.longitude);
      return (
        !isNaN(lat) &&
        !isNaN(lng) &&
        isFinite(lat) &&
        isFinite(lng) &&
        lat !== 0 &&
        lng !== 0
      );
    });

    if (locationsWithCoords.length === 0) {
      return;
    }

    if (locationsWithCoords.length === 1) {
      // Single location: center on it with zoom 15
      const loc = locationsWithCoords[0];
      const lat = Number(loc.latitude);
      const lng = Number(loc.longitude);
      setMapCenter({ lat, lng });
      setMapZoom(15);
      return;
    }

    // Multiple locations: calculate bounds
    const lats = locationsWithCoords.map(loc => Number(loc.latitude));
    const lngs = locationsWithCoords.map(loc => Number(loc.longitude));

    const minLat = Math.min(...lats);
    const maxLat = Math.max(...lats);
    const minLng = Math.min(...lngs);
    const maxLng = Math.max(...lngs);

    // Calculate center
    const centerLat = (minLat + maxLat) / 2;
    const centerLng = (minLng + maxLng) / 2;

    // Calculate zoom to fit all markers (padding already included in calculateZoom)
    const zoom = calculateZoomForBounds(
      minLat,
      maxLat,
      minLng,
      maxLng,
      1000,
      800,
      0
    );

    // Only update if we have valid coordinates
    if (
      !isNaN(centerLat) &&
      !isNaN(centerLng) &&
      isFinite(centerLat) &&
      isFinite(centerLng)
    ) {
      setMapCenter({ lat: centerLat, lng: centerLng });
      setMapZoom(zoom);
    }
  }, [locations]);

  const handleMarkerClick = useCallback((location: Location) => {
    setSelectedLocation(location);
  }, []);

  const handleInfoWindowClose = useCallback(() => {
    setSelectedLocation(null);
  }, []);

  const handleMapClick = useCallback(() => {
    setSelectedLocation(null);
  }, []);

  if (!process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY) {
    return (
      <div className="w-full h-[calc(100vh-17.5rem)] rounded-lg overflow-hidden border bg-gray-100 flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="text-gray-500">
            <svg
              className="w-16 h-16 mx-auto mb-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.5}
                d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
              />
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.5}
                d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
              />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-gray-700">
            Google Maps API Key Required
          </h3>
          <p className="text-gray-500 max-w-md">
            To display the map, please add your Google Maps API key to the
            environment variables as{' '}
            <code className="bg-gray-200 px-2 py-1 rounded text-sm">
              NEXT_PUBLIC_GOOGLE_MAPS_API_KEY
            </code>
          </p>
        </div>
      </div>
    );
  }

  // Ensure mapCenter has valid coordinates
  const validMapCenter =
    !isNaN(mapCenter.lat) &&
    !isNaN(mapCenter.lng) &&
    isFinite(mapCenter.lat) &&
    isFinite(mapCenter.lng)
      ? mapCenter
      : { lat: 34.0522, lng: -118.2437 }; // Default to LA if invalid

  return (
    <div className="w-full h-[calc(100vh-17.5rem)] rounded-2xl overflow-hidden border">
      <APIProvider apiKey={process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY}>
        <Map
          defaultCenter={validMapCenter}
          defaultZoom={mapZoom}
          mapId="scoutr-locations-map"
          className="w-full h-full"
          gestureHandling="greedy"
          disableDefaultUI={false}
          onClick={() => handleMapClick()}
        >
          {locations
            .filter(location => {
              const lat = Number(location.latitude);
              const lng = Number(location.longitude);
              return (
                !isNaN(lat) &&
                !isNaN(lng) &&
                isFinite(lat) &&
                isFinite(lng) &&
                lat !== 0 &&
                lng !== 0
              );
            })
            .map(location => {
              const lat = Number(location.latitude);
              const lng = Number(location.longitude);
              return (
                <Marker
                  key={location.id}
                  position={{
                    lat,
                    lng,
                  }}
                  onClick={() => handleMarkerClick(location)}
                  title={location.name}
                  // icon={{
                  //   url: '/assets/icons/location-marker.svg', // put your file in /public/assets/
                  //   scaledSize: new google.maps.Size(24, 24), // resize
                  //   anchor: new google.maps.Point(12, 12), // bottom-center
                  // }}
                />
              );
            })}

          {selectedLocation &&
            selectedLocation.latitude &&
            selectedLocation.longitude && (
              <InfoWindow
                position={{
                  lat: Number(selectedLocation.latitude),
                  lng: Number(selectedLocation.longitude),
                }}
                onCloseClick={handleInfoWindowClose}
                headerDisabled
                pixelOffset={[0, -36]}
                maxWidth={360}
                minWidth={360}
              >
                <LocationCard
                  location={selectedLocation}
                  onAddLocation={onAddLocation}
                  onViewDetail={location => {
                    router.push(
                      `/scout/reference/${referenceId}/view-details/${location.id}`
                    );
                  }}
                  hoverAction={false}
                />
                {/* <div>
                  <Carousel className="relative">
                    <CarouselContent>
                      {Array.from({ length: 5 }).map((_, index) => (
                        <CarouselItem key={index}>
                          <div className="relative w-full h-[12.5rem]">
                            <Image
                              src={`https://picsum.photos/350/200?random=${index}`}
                              alt="Location"
                              width={350}
                              height={200}
                              className="w-full h-full object-cover rounded-t-lg"
                            />
                          </div>
                        </CarouselItem>
                      ))}
                    </CarouselContent>
                    <CarouselPrevious className="absolute top-1/2 left-2 -translate-y-1/2 z-10 w-8.5 h-10 bg-white shadow-[0_0.625rem_0.9375rem_0_rgba(0,0,0,0.1),0_0.25rem_0.375rem_0_rgba(0,0,0,0.1)]" />
                    <CarouselNext className="absolute top-1/2 right-2 -translate-y-1/2 z-10 w-8.5 h-10 bg-white shadow-[0_0.625rem_0.9375rem_0_rgba(0,0,0,0.1),0_0.25rem_0.375rem_0_rgba(0,0,0,0.1)]" />
                  </Carousel>
                  <div className="px-4 pt-4 pb-3">
                    <Heading level={6} className="mb-1">
                      {selectedLocation.name}
                    </Heading>
                    <p className="text-sm leading-5 text-foreground mb-2">
                      {selectedLocation.address}
                    </p>
                    <div className="flex flex-wrap gap-1.5 max-h-[6.25rem] overflow-y-auto">
                      {selectedLocation.tags?.map((tag, index) => (
                        <Badge
                          key={index}
                          className="inline-block text-sm leading-4 px-2 py-1 rounded-full"
                          variant="secondary"
                        >
                          {tag.name}
                        </Badge>
                      ))}
                    </div>
                    <div className="mt-5 flex items-center justify-center gap-2">
                      <Button
                        onClick={() => {
                          console.log('Add location:', selectedLocation);
                          handleInfoWindowClose();
                        }}
                        className="w-full max-w-[11rem]"
                      >
                        <Plus /> Add to list
                      </Button>
                      <Button
                        onClick={() => {
                          console.log('View details:', selectedLocation);
                          router.push(
                            `/scout/reference/${referenceId}/view-details/${selectedLocation.id}`
                          );
                          handleInfoWindowClose();
                        }}
                        className="flex-1"
                        variant="outline"
                      >
                        View details
                      </Button>
                    </div>
                  </div>
                </div> */}
              </InfoWindow>
            )}
        </Map>
      </APIProvider>
    </div>
  );
}
