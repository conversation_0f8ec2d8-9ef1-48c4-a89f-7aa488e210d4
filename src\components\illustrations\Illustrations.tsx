import Image from 'next/image';

interface IllustrationProps {
  width?: number;
  height?: number;
  className?: string;
}

export function UndrawEnterPassword({
  width = 229,
  height = 192,
  className,
}: IllustrationProps) {
  return (
    <div className={`relative ${className || ''}`} style={{ width, height }}>
      <Image
        src="/assets/illustrations/undraw-enter-password.svg"
        alt="Enter password illustration"
        fill
        className="object-contain"
      />
    </div>
  );
}

export function StarrySky({
  width = 229,
  height = 192,
  className,
}: IllustrationProps) {
  return (
    <div className={`relative ${className || ''}`} style={{ width, height }}>
      <Image
        src="/assets/illustrations/starry-sky.svg"
        alt="Starry sky illustration"
        fill
        className="object-contain"
      />
    </div>
  );
}

export function UndrawDeliveryLocation({
  width = 229,
  height = 192,
  className,
}: IllustrationProps) {
  return (
    <div className={`relative ${className || ''}`} style={{ width, height }}>
      <Image
        src="/assets/illustrations/undraw_delivery-location.svg"
        alt="Delivery location illustration"
        fill
        className="object-contain"
      />
    </div>
  );
}

export function UndrawCompletedTasks({
  width = 229,
  height = 192,
  className,
}: IllustrationProps) {
  return (
    <div className={`relative ${className || ''}`} style={{ width, height }}>
      <Image
        src="/assets/illustrations/undraw-completed-tasks.svg"
        alt="Completed tasks illustration"
        fill
        className="object-contain"
      />
    </div>
  );
}

export function UndrawDeliveryAddress({
  width = 229,
  height = 192,
  className,
}: IllustrationProps) {
  return (
    <div className={`relative ${className || ''}`} style={{ width, height }}>
      <Image
        src="/assets/illustrations/undraw-delivery-address.svg"
        alt="Delivery address illustration"
        fill
        className="object-contain"
      />
    </div>
  );
}

export function UndrawFeedback({
  width = 229,
  height = 192,
  className,
}: IllustrationProps) {
  return (
    <div className={`relative ${className || ''}`} style={{ width, height }}>
      <Image
        src="/assets/illustrations/undraw-feedback.svg"
        alt="Feedback illustration"
        fill
        className="object-contain"
      />
    </div>
  );
}

export function UndrawFestivities({
  width = 229,
  height = 192,
  className,
}: IllustrationProps) {
  return (
    <div className={`relative ${className || ''}`} style={{ width, height }}>
      <Image
        src="/assets/illustrations/undraw-festivities.svg"
        alt="Festivities illustration"
        fill
        className="object-contain"
      />
    </div>
  );
}

export function UndrawTeamWork({
  width = 229,
  height = 192,
  className,
}: IllustrationProps) {
  return (
    <div className={`relative ${className || ''}`} style={{ width, height }}>
      <Image
        src="/assets/illustrations/undraw-team-work.svg"
        alt="Team work illustration"
        fill
        className="object-contain"
      />
    </div>
  );
}

export function UndrawTrees({
  width = 229,
  height = 192,
  className,
}: IllustrationProps) {
  return (
    <div className={`relative ${className || ''}`} style={{ width, height }}>
      <Image
        src="/assets/illustrations/undraw-trees.svg"
        alt="Trees illustration"
        fill
        className="object-contain"
      />
    </div>
  );
}

export function UndrawUrbanDesign({
  width = 229,
  height = 192,
  className,
}: IllustrationProps) {
  return (
    <div className={`relative ${className || ''}`} style={{ width, height }}>
      <Image
        src="/assets/illustrations/undraw-urban-design.svg"
        alt="Urban design illustration"
        fill
        className="object-contain"
      />
    </div>
  );
}
