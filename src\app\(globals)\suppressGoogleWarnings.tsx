'use client';

import { useEffect } from 'react';

export default function SuppressGoogleWarnings() {
  useEffect(() => {
    const originalWarn = console.warn;
    console.warn = (...args) => {
      if (
        args.some(
          arg =>
            typeof arg === 'string' &&
            (arg.includes('google.maps.places.Autocomplete') ||
              arg.includes('google.maps.places.PlacesService'))
        )
      ) {
        return; // Skip the deprecation warning
      }
      originalWarn.apply(console, args);
    };

    return () => {
      console.warn = originalWarn; // restore when component unmounts (rarely happens for layout)
    };
  }, []);

  return null; // nothing to render
}
