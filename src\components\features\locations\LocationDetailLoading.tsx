'use client';

import { Skeleton } from '@/components/ui/skeleton';

export function LocationDetailLoading() {
  return (
    <div className="w-full flex justify-center">
      <div className="space-y-6 xl:w-[70.5rem] p-6 xl:px-0">
        {/* Breadcrumb skeleton */}
        <div className="flex items-center gap-2 text-sm">
          <Skeleton className="h-4 w-20" />
          <Skeleton className="h-4 w-4" />
          <Skeleton className="h-4 w-32" />
        </div>

        {/* Photos grid skeleton */}
        <div className="grid gap-2 sm:gap-6 grid-cols-2 sm:grid-cols-4 grid-rows-2">
          <Skeleton className="row-span-2 col-span-2 h-48 sm:h-70 rounded-xl" />
          <Skeleton className="h-24 sm:h-32 rounded-xl" />
          <Skeleton className="h-24 sm:h-32 rounded-xl" />
          <Skeleton className="h-24 sm:h-32 rounded-xl" />
          <Skeleton className="h-24 sm:h-32 rounded-xl" />
        </div>

        {/* Text + sidebar skeleton */}
        <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6 items-start">
          {/* Left: title / text */}
          <div className="space-y-3">
            <Skeleton className="h-6 w-2/3" />
            <Skeleton className="h-4 w-1/2" />
            <div className="flex flex-wrap gap-2 mt-2">
              <Skeleton className="h-6 w-16 rounded-full" />
              <Skeleton className="h-6 w-20 rounded-full" />
              <Skeleton className="h-6 w-24 rounded-full" />
            </div>
            <div className="space-y-2 mt-4">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-5/6" />
              <Skeleton className="h-4 w-4/6" />
            </div>
          </div>

          {/* Right: detail card */}
          <div className="w-full">
            <div className="rounded-xl border border-gray-100 bg-white shadow-sm p-4 space-y-4">
              <Skeleton className="h-9 w-full rounded-md" />
              <div className="space-y-2">
                <Skeleton className="h-3 w-24" />
                <div className="grid grid-cols-2 gap-y-2">
                  <Skeleton className="h-3 w-20" />
                  <Skeleton className="h-3 w-16 justify-self-end" />
                  <Skeleton className="h-3 w-24" />
                  <Skeleton className="h-3 w-20 justify-self-end" />
                  <Skeleton className="h-3 w-16" />
                  <Skeleton className="h-3 w-24 justify-self-end" />
                  <Skeleton className="h-3 w-10" />
                  <Skeleton className="h-3 w-16 justify-self-end" />
                </div>
              </div>
              <div className="border-t border-gray-100 pt-4 space-y-3">
                <Skeleton className="h-3 w-28" />
                <div className="flex flex-wrap gap-3">
                  <Skeleton className="h-4 w-24 rounded-full" />
                  <Skeleton className="h-4 w-20 rounded-full" />
                  <Skeleton className="h-4 w-20 rounded-full" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
