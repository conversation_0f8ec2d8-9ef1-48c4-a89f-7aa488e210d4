'use client';

import { useState, useCallback, useMemo } from 'react';
import Image from 'next/image';
import { GripVertical } from 'lucide-react';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
  DragStartEvent,
  DragOverlay,
  UniqueIdentifier,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  useSortable,
  horizontalListSortingStrategy,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { cn } from '@/lib/utils';
import { Photo } from '@/types/location';

interface SortableThumbnailBarProps {
  images: Photo[];
  onReorder: (reorderedImages: Photo[]) => void;
  className?: string;
  selectedImageIds?: string[];
  orderAllImages?: boolean;
}

interface SortableThumbnailProps {
  image: Photo;
  index: number;
}

// Helper function to get stable ID for an image
function getStableId(image: Photo, index: number): string {
  return image.id || image.key || `image-${image.url}-${index}`;
}

function SortableThumbnail({ image, index }: SortableThumbnailProps) {
  const stableId = getStableId(image, index);

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging: isSortableDragging,
  } = useSortable({
    id: stableId,
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isSortableDragging ? 0.5 : 1,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={cn(
        'relative flex-shrink-0 w-[86px] h-[86px] rounded-md overflow-hidden border-2 cursor-move',
        'border-transparent hover:border-primary',
        isSortableDragging && 'opacity-50'
      )}
    >
      {image.url && (
        <Image
          src={image.url}
          alt={`Image ${index + 1}`}
          fill
          className="object-cover"
          unoptimized
        />
      )}
      {/* GripVertical icon - only draggable area, show on hover */}
      <div
        {...attributes}
        {...listeners}
        className="absolute inset-0 flex items-center justify-center bg-black/20 cursor-grab active:cursor-grabbing opacity-0 hover:opacity-100 transition-opacity group"
      >
        <GripVertical className="h-6 w-6 text-white" />
      </div>
    </div>
  );
}

export default function SortableThumbnailBar({
  images,
  onReorder,
  className = '',
  selectedImageIds = [],
  orderAllImages = true,
}: SortableThumbnailBarProps) {
  const [activeId, setActiveId] = useState<UniqueIdentifier | null>(null);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8, // Require 8px movement before activating drag
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const handleDragStart = useCallback((event: DragStartEvent) => {
    setActiveId(event.active.id);
  }, []);

  // Helper function to get image ID
  const getImageId = useCallback((image: Photo, index: number) => {
    return image.id || `image-${index}`;
  }, []);

  // Filter images based on orderAllImages prop
  const filteredImages = useMemo(() => {
    if (orderAllImages) {
      return images;
    }
    // Only show selected images
    return images.filter((image, index) => {
      const imageId = getImageId(image, index);
      return selectedImageIds.includes(imageId);
    });
  }, [images, selectedImageIds, orderAllImages, getImageId]);

  // Memoize sorted images and ID mapping for performance
  const sortedImages = useMemo(
    () => [...filteredImages].sort((a, b) => (a.order || 0) - (b.order || 0)),
    [filteredImages]
  );

  // Create a map of stable IDs to indices for O(1) lookup
  const idToIndexMap = useMemo(() => {
    const map = new Map<string, number>();
    sortedImages.forEach((img, idx) => {
      map.set(getStableId(img, idx), idx);
    });
    return map;
  }, [sortedImages]);

  const handleDragEnd = useCallback(
    (event: DragEndEvent) => {
      const { active, over } = event;

      if (!over || active.id === over.id) {
        setActiveId(null);
        return;
      }

      // Use map for O(1) lookup instead of findIndex
      const oldIndex = idToIndexMap.get(active.id as string);
      const newIndex = idToIndexMap.get(over.id as string);

      if (
        oldIndex !== undefined &&
        newIndex !== undefined &&
        oldIndex !== newIndex
      ) {
        if (orderAllImages) {
          // Reorder all images
          const reorderedSorted = arrayMove(sortedImages, oldIndex, newIndex);

          // Create a map of image reference to new order
          const orderMap = new Map<Photo, number>();
          reorderedSorted.forEach((img, index) => {
            orderMap.set(img, index);
          });

          // Update order property in original array without changing array order
          const updatedImages = images.map(img => {
            const newOrder = orderMap.get(img);
            return newOrder !== undefined ? { ...img, order: newOrder } : img;
          });

          onReorder(updatedImages);
        } else {
          // Only reorder selected images relative to each other
          const reorderedSelected = arrayMove(sortedImages, oldIndex, newIndex);

          // Get all selected images with their new relative order
          const selectedOrderMap = new Map<Photo, number>();
          reorderedSelected.forEach((img, index) => {
            selectedOrderMap.set(img, index);
          });

          // Find the minimum order of selected images to maintain relative positioning
          const selectedImagesInOriginal = images.filter((image, index) => {
            const imageId = getImageId(image, index);
            return selectedImageIds.includes(imageId);
          });

          const minSelectedOrder = Math.min(
            ...selectedImagesInOriginal.map(img => img.order || 0)
          );

          // Update only selected images with new relative order
          const updatedImages = images.map((img, index) => {
            const imageId = getImageId(img, index);
            if (selectedImageIds.includes(imageId)) {
              const relativeOrder = selectedOrderMap.get(img);
              if (relativeOrder !== undefined) {
                return { ...img, order: minSelectedOrder + relativeOrder };
              }
            }
            return img;
          });

          onReorder(updatedImages);
        }
      }

      setActiveId(null);
    },
    [
      images,
      sortedImages,
      idToIndexMap,
      onReorder,
      orderAllImages,
      selectedImageIds,
      getImageId,
    ]
  );

  const handleDragCancel = useCallback(() => {
    setActiveId(null);
  }, []);

  const activeImage = useMemo(() => {
    if (!activeId) return null;
    const index = idToIndexMap.get(activeId as string);
    return index !== undefined ? sortedImages[index] : null;
  }, [activeId, sortedImages, idToIndexMap]);
  const activeIndex = useMemo(() => {
    if (!activeImage) return -1;
    return sortedImages.indexOf(activeImage);
  }, [activeImage, sortedImages]);

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCenter}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
      onDragCancel={handleDragCancel}
    >
      <div
        className={cn(
          'flex gap-2 overflow-x-auto p-2 border border-border rounded-md min-h-26',
          className
        )}
      >
        <SortableContext
          items={sortedImages.map((img, idx) => getStableId(img, idx))}
          strategy={horizontalListSortingStrategy}
        >
          {sortedImages.map((image, index) => {
            const stableId = getStableId(image, index);
            return (
              <SortableThumbnail key={stableId} image={image} index={index} />
            );
          })}
        </SortableContext>
      </div>

      <DragOverlay>
        {activeImage && activeIndex !== -1 ? (
          <div className="relative flex-shrink-0 w-[86px] h-[86px] rounded-md overflow-hidden border-2 border-primary-500 opacity-90 rotate-3 shadow-lg">
            {activeImage.url && (
              <Image
                src={activeImage.url}
                alt={`Image ${activeIndex + 1}`}
                fill
                className="object-cover"
                unoptimized
              />
            )}
            <div className="absolute inset-0 flex items-center justify-center bg-black/30">
              <GripVertical className="h-6 w-6 text-white" />
            </div>
          </div>
        ) : null}
      </DragOverlay>
    </DndContext>
  );
}
