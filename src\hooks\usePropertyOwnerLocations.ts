import { useState, useCallback, useEffect } from 'react';
import { Location } from '@/types';
import { useLocationService } from '@/hooks/use-services';
import { toastError } from '@/lib/toast';

interface UsePropertyOwnerLocationsProps {
  search: string;
  status: string;
  locationType: string;
}

export function usePropertyOwnerLocations({
  search,
  status,
  locationType,
}: UsePropertyOwnerLocationsProps) {
  const locationService = useLocationService();
  const [locationData, setLocationData] = useState<Location[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isInitialLoading, setIsInitialLoading] = useState<boolean>(true);
  const [isLoadingMore, setIsLoadingMore] = useState<boolean>(false);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [page, setPage] = useState<number>(1);
  const [totalPages, setTotalPages] = useState<number>(0);
  const [totalCount, setTotalCount] = useState<number>(0);
  const [hasFilters, setHasFilters] = useState<boolean>(false);

  const fetchLocations = useCallback(
    async (
      pageNum: number,
      searchQuery?: string,
      statusQuery?: string,
      locationTypeQuery?: string,
      isLoadMore: boolean = false
    ) => {
      try {
        if (!isLoadMore) {
          setIsLoading(true);
          setIsLoadingMore(false);
        } else {
          setIsLoadingMore(true);
        }

        // Build tagIds from locationType if needed
        let tagIds: string | undefined;
        if (locationTypeQuery && locationTypeQuery !== 'all') {
          // You may need to map locationType to tag IDs
          // For now, we'll pass it as undefined and let the API handle it
          tagIds = undefined;
        }

        const response = await locationService.getLocations(
          pageNum,
          searchQuery || undefined,
          statusQuery !== 'all' ? statusQuery : undefined,
          tagIds,
          undefined, // where (address filter)
          6, // limit
          undefined // size
        );

        setLocationData(prev => {
          if (isLoadMore) {
            // Filter out duplicates by location ID
            const existingIds = new Set(prev.map(loc => loc.id));
            const newLocations = response.data.filter(
              loc => !existingIds.has(loc.id)
            );
            return [...prev, ...newLocations];
          }
          return response.data;
        });
        setTotalPages(response.meta.totalPages);
        setTotalCount(response.meta.totalItems);
        setHasMore(response.meta.currentPage < response.meta.totalPages);
        setHasFilters(
          !!(
            searchQuery ||
            (statusQuery && statusQuery !== 'all') ||
            (locationTypeQuery && locationTypeQuery !== 'all')
          )
        );

        if (pageNum === 1) {
          setIsInitialLoading(false);
        }
      } catch (error) {
        console.error('Error fetching locations:', error);
        toastError('Failed to load locations');
      } finally {
        setIsLoading(false);
        setIsLoadingMore(false);
      }
    },
    [locationService]
  );

  const fetchMoreData = useCallback(() => {
    if (page >= totalPages || !hasMore) return;
    const nextPage = page + 1;
    setPage(nextPage);
    fetchLocations(nextPage, search, status, locationType, true);
  }, [page, totalPages, hasMore, fetchLocations, search, status, locationType]);

  // Reset to first page when filters change
  useEffect(() => {
    if (page !== 1) {
      setPage(1);
    } else {
      fetchLocations(1, search, status, locationType);
    }
  }, [search, status, locationType, fetchLocations, page]);

  // Handle page changes for pagination
  useEffect(() => {
    if (page > 1) {
      fetchLocations(page, search, status, locationType, true);
    }
  }, [page, fetchLocations, search, status, locationType]);

  return {
    locationData,
    isLoading,
    isInitialLoading,
    isLoadingMore,
    hasMore,
    totalCount,
    hasFilters,
    fetchMoreData,
  };
}
