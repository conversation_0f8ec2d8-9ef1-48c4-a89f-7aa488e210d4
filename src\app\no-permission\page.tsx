'use client';

import Link from 'next/link';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Routes } from '@/lib/routes';
import { ShieldX, ArrowLeft, Home } from 'lucide-react';
import { useAuth } from '@/contexts/auth-context';

export default function NoPermission() {
  const { signOut, user } = useAuth();

  const handleSignOut = async () => {
    await signOut();
  };

  return (
    <div className="flex items-center justify-center min-h-[calc(100vh-var(--navbar-height))] w-full bg-gradient-to-b from-background to-muted/20">
      <Card className="w-full max-w-lg shadow-lg border-2">
        <CardHeader className="text-center space-y-4 pb-6">
          {/* Icon with background circle */}
          <div className="flex justify-center">
            <div className="relative">
              <div className="absolute inset-0 bg-destructive/10 rounded-full blur-xl animate-pulse" />
              <div className="relative bg-destructive/5 rounded-full p-6">
                <ShieldX className="h-16 w-16 text-primary" />
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <CardTitle className="text-3xl font-bold text-foreground">
              Access Denied
            </CardTitle>
            <CardDescription className="text-base text-muted-foreground">
              You don&apos;t have permission to access this resource.
            </CardDescription>
          </div>
        </CardHeader>

        <CardContent className="space-y-4 pt-2">
          <div className="bg-muted/50 rounded-lg p-4 border border-border">
            <p className="text-sm text-muted-foreground text-center">
              Your permissions may have changed. Please contact your
              administrator if you believe this is an error.
            </p>
          </div>

          <div className="flex flex-col sm:flex-row gap-3 pt-2">
            {user && (
              <Button variant="default" className="w-full sm:flex-1">
                <Link href={Routes.DASHBOARD}>
                  <span className="flex items-center justify-center gap-2">
                    <Home className="h-4 w-4" />
                    Go to Dashboard
                  </span>
                </Link>
              </Button>
            )}
            <Button
              variant={user ? 'outline' : 'default'}
              className="w-full sm:flex-1"
              onClick={handleSignOut}
            >
              <span className="flex items-center justify-center gap-2">
                <ArrowLeft className="h-4 w-4" />
                {user ? 'Sign Out' : 'Sign In'}
              </span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
