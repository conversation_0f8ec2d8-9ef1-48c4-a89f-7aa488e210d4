'use client';

import React, { useEffect, useState } from 'react';
import { useAuth } from '@/contexts/auth-context';
import { useSession } from 'next-auth/react';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { getRoleDisplayName, getUserInitials } from '@/lib/utils';
import { Role } from '@/types/enum';
import { Skeleton } from '@/components/ui/skeleton';
import { Lock, Upload } from 'lucide-react';
import { ChangePasswordDialog } from '@/components/auth/ChangePasswordDialog';
import { ResetPasswordDialog } from '@/components/auth/ResetPasswordDialog';
import { useImageUpload } from '@/hooks/useFileUpload';
import { Loader2Icon } from 'lucide-react';
import { User } from '@/types/user';
import { toastError, toastSuccess } from '@/lib/toast';
import { ApiError } from '@/lib/api-client';

export default function ProfilePage() {
  const { user, loading, isAuthenticated } = useAuth();
  const { update: updateSession } = useSession();
  const [isEditing, setIsEditing] = useState(false);
  const [saveLoading, setSaveLoading] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);

  // File upload hook for avatar
  const {
    selectedFiles,
    fileInputRef,
    openFileDialog,
    handleFileChange,
    isUploading,
    uploadProgress,
    // uploadResult,
    // uploadFiles,
  } = useImageUpload({
    autoUpload: true,
    presignedPath: '/users/me/avatar/presigned',
    completePath: '/users/me/avatar/complete',
    onUploadComplete: async (data: unknown) => {
      try {
        // Update new avatar url to use session token
        const freshUser = data as User;
        setPreviewUrl(freshUser.avatar);
        setFormData(prev => ({ ...prev, avatar: freshUser.avatar }));

        // Simple approach: Update session directly with new avatar
        console.log('Updating user avatar:', freshUser.avatar);

        await updateSession({
          user: {
            ...user,
            avatar: freshUser.avatar,
          },
        });

        console.log('User avatar updated successfully');

        toastSuccess('Avatar uploaded successfully!');
      } catch (error) {
        console.error('Failed to update session:', error);
        toastError(
          'Avatar uploaded but failed to update session. Please refresh the page.'
        );
      }
    },
    onUploadError: error => {
      toastError(`Upload failed: ${error}`);
      // Reset preview url
      setPreviewUrl(null);
    },
    onValidationError: error => {
      toastError(error);
    },
  });

  useEffect(() => {
    if (selectedFiles.length > 0) {
      setPreviewUrl(URL.createObjectURL(selectedFiles[0]));
    }
  }, [selectedFiles]);

  const [formData, setFormData] = useState({
    firstName: user?.firstName || '',
    lastName: user?.lastName || '',
    email: user?.email || '',
    role: user?.role || '',
    avatar: user?.avatar || '',
  });

  // Update form data when user changes
  React.useEffect(() => {
    if (user) {
      setFormData({
        firstName: user.firstName || '',
        lastName: user.lastName || '',
        email: user.email || '',
        role: user.role || '',
        avatar: user.avatar || '',
      });
      // setPreviewUrl(user.avatar || null);
    }
  }, [user]);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSave = async () => {
    setSaveLoading(true);
    try {
      const response = await fetch('/nextapi/profile/update', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to update profile');
      }

      toastSuccess('Profile updated successfully!');
      setIsEditing(false);
      await updateSession({
        user: {
          ...user,
          firstName: formData.firstName,
          lastName: formData.lastName,
          role: formData.role,
        },
      });
    } catch (error) {
      console.error('Error updating profile:', error);
      toastError(
        (error as ApiError).response?.data?.message ??
          'Failed to update profile'
      );
    } finally {
      setSaveLoading(false);
    }
  };

  const handleCancel = () => {
    setFormData({
      firstName: user?.firstName || '',
      lastName: user?.lastName || '',
      email: user?.email || '',
      role: user?.role || '',
      avatar: user?.avatar || '',
    });
    // setPreviewUrl(user?.avatar || null);
    setIsEditing(false);
  };

  // Show loading skeleton while auth is loading
  if (loading) {
    return (
      <div className="container mx-auto py-8 px-4">
        <div className="max-w-2xl mx-auto">
          <Card className="shadow-lg">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="space-y-2">
                  <Skeleton className="h-6 w-32" />
                  <Skeleton className="h-4 w-48" />
                </div>
                <Skeleton className="h-8 w-24" />
              </div>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center space-x-4">
                <Skeleton className="h-20 w-20 rounded-full" />
                <div className="space-y-2">
                  <Skeleton className="h-5 w-32" />
                  <Skeleton className="h-4 w-24" />
                </div>
              </div>
              <div className="grid gap-8">
                {Array.from({ length: 4 }).map((_, index) => (
                  <div key={index} className="space-y-2">
                    <Skeleton className="h-4 w-16" />
                    <Skeleton className="h-10 w-full" />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  if (!isAuthenticated || !user) {
    return (
      <div className="container mx-auto py-8 px-4">
        <div className="max-w-2xl mx-auto">
          <Card>
            <CardHeader>
              <CardTitle>Authentication Required</CardTitle>
              <CardDescription>
                Please sign in to view your profile.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button onClick={() => (window.location.href = '/sign-in')}>
                Sign In
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="max-w-2xl mx-auto space-y-6">
        {/* Profile Settings Card */}
        <Card className="shadow-lg">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Profile Settings</CardTitle>
                <CardDescription>
                  Manage your account settings and preferences.
                </CardDescription>
              </div>
              <div className="flex space-x-2">
                {isEditing ? (
                  <>
                    <Button
                      onClick={handleSave}
                      disabled={saveLoading}
                      size="sm"
                    >
                      {saveLoading ? 'Saving...' : 'Save Changes'}
                    </Button>
                    <Button
                      onClick={handleCancel}
                      variant="outline"
                      size="sm"
                      disabled={saveLoading}
                    >
                      Cancel
                    </Button>
                  </>
                ) : (
                  <Button onClick={() => setIsEditing(true)} size="sm">
                    Edit Profile
                  </Button>
                )}
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Avatar Section */}
            <div className="space-y-4">
              <div className="flex items-center space-x-4">
                <div className="relative rounded-full overflow-hidden">
                  <Avatar className="h-20 w-20">
                    <AvatarImage
                      src={previewUrl || formData.avatar}
                      alt={`${formData.firstName} ${formData.lastName}`}
                    />
                    <AvatarFallback className="text-lg">
                      {getUserInitials(
                        `${formData.firstName} ${formData.lastName}`
                      )}
                    </AvatarFallback>
                  </Avatar>
                  {isEditing && !isUploading && (
                    <div
                      className="absolute bottom-0 right-0 w-full h-[40%] bg-black/50 flex items-center justify-center cursor-pointer hover:bg-black/70 transition-all duration-300"
                      onClick={openFileDialog}
                    >
                      <Upload className="h-3 w-3 text-white" />
                      <Input
                        type="file"
                        accept="image/jpeg,image/jpg,image/png"
                        onChange={handleFileChange}
                        ref={fileInputRef}
                        className="hidden"
                      />
                    </div>
                  )}
                  {isUploading && (
                    <>
                      <div
                        className="absolute bottom-0 right-0 w-full bg-black/80 transition-all duration-300"
                        style={{ height: `${100 - uploadProgress}%` }}
                      ></div>
                      <Loader2Icon className="h-4 w-4 animate-spin text-white absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2" />
                    </>
                  )}
                </div>
                <div>
                  <h3 className="text-lg font-medium">
                    {formData.firstName} {formData.lastName}
                  </h3>
                  <p className="text-sm">{getRoleDisplayName(formData.role)}</p>
                </div>
              </div>
            </div>

            {/* Form Fields */}
            <div className="grid grid-cols-2 gap-8">
              <div className="space-y-2">
                <Label htmlFor="firstName">First Name</Label>
                <Input
                  id="firstName"
                  value={formData.firstName}
                  onChange={e => handleInputChange('firstName', e.target.value)}
                  disabled={!isEditing}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="lastName">Last Name</Label>
                <Input
                  id="lastName"
                  value={formData.lastName}
                  onChange={e => handleInputChange('lastName', e.target.value)}
                  disabled={!isEditing}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  disabled
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="role">Role</Label>
                <Select
                  value={formData.role}
                  onValueChange={value => handleInputChange('role', value)}
                  disabled
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select a role" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={Role.SuperAdmin}>Super Admin</SelectItem>
                    <SelectItem value={Role.Scout}>Scout</SelectItem>
                    <SelectItem value={Role.PropertyOwner}>
                      Property Owner
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Password Management Card */}
        <Card className="shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Lock className="h-5 w-5" />
              <span>Password Management</span>
            </CardTitle>
            <CardDescription>
              Change your password or reset it if you&apos;ve forgotten it.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex space-x-3">
              <ChangePasswordDialog />
              <ResetPasswordDialog defaultEmail={user.email} />
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
