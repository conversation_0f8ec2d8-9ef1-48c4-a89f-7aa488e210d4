'use client';

import { useEffect } from 'react';
import { usePathname } from 'next/navigation';
import { Routes, getPageTitle, getPageDescription } from '@/lib/routes';
import { ScrollArea } from '../ui/scroll-area';

interface PublicLayoutProps {
  children: React.ReactNode;
}

export function PublicLayout({ children }: PublicLayoutProps) {
  const pathname = usePathname();

  useEffect(() => {
    // Find the matching route
    const currentRoute =
      Object.values(Routes).find(route => route === pathname) || Routes.HOME;

    // Update document title
    document.title = getPageTitle(currentRoute);

    // Update meta description
    const metaDescription = document.querySelector('meta[name="description"]');
    if (metaDescription) {
      metaDescription.setAttribute('content', getPageDescription(currentRoute));
    }
  }, [pathname]);

  return (
    <div className="h-screen w-full flex flex-col">
      {/* Main Content */}
      <main className="flex-1 flex items-center justify-center">
        <ScrollArea className="w-full">{children}</ScrollArea>
      </main>
    </div>
  );
}
