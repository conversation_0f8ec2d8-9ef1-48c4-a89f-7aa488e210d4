# API Documentation

This document describes the API patterns and conventions used in the Scoutr application.

## API Architecture

### Route Structure

The application uses a custom `/nextapi/` route prefix for Next.js API routes to avoid conflicts with backend REST API endpoints at `/api/`.

```
/nextapi/auth/*          # NextAuth.js authentication routes
/nextapi/users           # User management endpoints
/nextapi/tags            # Tag management endpoints
/nextapi/locations       # Location management endpoints
```

### HTTP Client

All API calls use a centralized HTTP client (`src/lib/api-client.ts`) that:

- Automatically injects authentication tokens
- Handles token refresh
- Provides consistent error handling
- Supports request/response interceptors

```tsx
import { apiClient } from '@/lib/api-client';

const response = await apiClient.get('/users');
```

## Data Fetching Patterns

### React Query Hooks

All data fetching uses React Query hooks located in `src/hooks/api/`:

#### Tags

```tsx
import {
  useTags,
  useCreateTag,
  useUpdateTag,
  useDeleteTag,
} from '@/hooks/api/useTags';

// Fetch tags with pagination
const { data, isLoading } = useTags({ page: 1, limit: 10 });

// Create tag
const createTag = useCreateTag();
await createTag.mutateAsync({ name: 'New Tag' });

// Update tag
const updateTag = useUpdateTag();
await updateTag.mutateAsync({ id: 1, name: 'Updated Tag' });

// Delete tag
const deleteTag = useDeleteTag();
await deleteTag.mutateAsync(1);
```

#### Locations

```tsx
import { useLocations, useDeleteLocation } from '@/hooks/api/useLocations';

// Fetch locations
const { data, isLoading } = useLocations({ page: 1, limit: 10 });

// Delete location
const deleteLocation = useDeleteLocation();
await deleteLocation.mutateAsync(1);
```

#### Users

```tsx
import {
  useUsers,
  useUserRequests,
  useCreateUser,
  useUpdateUser,
  useDeleteUser,
} from '@/hooks/api/useUsers';

// Fetch active users
const { data, isLoading } = useUsers({ page: 1, limit: 10 });

// Fetch user requests
const { data: requests } = useUserRequests({ page: 1, limit: 10 });

// Create user
const createUser = useCreateUser();
await createUser.mutateAsync({ email: '<EMAIL>', role: 'USER' });
```

### Query Keys

Query keys are centralized in `src/lib/query-keys.ts` using a factory pattern:

```tsx
import { queryKeys } from '@/lib/query-keys';

// Tags query keys
queryKeys.tags.all(); // ['tags']
queryKeys.tags.lists(); // ['tags', 'list']
queryKeys.tags.list({ page: 1 }); // ['tags', 'list', { page: 1 }]
queryKeys.tags.detail(1); // ['tags', 'detail', 1]

// Locations query keys
queryKeys.locations.all(); // ['locations']
queryKeys.locations.lists(); // ['locations', 'list']
```

## Service Layer

API service functions are located in `src/lib/services/`:

### Tag Service

```tsx
import { tagService } from '@/lib/services/tag-service';

// Fetch tags
const tags = await tagService.getTags({ page: 1, limit: 10 });

// Create tag
const newTag = await tagService.createTag({ name: 'New Tag' });

// Update tag
const updated = await tagService.updateTag(1, { name: 'Updated' });

// Delete tag
await tagService.deleteTag(1);

// Bulk delete
await tagService.bulkDeleteTags([1, 2, 3]);
```

### Location Service

```tsx
import { locationService } from '@/lib/services/location-service';

// Fetch locations
const locations = await locationService.getLocations({ page: 1, limit: 10 });

// Get tag options
const tagOptions = await locationService.getTagOptions();

// Delete location
await locationService.deleteLocation(1);
```

### User Service

```tsx
import { userService } from '@/lib/services/user-service';

// Fetch active users
const users = await userService.getUsers({ page: 1, limit: 10 });

// Fetch user requests
const requests = await userService.getUserRequests({ page: 1, limit: 10 });

// Create user
const newUser = await userService.createUser({ email: '<EMAIL>' });

// Update user
const updated = await userService.updateUser(1, { name: 'Updated' });

// Delete user
await userService.deleteUser(1);

// Update user status
await userService.updateUserStatus(1, 'ACTIVE');

// Accept user
await userService.acceptUser(1);

// Reject user
await userService.rejectUser(1);
```

## Error Handling

### API Errors

API errors are handled consistently:

```tsx
import { ApiError } from '@/lib/api-client';

try {
  await apiClient.get('/users');
} catch (error) {
  if (error instanceof ApiError) {
    console.error(error.message);
    console.error(error.statusCode);
  }
}
```

### React Query Error Handling

React Query hooks provide error states:

```tsx
const { data, error, isLoading } = useTags();

if (error) {
  // Handle error
  console.error(error.message);
}
```

## Authentication

### Token Management

Authentication tokens are automatically managed by the API client:

- Tokens are stored in session cookies
- Tokens are automatically injected into requests
- Token refresh is handled automatically
- Failed requests trigger token refresh

### Protected Endpoints

All API endpoints require authentication except:

- `/nextapi/auth/*` - Authentication endpoints
- Public endpoints (if any)

## Pagination

Pagination follows a consistent pattern:

```tsx
interface PaginationParams {
  page?: number;
  limit?: number;
}

interface PaginatedResponse<T> {
  data: T[];
  meta: {
    totalPages: number;
    totalItems: number;
    page: number;
    limit: number;
  };
}
```

## Best Practices

1. **Always use React Query hooks** for data fetching
2. **Use service functions** for API calls, not direct fetch
3. **Handle errors** consistently using try/catch or React Query error states
4. **Use query keys** from `queryKeys` factory
5. **Invalidate queries** after mutations to keep data fresh
6. **Use optimistic updates** for better UX
7. **Handle loading states** using React Query's `isLoading` or `isPending`

## Examples

### Complete Example: Tag Management

```tsx
'use client';

import {
  useTags,
  useCreateTag,
  useUpdateTag,
  useDeleteTag,
} from '@/hooks/api/useTags';
import { useState } from 'react';

export function TagsContainer() {
  const [page, setPage] = useState(1);
  const { data, isLoading, error } = useTags({ page, limit: 10 });

  const createTag = useCreateTag();
  const updateTag = useUpdateTag();
  const deleteTag = useDeleteTag();

  const handleCreate = async (name: string) => {
    try {
      await createTag.mutateAsync({ name });
      // Query is automatically invalidated and refetched
    } catch (error) {
      console.error('Failed to create tag:', error);
    }
  };

  const handleUpdate = async (id: number, name: string) => {
    try {
      await updateTag.mutateAsync({ id, name });
    } catch (error) {
      console.error('Failed to update tag:', error);
    }
  };

  const handleDelete = async (id: number) => {
    try {
      await deleteTag.mutateAsync(id);
    } catch (error) {
      console.error('Failed to delete tag:', error);
    }
  };

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <div>
      {data?.data.map(tag => (
        <div key={tag.id}>
          {tag.name}
          <button onClick={() => handleDelete(tag.id)}>Delete</button>
        </div>
      ))}
    </div>
  );
}
```
