'use client';

import * as React from 'react';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { DateRangePicker } from '../DateRangePicker';
import ConfirmDialog from '@/components/shared/ConfirmDialog';
import { Reference } from '@/types';
import { toastError, toastSuccess } from '@/lib/toast';
import { referenceService } from '@/lib/services';
import { handleError } from '@/lib/error-handler';
import { Loader2 } from 'lucide-react';

// Zod validation schema
const referenceSchema = z.object({
  projectName: z.string().min(1, 'Project name is required').trim(),
  productionHouse: z.string().min(1, 'Production house is required'),
  shootDates: z
    .object({
      from: z.date(),
      to: z.date().optional(),
    })
    .refine(dates => dates.from, 'Shoot dates are required'),
  internalNotes: z.string().optional(),
});

type ReferenceFormData = z.infer<typeof referenceSchema>;

interface AddReferenceModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: (response: Reference) => void;
  productionHouseOptions: {
    id: string;
    name: string;
  }[];
  reference?: Reference;
  isEdit?: boolean;
}

export default function AddReferenceModal({
  open,
  onOpenChange,
  onSuccess,
  productionHouseOptions,
  reference,
  isEdit,
}: AddReferenceModalProps) {
  const [isDiscardReferenceDialogOpen, setIsDiscardReferenceDialogOpen] =
    useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);

  const form = useForm<ReferenceFormData>({
    resolver: zodResolver(referenceSchema),
    mode: 'onChange',
    defaultValues: {
      projectName: reference?.projectName || '',
      productionHouse: reference?.productionHouse.name || '',
      shootDates: reference
        ? {
            from: new Date(reference.shootDateStart),
            to: reference.shootDateEnd
              ? new Date(reference.shootDateEnd)
              : undefined,
          }
        : undefined,
      internalNotes: reference?.internalNotes || '',
    },
  });

  const onSubmit = async (data: ReferenceFormData) => {
    try {
      setLoading(true);
      const { projectName, productionHouse, shootDates, internalNotes } = data;
      const response = await referenceService.createReference({
        projectName,
        productionHouseId: productionHouse,
        internalNotes,
        shootDateStart: shootDates.from.toISOString(),
        shootDateEnd: shootDates.to
          ? shootDates.to.toISOString()
          : shootDates.from.toISOString(),
      });
      toastSuccess('List created successfully.');
      onSuccess(response);
    } catch (error) {
      const { message } = handleError(
        error,
        'Failed to create reference lists.'
      );
      toastError(message);
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    form.reset();
  };

  const handleOpenChange = (open: boolean) => {
    onOpenChange(open);
  };

  const handleDiscard = () => {
    resetForm();
    onOpenChange(false);
  };

  return (
    <>
      <Dialog open={open} onOpenChange={handleOpenChange}>
        <DialogContent className="sm:max-w-[20.5625rem]">
          <DialogHeader>
            <DialogTitle>
              {isEdit ? 'Edit reference list' : 'New reference list'}
            </DialogTitle>
            <DialogDescription>
              {isEdit
                ? 'Update your reference list details.'
                : 'Add the basics to create your list.'}
            </DialogDescription>
          </DialogHeader>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="projectName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      {isEdit ? 'List name' : 'Project name'}
                    </FormLabel>
                    <FormControl>
                      <Input placeholder="Enter the project name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="productionHouse"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Production house</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger className="w-full text-black">
                          <SelectValue placeholder="Select the production house" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {productionHouseOptions.map((ph, index) => (
                          <SelectItem key={index} value={ph.id}>
                            {ph.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="shootDates"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Shoot dates</FormLabel>
                    <FormControl>
                      <DateRangePicker
                        className="text-black"
                        value={field.value}
                        onDateRangeChange={field.onChange}
                        placeholder="Select dates"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {!isEdit && (
                <FormField
                  control={form.control}
                  name="internalNotes"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Internal notes (optional)</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Add any internal notes..."
                          className="min-h-[5rem] text-black"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}

              <DialogFooter className="mt-6">
                <Button
                  onClick={() => {
                    const hasFilled = Object.values(form.getValues()).some(
                      field => !!field
                    );
                    if (hasFilled && !isEdit) {
                      setIsDiscardReferenceDialogOpen(true);
                    } else {
                      onOpenChange(false);
                    }
                  }}
                  type="button"
                  variant="ghost"
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={!form.formState.isValid}>
                  {isEdit ? 'Save changes' : 'Create list'}
                  {loading && <Loader2 className="w-4 h-4 animate-spin" />}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
      <ConfirmDialog
        open={isDiscardReferenceDialogOpen}
        onOpenChange={setIsDiscardReferenceDialogOpen}
        title="Discard this reference list?"
        description="You’ve entered some details. If you cancel now, your changes will be lost."
        confirmText="Discard"
        cancelText="Keep editing"
        onConfirm={handleDiscard}
        onCancel={() => setIsDiscardReferenceDialogOpen(false)}
      />
    </>
  );
}
