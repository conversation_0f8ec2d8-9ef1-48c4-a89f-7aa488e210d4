import { Skeleton } from '@/components/ui/skeleton';

export default function Loading() {
  return (
    <div className="w-full flex justify-center">
      <div className="space-y-11 w-full xl:w-[70.5rem] py-11 xl:px-0">
        {/* Header section */}
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-4">
          {/* Back button and title */}
          <div className="inline-flex items-center gap-3">
            <Skeleton className="h-5 w-5 rounded-full" /> {/* ArrowLeft icon */}
            <div className="flex flex-col justify-start gap-1">
              <Skeleton className="h-8 w-48" /> {/* "All Photos" heading */}
              <Skeleton className="h-5 w-64" />{' '}
              {/* "Downtown Creative Loft" subtitle */}
            </div>
          </div>
          {/* Add to List button */}
          <Skeleton className="w-full sm:w-[9.6875rem] h-12" />
        </div>

        {/* Photo grid skeleton */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.from({ length: 12 }).map((_, index) => (
            <div
              key={index}
              className="relative border rounded-lg shadow-sm bg-white flex flex-col"
            >
              {/* Image placeholder */}
              <div className="relative bg-gray-200 flex items-center justify-center rounded-t-lg aspect-square">
                <Skeleton className="h-full w-full rounded-t-lg" />
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
