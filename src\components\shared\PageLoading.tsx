import { Card, CardContent } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { LoadingSpinner } from './LoadingSpinner';

interface PageLoadingProps {
  /**
   * Show spinner instead of skeleton
   * @default false
   */
  showSpinner?: boolean;
  /**
   * Loading message
   */
  message?: string;
}

/**
 * Standardized page loading component
 */
export function PageLoading({
  showSpinner = false,
  message,
}: PageLoadingProps) {
  if (showSpinner) {
    return (
      <div className="flex items-center justify-center min-h-[calc(100vh-4rem)] w-full">
        <Card className="w-full max-w-md">
          <CardContent className="flex flex-col items-center justify-center p-8 space-y-4">
            <LoadingSpinner size="lg" text={message} />
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="flex items-center justify-center min-h-[calc(100vh-4rem)] w-full">
      <Card className="w-full max-w-md">
        <CardContent className="flex flex-col items-center justify-center p-8 space-y-4">
          <div className="flex items-center justify-center">
            <LoadingSpinner size="lg" />
          </div>
          <div className="text-center space-y-2">
            <Skeleton className="h-6 w-32 mx-auto" />
            <Skeleton className="h-4 w-48 mx-auto" />
          </div>
          <div className="flex space-x-2">
            <Skeleton className="h-2 w-2 rounded-full" />
            <Skeleton className="h-2 w-2 rounded-full" />
            <Skeleton className="h-2 w-2 rounded-full" />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
