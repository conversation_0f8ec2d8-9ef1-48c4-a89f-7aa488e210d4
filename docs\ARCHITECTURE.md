# Architecture Documentation

This document describes the architecture decisions and patterns used in the Scoutr application.

## Overview

Scoutr is built with Next.js 15 using the App Router, TypeScript, and React Query for state management. The application follows a feature-based architecture with clear separation of concerns.

## Core Principles

1. **Type Safety First** - TypeScript strict mode enabled, minimal use of `any`
2. **Component Composition** - Small, focused components that compose together
3. **Server State Management** - React Query for all server state
4. **Form Validation** - React Hook Form + Zod for type-safe forms
5. **Consistent Patterns** - Standardized patterns for common operations

## Architecture Layers

### 1. Presentation Layer

**Location**: `src/components/`

- **Feature Components** (`features/`) - Domain-specific components
- **Shared Components** (`shared/`) - Reusable UI components
- **Layout Components** (`layout/`) - Page layout components
- **UI Components** (`ui/`) - Base UI primitives (Shadcn)

### 2. Data Layer

**Location**: `src/hooks/api/` and `src/lib/services/`

- **React Query Hooks** - Data fetching and mutations
- **Service Functions** - API call abstractions
- **Query Keys** - Centralized query key management

### 3. Business Logic Layer

**Location**: `src/lib/`

- **Validations** - Zod schemas for form validation
- **Services** - Business logic and API calls
- **Utilities** - Helper functions

### 4. Type Definitions

**Location**: `src/types/` and `src/lib/types/`

- **Domain Types** - User, Tag, Location, etc.
- **Common Types** - Shared type definitions
- **API Types** - Request/response types

## Data Flow

```
User Action
    ↓
Component (UI)
    ↓
React Query Hook (useTags, useCreateTag, etc.)
    ↓
Service Function (tagService.createTag)
    ↓
API Client (apiClient.post)
    ↓
Backend API
    ↓
Response flows back up
    ↓
React Query updates cache
    ↓
Component re-renders with new data
```

## State Management

### Server State (React Query)

All server state is managed by React Query:

- **Queries** - Data fetching (GET requests)
- **Mutations** - Data modification (POST, PUT, DELETE)
- **Cache** - Automatic caching and invalidation
- **Synchronization** - Background refetching

### Client State (React)

Local component state for:

- UI state (modals, dropdowns)
- Form state (handled by React Hook Form)
- Temporary state (loading indicators)

### Global State (Context)

- **Auth Context** - User authentication state
- **Navbar Context** - Dynamic navbar items

## Component Patterns

### Container/Presenter Pattern

```tsx
// Container (orchestrates data fetching)
export function TagsContainer() {
  const { data, isLoading } = useTags();
  return <TagsTable data={data} loading={isLoading} />;
}

// Presenter (renders UI)
export function TagsTable({ data, loading }) {
  // Pure presentation logic
}
```

### Compound Components

```tsx
<Dialog>
  <DialogHeader>
    <DialogTitle>Title</DialogTitle>
  </DialogHeader>
  <DialogContent>Content</DialogContent>
  <DialogFooter>Footer</DialogFooter>
</Dialog>
```

## Form Patterns

All forms follow this pattern:

1. **Validation Schema** - Zod schema in `src/lib/validations/`
2. **React Hook Form** - Form state management
3. **Type Inference** - TypeScript types from Zod schema
4. **Error Handling** - Automatic error display

```tsx
const formSchema = z.object({
  name: z.string().min(1, 'Name is required'),
});

type FormData = z.infer<typeof formSchema>;

const form = useForm<FormData>({
  resolver: zodResolver(formSchema),
});
```

## API Integration

### Service Layer

Service functions abstract API calls:

```tsx
// Service function
export async function createTag(data: CreateTagDto) {
  return apiClient.post('/tags', data);
}

// React Query hook
export function useCreateTag() {
  return useMutation({
    mutationFn: createTag,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.tags.all() });
    },
  });
}
```

### Error Handling

Consistent error handling:

```tsx
try {
  await createTag.mutateAsync(data);
  toast.success('Tag created');
} catch (error) {
  toast.error(error.message || 'Failed to create tag');
}
```

## Routing

### Next.js App Router

- **File-based routing** - Routes defined by file structure
- **Route Groups** - `(auth)` for route organization
- **Dynamic Routes** - `[id]` for dynamic segments
- **Middleware** - Route protection and redirects

### Route Protection

Middleware (`src/middleware.ts`) handles:

- Authentication checks
- Role-based access control
- Public/private route handling

## Performance Optimizations

1. **Code Splitting** - Lazy loading for large components
2. **React Query Caching** - Reduces API calls
3. **Image Optimization** - Next.js Image component
4. **Debouncing** - Search and input debouncing
5. **Memoization** - useMemo/useCallback for expensive operations

## Testing Strategy

(To be implemented)

- **Unit Tests** - Utility functions and hooks
- **Integration Tests** - Component integration
- **E2E Tests** - Critical user flows

## Security Considerations

1. **Authentication** - NextAuth.js with secure sessions
2. **Authorization** - Role-based access control
3. **Input Validation** - Zod schemas prevent invalid data
4. **XSS Protection** - React's built-in escaping
5. **CSRF Protection** - NextAuth.js handles CSRF tokens

## Deployment

### Build Process

1. Type checking (`tsc --noEmit`)
2. Linting (`eslint`)
3. Formatting (`prettier`)
4. Build (`next build`)

### Environment Variables

- `NEXTAUTH_URL` - Application URL
- `NEXTAUTH_SECRET` - Session encryption
- `NEXT_PUBLIC_GOOGLE_MAPS_API_KEY` - Maps API key
- `NEXT_PUBLIC_API_URL` - Backend API URL

## Future Improvements

1. **Testing** - Add comprehensive test coverage
2. **Storybook** - Component documentation
3. **Performance Monitoring** - Real User Monitoring (RUM)
4. **Accessibility** - WCAG compliance improvements
5. **Internationalization** - Multi-language support
