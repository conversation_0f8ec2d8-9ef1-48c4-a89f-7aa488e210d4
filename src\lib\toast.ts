import React from 'react';
import { toast, ExternalToast } from 'sonner';
import { CheckCircleIcon, InfoIcon } from './icons';
import { XCircleIcon } from 'lucide-react';
import { Loader2 } from 'lucide-react';
import { cn } from './utils';

interface ToastOptions extends ExternalToast {
  iconClassName?: string;
}

export function toastSuccess(message: string, options?: ToastOptions) {
  toast.success(message, {
    ...options,
    icon: React.createElement(CheckCircleIcon, {
      className: cn('size-6 text-green-500', options?.iconClassName),
    }),
  });
}

export function toastError(
  message: string | React.ReactNode,
  options?: ToastOptions
) {
  const content =
    typeof message === 'string' && /<\/?[a-z][\s\S]*>/i.test(message)
      ? React.createElement('div', {
          dangerouslySetInnerHTML: { __html: message },
        })
      : message;

  toast.error(content, {
    ...options,
    icon: React.createElement(InfoIcon, {
      className: cn('size-6 text-red-500', options?.iconClassName),
    }),
  });
}

export function toastLoading(message: string, options?: ToastOptions) {
  return toast(message, {
    ...options,
    icon: React.createElement(Loader2, {
      className: cn(
        'size-6 text-blue-500 animate-spin',
        options?.iconClassName
      ),
    }),
  });
}

export function toastDismiss(id: string | number) {
  toast.dismiss(id);
}

export function toastWarning(message: string, options?: ToastOptions) {
  toast.warning(message, {
    ...options,
    icon: React.createElement(XCircleIcon, {
      className: cn('size-6 text-yellow-500', options?.iconClassName),
    }),
  });
}

export function toastInfo(message: string, options?: ToastOptions) {
  toast.info(message, {
    ...options,
    icon: React.createElement(InfoIcon, {
      className: cn('size-6 text-blue-500', options?.iconClassName),
    }),
  });
}
