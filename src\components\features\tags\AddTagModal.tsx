'use client';

import * as React from 'react';
import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import ConfirmDialog from '@/components/shared/ConfirmDialog';
import { Loader2 } from 'lucide-react';
import { createTagSchema, CreateTagFormData } from '@/lib/validations';
import { PropertyType } from '@/types/enum';
import { PROPERTY_CATEGORY, TAG_TYPE_OPTIONS } from '@/types/constant';
import { useCreateTag } from '@/hooks/api/useTags';
import { formatCategoryLabel } from './TagsForm';

interface AddTagModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const colorOptions = [
  { value: 'red-500', label: 'Red' },
  { value: 'green-500', label: 'Green' },
  { value: 'blue-500', label: 'Blue' },
  { value: 'yellow-500', label: 'Yellow' },
  { value: 'orange-500', label: 'Orange' },
  { value: 'purple-500', label: 'Purple' },
  { value: 'pink-500', label: 'Pink' },
  { value: 'teal-500', label: 'Teal' },
  { value: 'cyan-500', label: 'Cyan' },
  { value: 'gray-500', label: 'Gray' },
  { value: 'black', label: 'Black' },
];

export default function AddTagModal({ open, onOpenChange }: AddTagModalProps) {
  const [isDiscardTagDialogOpen, setIsDiscardTagDialogOpen] = useState(false);
  // Mutations
  const createTagMutation = useCreateTag();
  const form = useForm<CreateTagFormData>({
    resolver: zodResolver(createTagSchema),
    defaultValues: {
      name: '',
      propertyType: undefined,
      type: '',
    },
  });

  const selectedType = form.watch('propertyType');

  const categoryOptions =
    selectedType && PROPERTY_CATEGORY[selectedType as PropertyType]
      ? PROPERTY_CATEGORY[selectedType as PropertyType]
      : [];

  // Reset form when modal opens/closes
  useEffect(() => {
    if (!open) {
      form.reset();
    }
  }, [open, form]);

  const hasUnsavedChanges = form.formState.isDirty;

  const handleSubmit = (data: CreateTagFormData) => {
    handleAddTag(data);
  };

  const handleAddTag = async (tag: CreateTagFormData) => {
    try {
      await createTagMutation.mutateAsync({
        name: tag.name,
        propertyType: tag.propertyType,
        type: tag.type,
      });
      onOpenChange(false);
    } catch {
      // Error handling is done in the mutation
    }
  };

  const handleDiscardTag = () => {
    form.reset();
    setIsDiscardTagDialogOpen(false);
    onOpenChange(false);
  };

  const handleCancel = () => {
    if (hasUnsavedChanges) {
      setIsDiscardTagDialogOpen(true);
    } else {
      onOpenChange(false);
    }
  };

  return (
    <>
      <Dialog open={open} onOpenChange={handleCancel}>
        <DialogContent className="sm:max-w-[20.5625rem]">
          <DialogHeader>
            <DialogTitle>Create Tag</DialogTitle>
            <DialogDescription>
              Add a tag to better describe and organize your locations.
            </DialogDescription>
          </DialogHeader>
          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(handleSubmit)}
              className="space-y-4"
            >
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tag name</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter tag name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="propertyType"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Property type</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger className="w-full">
                          <SelectValue placeholder="Select property type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {Object.entries(TAG_TYPE_OPTIONS).map(
                          ([typeValue, label]) => (
                            <SelectItem
                              className="capitalize"
                              key={typeValue}
                              value={typeValue}
                            >
                              <span className="capitalize">{label}</span>
                            </SelectItem>
                          )
                        )}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Category</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      value={field.value}
                      disabled={!selectedType}
                    >
                      <FormControl>
                        <SelectTrigger className="w-full">
                          <SelectValue placeholder="Select category" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {(categoryOptions ?? []).map(category => (
                          <SelectItem
                            className="capitalize"
                            key={category}
                            value={category}
                          >
                            <span className="capitalize">
                              {formatCategoryLabel(category)}
                            </span>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                    <DialogDescription>
                      The category will vary according to the property type
                      selected.
                    </DialogDescription>
                  </FormItem>
                )}
              />

              <DialogFooter className="mt-6">
                <Button
                  onClick={handleCancel}
                  type="button"
                  variant="link"
                  className="hover:no-underline"
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={
                    createTagMutation.isPending || !form.formState.isValid
                  }
                >
                  Create tag
                  {createTagMutation.isPending && (
                    <Loader2 className="ml-2 size-4 animate-spin" />
                  )}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
      <ConfirmDialog
        open={isDiscardTagDialogOpen}
        onOpenChange={setIsDiscardTagDialogOpen}
        title="Discard tag?"
        description="You've started filling in this tag's details. If you cancel now, the information will be lost."
        confirmText="Remove"
        cancelText="Keep tag"
        onConfirm={handleDiscardTag}
        onCancel={() => setIsDiscardTagDialogOpen(false)}
      />
    </>
  );
}
