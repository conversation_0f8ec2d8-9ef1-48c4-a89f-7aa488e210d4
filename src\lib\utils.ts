import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';
import {
  format,
  differenceInSeconds,
  differenceInMinutes,
  differenceInHours,
  differenceInDays,
} from 'date-fns';
import { Role } from '@/types/enum';

// Re-export UUID utilities
export {
  generateUUID,
  generateShortUUID,
  generateUUIDWithoutHyphens,
  generateMultipleUUIDs,
  isValidUUID,
} from './utils/uuid';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Generates user initials from a full name
 * @param name - The full name of the user
 * @returns The initials (up to 2 characters)
 * @example
 * getUserInitials('<PERSON>') // Returns 'JD'
 * getUserInitials('Alice') // Returns 'A'
 * getUserInitials('<PERSON>') // Returns 'MJ'
 */
export function getUserInitials(name: string): string {
  if (!name || typeof name !== 'string') {
    return 'U';
  }

  return name
    .split(' ')
    .map(word => word[0])
    .join('')
    .toUpperCase()
    .slice(0, 2);
}

export function formatDateRange(from: Date, to: Date) {
  if (
    from.getMonth() !== to.getMonth() ||
    from.getFullYear() !== to.getFullYear()
  ) {
    return `${format(from, 'LLL d, yyyy')} – ${format(to, 'LLL d, yyyy')}`;
  }

  return `${format(from, 'LLL d')}–${format(to, 'd, yyyy')}`;
}

export function getDisplayText(str: string): string {
  if (!str) return '';

  const s = str.toLowerCase().replace(/_/g, ' ');
  return s.charAt(0).toUpperCase() + s.slice(1);
}

/**
 * Mapping of Role enum values to their display names
 */
export const ROLE_DISPLAY_NAMES: Record<Role, string> = {
  [Role.SuperAdmin]: 'Super Admin',
  [Role.ProductionAdmin]: 'Production Admin',
  [Role.Scout]: 'Scout',
  [Role.PropertyOwner]: 'Property Owner',
};

/**
 * Get the display name for a role
 * @param role - The role enum value or string
 * @returns The display name for the role, or the original value if not found
 */
export function getRoleDisplayName(role: string | Role): string {
  return ROLE_DISPLAY_NAMES[role as Role] || role;
}

/**
 * Fetch a remote image URL and convert it into a File instance.
 */
export async function fileFromImageUrl(
  url: string,
  filename?: string
): Promise<File> {
  const res = await fetch(url, { cache: 'no-store' });
  if (!res.ok) throw new Error(`Failed to fetch image: ${res.status}`);
  const blob = await res.blob();
  const name = filename ?? url.split('/').pop() ?? 'image.jpg';
  const type = blob.type || 'image/jpeg';
  return new File([blob], name, { type });
}

/**
 * Converts a datetime to a relative time string from the current time.
 * @param date - The date to convert (Date object or ISO string)
 * @returns Formatted relative time string
 * @example
 * getRelativeTime(new Date(Date.now() - 30 * 1000)) // Returns '30 seconds ago'
 * getRelativeTime(new Date(Date.now() - 5 * 60 * 1000)) // Returns '5 mins ago'
 * getRelativeTime(new Date(Date.now() - 2 * 60 * 60 * 1000)) // Returns '2h ago'
 * getRelativeTime(new Date(Date.now() - 25 * 60 * 60 * 1000)) // Returns '1 day ago'
 * getRelativeTime(new Date(Date.now() - 3 * 24 * 60 * 60 * 1000)) // Returns '3 days ago'
 */
export function getRelativeTime(date: Date | string): string {
  const now = new Date();
  const targetDate = typeof date === 'string' ? new Date(date) : date;

  if (isNaN(targetDate.getTime())) {
    return 'Invalid date';
  }

  const secondsDiff = differenceInSeconds(now, targetDate);
  const minutesDiff = differenceInMinutes(now, targetDate);
  const hoursDiff = differenceInHours(now, targetDate);
  const daysDiff = differenceInDays(now, targetDate);

  // Less than 1 hour: display seconds or minutes
  if (hoursDiff < 1) {
    if (minutesDiff < 1) {
      return `${secondsDiff} ${secondsDiff === 1 ? 'second' : 'seconds'} ago`;
    }
    return `${minutesDiff} ${minutesDiff === 1 ? 'min' : 'mins'} ago`;
  }

  // Less than 24 hours: display hours
  if (hoursDiff < 24) {
    return `${hoursDiff}h ago`;
  }

  // 24 hours or more: display days
  if (daysDiff === 1) {
    return '1 day ago';
  }
  return `${daysDiff} days ago`;
}
