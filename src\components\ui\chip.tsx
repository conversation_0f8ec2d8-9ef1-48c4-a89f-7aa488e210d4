'use client';

import * as React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';

import { cn } from '@/lib/utils';

const chipVariants = cva(
  'inline-flex items-center gap-2 rounded-lg border text-primary transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring/50 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 select-none',
  {
    variants: {
      type: {
        default: 'justify-start',
        notification: 'justify-between w-full',
      },
      size: {
        default: 'px-4 py-3 text-sm leading-5',
        small: 'px-3 py-2 text-xs leading-4',
      },
      selected: {
        false: 'border border-[#ceb4d6]',
        true: 'border-2 border-[#6b1f85]',
      },
    },
    defaultVariants: {
      type: 'default',
      size: 'default',
      selected: false,
    },
  }
);

interface ChipProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  icon?: React.ReactNode;
  badge?: React.ReactNode;
  chipType?: NonNullable<VariantProps<typeof chipVariants>['type']>;
  chipSize?: NonNullable<VariantProps<typeof chipVariants>['size']>;
  selected?: boolean;
}

function Chip({
  icon,
  children,
  className,
  chipType = 'default',
  chipSize = 'default',
  selected = false,
  badge,
  ...props
}: ChipProps) {
  const iconSizeClass = chipSize === 'small' ? 'h-5 w-5' : 'h-6 w-6';
  const badgeClass =
    chipSize === 'small' ? 'text-[11px] px-1.5 py-0.5' : 'text-xs px-2 py-0.5';

  return (
    <button
      type="button"
      data-slot="chip"
      className={cn(
        chipVariants({ type: chipType, size: chipSize, selected }),
        'flex gap-1 items-center justify-center',
        className
      )}
      aria-pressed={selected ? true : undefined}
      {...props}
    >
      {icon && (
        <span
          className={cn(
            'flex items-center justify-center text-primary-400',
            iconSizeClass
          )}
        >
          {icon}
        </span>
      )}
      <span className="truncate">{children}</span>
      {badge && (
        <span
          className={cn(
            'ml-auto rounded-full bg-primary-50 font-semibold text-primary',
            badgeClass
          )}
        >
          {badge}
        </span>
      )}
    </button>
  );
}

export { Chip };
