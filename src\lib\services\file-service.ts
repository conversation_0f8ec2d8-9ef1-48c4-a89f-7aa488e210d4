import { httpClient } from '../http-client';
import type {
  PresignedUrlRequest,
  PresignedUrlResponse,
  UploadResult,
  UploadProgress,
  UploadProgressCallback,
  FileCompleteRequest,
  FileCompleteResponse,
} from '@/types/file';

export interface UploadWithCancel {
  uploadPromise: Promise<UploadResult>;
  cancel: () => void;
}

export class FileService {
  private allowedTypes: string[];
  private maxSize: number;
  private presignedPath: string;
  private completePath: string;

  constructor(
    presignedPath: string = '/uploads/presigned',
    completePath: string = '/uploads/complete',
    allowedTypes: string[] = ['image/jpeg', 'image/jpg', 'image/png'],
    maxSize: number = 10 * 1024 * 1024
  ) {
    this.allowedTypes = allowedTypes;
    this.maxSize = maxSize;
    this.presignedPath = presignedPath;
    this.completePath = completePath;
  }

  /**
   * Normalize content type - convert jpg to jpeg for consistency
   */
  private normalizeFileType(file: File): File {
    // Force to image/jpeg if it's jfif or browser reports something else
    if (file.type !== 'image/jpeg' && file.name.endsWith('.jfif')) {
      const blob = file.slice(0, file.size, 'image/jpeg');
      return new File([blob], file.name.replace(/\.jfif$/, '.jpg'), {
        type: 'image/jpeg',
      });
    }
    return file;
  }

  /**
   * Get a presigned URL for uploading a file to S3
   */
  async getPresignedUrl(
    request: PresignedUrlRequest
  ): Promise<PresignedUrlResponse> {
    const response = await httpClient.post<PresignedUrlResponse>(
      this.presignedPath,
      {
        fileName: request.fileName,
        contentType: request.fileType,
      }
    );
    return response;
  }

  /**
   * Upload a file to S3 using a presigned URL
   */
  async uploadFileToS3(
    presignedData: PresignedUrlResponse,
    file: File,
    onProgress?: UploadProgressCallback,
    abortController?: AbortController
  ): Promise<UploadResult> {
    try {
      const formData = new FormData();

      // Add all the fields from the presigned response
      Object.entries(presignedData.fields).forEach(([key, value]) => {
        formData.append(key, String(value));
      });
      const normalizedFile = this.normalizeFileType(file);
      formData.append('Content-Type', normalizedFile.type);

      // Add the file last - this is crucial for S3 presigned POST
      // The file must be added after all other fields
      formData.append('file', normalizedFile);

      const xhr = new XMLHttpRequest();

      // Set up abort controller
      if (abortController) {
        abortController.signal.addEventListener('abort', () => {
          xhr.abort();
        });
      }

      // Set up progress tracking
      if (onProgress) {
        xhr.upload.addEventListener('progress', event => {
          if (event.lengthComputable) {
            const progress: UploadProgress = {
              loaded: event.loaded,
              total: event.total,
              percentage: Math.round((event.loaded / event.total) * 100),
            };
            // console.log('Upload progress:', progress);
            onProgress(progress);
          }
        });
      }

      // Create a promise to handle the upload
      const uploadPromise = new Promise<Response>((resolve, reject) => {
        xhr.addEventListener('load', () => {
          if (xhr.status >= 200 && xhr.status < 300) {
            // For 204 No Content, create a response without body
            if (xhr.status === 204) {
              const response = new Response(null, {
                status: xhr.status,
                statusText: xhr.statusText,
              });
              resolve(response);
            } else {
              // For other success statuses, include response text
              const response = new Response(xhr.responseText, {
                status: xhr.status,
                statusText: xhr.statusText,
              });
              resolve(response);
            }
          } else {
            reject(
              new Error(
                `Upload failed: ${xhr.status} ${xhr.statusText} - ${xhr.responseText}`
              )
            );
          }
        });

        xhr.addEventListener('error', () => {
          reject(new Error('Upload failed: Network error'));
        });

        xhr.addEventListener('abort', () => {
          reject(new Error('Upload cancelled'));
        });
      });

      // Start the upload
      xhr.open('POST', presignedData.url);
      xhr.send(formData);

      const response = await uploadPromise;

      if (!response.ok) {
        throw new Error(`Upload failed: ${response.statusText}`);
      }

      // Complete the upload to get the final URL
      const completeResponse = await this.completeFileUpload({
        key: presignedData.key,
      });

      return {
        success: true,
        data: completeResponse,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Upload failed',
      };
    }
  }

  /**
   * Complete file upload process: get presigned URL and upload file
   * Note: File validation should be done before calling this method
   */
  async uploadFile(
    file: File,
    onProgress?: UploadProgressCallback
  ): Promise<UploadResult> {
    try {
      // Basic validation (additional validation should be done in the hook)
      if (file.size === 0) {
        return {
          success: false,
          error: 'File is empty',
        };
      }

      // Get presigned URL
      const presignedResponse = await this.getPresignedUrl({
        fileName: file.name,
        fileType: file.type,
        fileSize: file.size,
      });

      // Upload file to S3
      const uploadResult = await this.uploadFileToS3(
        presignedResponse,
        file,
        onProgress
      );

      return uploadResult;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Upload failed',
      };
    }
  }

  /**
   * Upload file with cancel functionality
   * Returns both the upload promise and a cancel function
   */
  uploadFileWithCancel(
    file: File,
    onProgress?: UploadProgressCallback
  ): UploadWithCancel {
    const abortController = new AbortController();

    const uploadPromise = (async (): Promise<UploadResult> => {
      try {
        // Basic validation (additional validation should be done in the hook)
        if (file.size === 0) {
          return {
            success: false,
            error: 'File is empty',
          };
        }

        // Get presigned URL
        const presignedResponse = await this.getPresignedUrl({
          fileName: file.name,
          fileType: file.type,
          fileSize: file.size,
        });

        // Upload file to S3
        const uploadResult = await this.uploadFileToS3(
          presignedResponse,
          file,
          onProgress,
          abortController
        );

        return uploadResult;
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Upload failed',
        };
      }
    })();

    return {
      uploadPromise,
      cancel: () => abortController.abort(),
    };
  }

  /**
   * Upload multiple files
   */
  async uploadMultipleFiles(
    files: File[],
    onProgress?: (fileIndex: number, progress: UploadProgress) => void
  ): Promise<UploadResult[]> {
    const uploadPromises = files.map((file, index) =>
      this.uploadFile(
        file,
        onProgress ? progress => onProgress(index, progress) : undefined
      )
    );

    return Promise.all(uploadPromises);
  }

  /**
   * Upload multiple files with cancel functionality
   * Returns both the upload promises and cancel functions for each file
   */
  uploadMultipleFilesWithCancel(
    files: File[],
    onProgress?: (fileIndex: number, progress: UploadProgress) => void
  ): {
    uploadPromises: Promise<UploadResult>[];
    cancelAll: () => void;
    cancelFile: (fileIndex: number) => void;
  } {
    const uploadWithCancelResults = files.map((file, index) =>
      this.uploadFileWithCancel(
        file,
        onProgress ? progress => onProgress(index, progress) : undefined
      )
    );

    const uploadPromises = uploadWithCancelResults.map(
      result => result.uploadPromise
    );
    const cancelFunctions = uploadWithCancelResults.map(
      result => result.cancel
    );

    return {
      uploadPromises,
      cancelAll: () => cancelFunctions.forEach(cancel => cancel()),
      cancelFile: (fileIndex: number) => {
        if (fileIndex >= 0 && fileIndex < cancelFunctions.length) {
          cancelFunctions[fileIndex]();
        }
      },
    };
  }

  /**
   * Get file metadata
   */
  async getFileMetadata(fileUrl: string): Promise<{
    fileName: string;
    fileSize: number;
    fileType: string;
    uploadedAt: string;
  }> {
    return httpClient.get<{
      fileName: string;
      fileSize: number;
      fileType: string;
      uploadedAt: string;
    }>(`/files/metadata?fileUrl=${encodeURIComponent(fileUrl)}`);
  }

  /**
   * Complete a file upload
   */
  async completeFileUpload(
    request: FileCompleteRequest
  ): Promise<FileCompleteResponse> {
    return httpClient.post<FileCompleteResponse>(this.completePath, request);
  }
}

// Export singleton instance
export const fileService = new FileService();
