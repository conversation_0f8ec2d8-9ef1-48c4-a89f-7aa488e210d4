/**
 * Query Key Factories
 * Centralized query keys for React Query to ensure consistency and type safety
 */

export const queryKeys = {
  // Tags
  tags: {
    all: ['tags'] as const,
    lists: () => [...queryKeys.tags.all, 'list'] as const,
    list: (filters?: {
      page?: number;
      search?: string;
      type?: string;
      limit?: number;
    }) => [...queryKeys.tags.lists(), filters] as const,
    details: () => [...queryKeys.tags.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.tags.details(), id] as const,
  },

  // Locations
  locations: {
    all: ['locations'] as const,
    lists: () => [...queryKeys.locations.all, 'list'] as const,
    list: (filters?: {
      page?: number;
      search?: string;
      status?: string;
      tagIds?: string;
      place?: string;
      limit?: number;
      size?: string;
    }) => [...queryKeys.locations.lists(), filters] as const,
    requests: (filters?: {
      page?: number;
      search?: string;
      tagIds?: string;
      place?: string;
      limit?: number;
    }) => [...queryKeys.locations.all, 'requests', filters] as const,
    cities: () => [...queryKeys.locations.all, 'cities'] as const,
    details: () => [...queryKeys.locations.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.locations.details(), id] as const,
  },

  // Users
  users: {
    all: ['users'] as const,
    lists: () => [...queryKeys.users.all, 'list'] as const,
    list: (filters?: {
      page?: number;
      search?: string;
      role?: string;
      status?: string;
      limit?: number;
    }) => [...queryKeys.users.lists(), filters] as const,
    requests: (filters?: {
      page?: number;
      search?: string;
      role?: string;
      limit?: number;
    }) => [...queryKeys.users.all, 'requests', filters] as const,
    details: () => [...queryKeys.users.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.users.details(), id] as const,
  },

  // Comments
  comments: {
    all: ['comments'] as const,
    lists: () => [...queryKeys.comments.all, 'list'] as const,
    list: (referenceListId?: string, referenceItemId?: string) =>
      [
        ...queryKeys.comments.lists(),
        referenceListId,
        referenceItemId,
      ] as const,
  },
} as const;
