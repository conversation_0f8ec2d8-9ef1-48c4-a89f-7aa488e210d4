import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { DateRangePicker } from '@/components/DateRangePicker';
import { DateRange } from 'react-day-picker';
import { Plus } from 'lucide-react';
import { SearchIcon } from '@/lib/icons';
import { STATUS_OPTIONS } from '@/lib/constants';

interface DashboardFiltersProps {
  search: string;
  onSearchChange: (value: string) => void;
  status: string;
  onStatusChange: (value: string) => void;
  dateRange: DateRange | undefined;
  onDateRangeChange: (dateRange: DateRange | undefined) => void;
  onCreateProject: () => void;
}

export function DashboardFilters({
  search,
  onSearchChange,
  status,
  onStatusChange,
  dateRange,
  onDateRangeChange,
  onCreateProject,
}: DashboardFiltersProps) {
  return (
    <div className="flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between">
      <div className="flex flex-col gap-4 lg:flex-row lg:items-center lg:flex-1">
        <div className="relative flex-1 max-w-[359px]">
          <SearchIcon className="absolute left-3 top-1/2 -translate-y-1/2 text-[#A3A3A3] h-4 w-4" />
          <Input
            placeholder="Search projects..."
            value={search ?? ''}
            onChange={e => onSearchChange(e.target.value)}
            className="pl-9 h-[42px] border-border placeholder:text-input-placeholder"
            debounceMs={300}
          />
        </div>
        <div className="flex gap-3 lg:flex-row lg:items-center">
          <DateRangePicker
            value={dateRange}
            onDateRangeChange={onDateRangeChange}
            className="text-black rounded-lg border-border"
          />

          <Select value={status} onValueChange={onStatusChange}>
            <SelectTrigger
              aria-label="Status"
              className="rounded-lg border-border"
            >
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Status</SelectItem>
              {STATUS_OPTIONS.map((option, index) => (
                <SelectItem key={index} value={option.value}>
                  {option.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>
      <Button
        onClick={onCreateProject}
        variant="default"
        className="bg-[#5b0677] hover:bg-[#6B1F85] text-white h-[41px] w-full lg:w-[178px] px-4 py-2"
      >
        <Plus className="h-3 w-3 mr-1" />
        Create Project
      </Button>
    </div>
  );
}
