'use client';

import Image from 'next/image';
import { Location } from '@/types';
import { Plus } from 'lucide-react';
import { Heading } from '@/components/ui/typography';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { use, useCallback, useEffect, useState } from 'react';
import LocationOffers from '@/components/features/locations/LocationOffers';
import {
  LightingIcon,
  NaturalLightIcon,
  ParkingIcon,
  RooftopAccessIcon,
  ShieldIcon,
  SnowflakeIcon,
  WheelchairIcon,
  WifiIcon,
} from '@/lib/icons';
import Loading from './loading';
import { useRouter } from 'next/navigation';
import { toastError } from '@/lib/toast';
import { useLocationService } from '@/hooks/use-services';

const mockLocationOffers = [
  { icon: ParkingIcon, label: 'Free parking on premises' },
  { icon: NaturalLightIcon, label: 'Abundant natural light' },
  { icon: RooftopAccessIcon, label: 'Rooftop access' },
  { icon: WheelchairIcon, label: 'Wheelchair accessible' },
  { icon: WifiIcon, label: 'High-speed internet' },
  { icon: LightingIcon, label: 'Professional lighting equipment' },
  { icon: SnowflakeIcon, label: 'Climate controlled' },
  { icon: ShieldIcon, label: '24/7 security' },
];

export default function ViewLocationDetail({
  params,
}: {
  params: Promise<{ id: string; locationId: string }>;
}) {
  const router = useRouter();
  const locationService = useLocationService();
  const { id, locationId } = use(params);
  const [location, setLocation] = useState<Location>();
  const [loading, setLoading] = useState<boolean>(true);

  const fetchLocation = useCallback(async () => {
    try {
      setLoading(true);
      const response = await locationService.getLocationById(locationId);
      setLocation(response);
    } catch {
      toastError('Failed to get location');
    } finally {
      setLoading(false);
    }
  }, [locationId, locationService]);

  useEffect(() => {
    if (!locationId) return;
    fetchLocation();
  }, [fetchLocation, locationId]);
  const onSeeAll = () => {
    router.push(`/scout/reference/${id}/view-details/${locationId}/all-photos`);
  };

  return (
    <>
      {loading && <Loading />}
      {!loading && (
        <div className="w-full flex justify-center">
          <div className="space-y-6 xl:w-[70.5rem] p-6 xl:px-0">
            <div className="grid gap-2 sm:gap-6 grid-cols-2 sm:grid-cols-4 grid-rows-2">
              {location?.images[0] && (
                <div className="relative row-span-2 col-span-2 rounded-xl overflow-hidden aspect-[552/337]">
                  <Image
                    src={location?.images[0].url}
                    alt="Most relevant photo"
                    fill
                    className="object-cover hover:scale-105 transition-transform duration-300"
                  />
                </div>
              )}

              {location?.images.slice(1, 5).map((photo, i) => (
                <div
                  key={i}
                  className="relative rounded-xl overflow-hidden sm:h-auto aspect-[263/157]"
                >
                  {i === 3 && location?.images.length > 3 ? (
                    <div
                      onClick={onSeeAll}
                      className="absolute inset-0 flex flex-col items-center justify-center bg-black/40 text-white text-sm sm:text-base font-medium hover:bg-black/50 transition cursor-pointer"
                    >
                      <span className="font-semibold flex items-center text-sm lg:gap-2">
                        <Plus /> See all photos
                      </span>
                    </div>
                  ) : (
                    <Image
                      src={photo.url}
                      alt={`Photo ${i + 2}`}
                      fill
                      className="object-cover hover:scale-105 transition-transform duration-300"
                    />
                  )}
                </div>
              ))}
            </div>
            <div className="flex justify-between items-start">
              <div className="pt-4 pb-3 flex-1 flex flex-col gap-1">
                <div className="flex justify-between items-center">
                  <Heading level={2} className="mb-1">
                    {location?.title}
                  </Heading>
                </div>
                <p className="text-sm text-gray-600">{location?.address}</p>

                <div className="mt-auto flex flex-wrap justify-start gap-1">
                  {location?.tags?.map((tag, ind) => (
                    <Badge
                      key={`tag-${ind}`}
                      variant="secondary"
                      className={`${tag.color ? `bg-${tag.color} text-white` : ''}`}
                    >
                      {tag.name}
                    </Badge>
                  ))}
                </div>
              </div>
              <Button
                onClick={() => {
                  router.push(
                    `/scout/reference/${id}/select-images/${locationId}`
                  );
                }}
                className="w-[9.6875rem] h-12"
              >
                <Plus />
                Add to List
              </Button>
            </div>
            <p>{}</p>
            <div className="mt-8">
              <LocationOffers data={mockLocationOffers} />
            </div>
          </div>
        </div>
      )}
    </>
  );
}
