'use client';

import * as React from 'react';
import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { User } from '@/types/user';
import ConfirmDialog from '@/components/shared/ConfirmDialog';
import { Loader2 } from 'lucide-react';
import { createUserSchema, CreateUserFormData } from '@/lib/validations';
import { Role } from '@/types/enum';
import { ROLE_DISPLAY_NAMES } from '@/lib/utils';
import { useProductionHouseService } from '@/hooks/use-services';
import { AutocompleteInput } from '@/components/ui/autocomplete';
import type { ProductionHouse } from '@/lib/services/production-service';
import { useCreateUser } from '@/hooks/api/useUsers';
interface AddUserModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export default function AddUserModal({
  open,
  onOpenChange,
}: AddUserModalProps) {
  const createUserMutation = useCreateUser();
  const isCreating = createUserMutation.isPending;
  const [isDiscardUserDialogOpen, setIsDiscardUserDialogOpen] = useState(false);
  const productionHouseService = useProductionHouseService();
  const [productionHouses, setProductionHouses] = useState<ProductionHouse[]>(
    []
  );
  const [isLoadingProductionHouses, setIsLoadingProductionHouses] =
    useState(false);

  const form = useForm<CreateUserFormData>({
    resolver: zodResolver(createUserSchema),
    defaultValues: {
      name: '',
      email: '',
      role: undefined,
      productionHouseId: '',
    },
  });

  const [currentRole, setCurrentRole] = useState<Role | undefined>(undefined);
  const watchedProductionHouseId = form.watch('productionHouseId');

  useEffect(() => {
    if (currentRole !== Role.ProductionAdmin) return;

    const fetchProductionHouses = async () => {
      try {
        setIsLoadingProductionHouses(true);
        const response = await productionHouseService.getProductionHouses();
        setProductionHouses(response);
      } catch (error) {
        console.error('Error fetching production houses', error);
      } finally {
        setIsLoadingProductionHouses(false);
      }
    };

    fetchProductionHouses();
  }, [currentRole, productionHouseService]);

  // Reset form when modal opens/closes
  useEffect(() => {
    if (!open) {
      form.reset();
    }
  }, [open, form]);

  const hasUnsavedChanges = form.formState.isDirty;

  const handleSubmit = async (data: CreateUserFormData) => {
    const user: Partial<User> = {
      name: data.name,
      email: data.email,
      role: data.role,
      productionHouse:
        data.role === Role.ProductionAdmin
          ? { id: data.productionHouseId ?? '' }
          : undefined,
    };
    try {
      await createUserMutation.mutateAsync(user);
      onOpenChange(false);
    } catch {
      // Error handling is done in the mutation
    }
  };

  const handleDiscardUser = () => {
    form.reset();
    setIsDiscardUserDialogOpen(false);
    onOpenChange(false);
  };

  const handleCancel = () => {
    if (hasUnsavedChanges) {
      setIsDiscardUserDialogOpen(true);
    } else {
      onOpenChange(false);
    }
  };

  return (
    <>
      <Dialog open={open} onOpenChange={handleCancel}>
        <DialogContent className="sm:max-w-[20.5625rem]">
          <DialogHeader>
            <DialogTitle>New user</DialogTitle>
            <DialogDescription>
              Fill in the details to invite a new user to Scoutr.
            </DialogDescription>
          </DialogHeader>
          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(handleSubmit)}
              className="space-y-4"
            >
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>User name</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter the user's name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input
                        type="email"
                        placeholder="Enter the user's email"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="role"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Role</FormLabel>
                    <Select
                      onValueChange={value => {
                        field.onChange(value);
                        setCurrentRole(value as Role);
                      }}
                      value={field.value}
                    >
                      <FormControl>
                        <SelectTrigger className="w-full">
                          <SelectValue placeholder="Select the user's role" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {Object.values(Role)
                          .filter(role => role !== Role.SuperAdmin)
                          .map(role => (
                            <SelectItem key={role} value={role}>
                              {ROLE_DISPLAY_NAMES[role]}
                            </SelectItem>
                          ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {currentRole === Role.ProductionAdmin && (
                <FormField
                  control={form.control}
                  name="productionHouseId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Production house</FormLabel>
                      <FormControl>
                        <AutocompleteInput
                          allowCreateOption
                          onCreateOption={async label => {
                            const newHouse =
                              await productionHouseService.createProductionHouse(
                                { name: label }
                              );
                            setProductionHouses([
                              newHouse,
                              ...productionHouses,
                            ]);
                            return {
                              value: newHouse.id,
                              label: newHouse.name,
                            };
                          }}
                          options={productionHouses.map(house => ({
                            value: house.id,
                            label: house.name,
                          }))}
                          selectedValue={field.value || null}
                          onSelectedValueChange={value => {
                            field.onChange(value ?? '');
                          }}
                          placeholder="Enter the production house name"
                          isLoading={isLoadingProductionHouses}
                          emptyState={
                            isLoadingProductionHouses
                              ? 'Loading production houses...'
                              : 'No production houses found'
                          }
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}

              <DialogFooter className="mt-6">
                <Button
                  onClick={handleCancel}
                  type="button"
                  variant="link"
                  className="hover:no-underline"
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={
                    isCreating ||
                    !form.formState.isValid ||
                    (currentRole === Role.ProductionAdmin &&
                      !watchedProductionHouseId?.trim())
                  }
                >
                  Send User Invite
                  {isCreating && (
                    <Loader2 className="ml-2 size-4 animate-spin" />
                  )}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
      <ConfirmDialog
        open={isDiscardUserDialogOpen}
        onOpenChange={setIsDiscardUserDialogOpen}
        title="Discard user?"
        description="You've started filling in this user's details. If you cancel now, the information will be lost."
        confirmText="Reject invite"
        cancelText="Cancel"
        onConfirm={handleDiscardUser}
        onCancel={() => setIsDiscardUserDialogOpen(false)}
      />
    </>
  );
}
