import { useState, useRef, useCallback, useEffect } from 'react';
import { FileService } from '@/lib/services';
import { FileError, UploadProgress } from '@/types/file';
import { FileErrorCode, FileStatusCode } from '@/types';
import { FILE_VALIDATION } from '@/types/constant';
import { ApiError } from '@/lib/api-client';

export interface FileValidation {
  maxSize?: number; // in bytes
  allowedTypes?: string[]; // MIME types
  maxFiles?: number; // for multiple files
}

export interface UseFileUploadOptions {
  validation?: FileValidation;
  onUploading?: (isUploading: boolean) => void;
  onUploadComplete?: (data: unknown) => void;
  onUploadError?: (error: FileError) => void;
  onValidationError?: (error: string) => void;
  autoUpload?: boolean; // Automatically upload after validation
  presignedPath?: string;
  completePath?: string;
}

export interface UseFileUploadReturn {
  // File selection
  selectedFiles: File[];
  fileInputRef: React.RefObject<HTMLInputElement | null>;
  openFileDialog: () => void;
  handleFileChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  clearFiles: () => void;

  // Validation
  validationError: string;
  isValid: boolean;

  // Upload state
  isUploading: boolean;
  uploadProgress: number;
  uploadStatus: string;
  uploadResult: {
    success: boolean;
    data?: unknown;
    error?: string;
  } | null;

  // Upload functions
  uploadFiles: () => Promise<void>;
  uploadSingleFile: (
    file: File
  ) => Promise<{ success: boolean; data?: unknown; error?: string }>;

  // Cancel functionality
  cancelUpload: () => void;

  // Utility functions
  validateFile: (file: File) => FileError | null;
  formatFileSize: (bytes: number) => string;
}

export function useFileUpload({
  validation = {},
  onUploading,
  onUploadComplete,
  onUploadError,
  onValidationError,
  autoUpload = false,
  presignedPath,
  completePath,
}: UseFileUploadOptions = {}): UseFileUploadReturn {
  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const fileServiceRef = useRef(new FileService(presignedPath, completePath));

  // Use refs to stabilize callbacks and prevent uploadSingleFile from changing
  const onUploadCompleteRef = useRef(onUploadComplete);
  const onUploadErrorRef = useRef(onUploadError);
  const onValidationErrorRef = useRef(onValidationError);
  const onUploadingRef = useRef(onUploading);

  useEffect(() => {
    onUploadCompleteRef.current = onUploadComplete;
    onUploadErrorRef.current = onUploadError;
    onValidationErrorRef.current = onValidationError;
    onUploadingRef.current = onUploading;
  }, [onUploadComplete, onUploadError, onValidationError, onUploading]);

  // File state
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [validationError, setValidationError] = useState<string>('');

  // Upload state
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadStatus, setUploadStatus] = useState<string>('');
  const [uploadResult, setUploadResult] = useState<{
    success: boolean;
    data?: unknown;
    error?: string;
  } | null>(null);
  const [cancelUploadFn, setCancelUploadFn] = useState<(() => void) | null>(
    null
  );

  const {
    maxSize = FILE_VALIDATION.MAX_SIZE,
    allowedTypes = FILE_VALIDATION.ALLOWED_TYPES,
    maxFiles = 1,
  } = validation;

  const validateFile = useCallback(
    (file: File): FileError | null => {
      // Check file extension
      const fileExtension = file.name.split('.').pop()?.toLowerCase();
      const allowedExtensions = allowedTypes.map(type =>
        type.split('/')[1].toLowerCase().replace('jpeg', 'jpg')
      );

      if (
        allowedTypes.length > 0 &&
        fileExtension &&
        !allowedExtensions.includes(fileExtension)
      ) {
        return {
          code: FileErrorCode.FILE_TYPE_ERROR,
          status: FileStatusCode.ERROR,
          message: `File "${file.name}" has an unsupported extension.`,
        };
      }

      // Check MIME type
      if (allowedTypes.length > 0 && !allowedTypes.includes(file.type)) {
        return {
          code: FileErrorCode.FILE_TYPE_ERROR,
          status: FileStatusCode.ERROR,
          message: `File "${file.name}" is not a supported format.`,
        };
      }

      // Check file size
      if (file.size > maxSize) {
        const maxSizeMB = Math.round(maxSize / (1024 * 1024));
        return {
          code: FileErrorCode.FILE_SIZE_ERROR,
          status: FileStatusCode.ERROR,
          message: `File "${file.name}" is too large. Maximum size: ${maxSizeMB}MB`,
        };
      }

      // Check if file is empty
      if (file.size === 0) {
        return {
          code: FileErrorCode.FILE_SIZE_ERROR,
          status: FileStatusCode.ERROR,
          message: `File "${file.name}" is empty`,
        };
      }

      return null;
    },
    [allowedTypes, maxSize]
  );

  const validateFiles = useCallback(
    (files: FileList | null): { valid: File[]; errors: FileError[] } => {
      if (!files || files.length === 0) {
        return { valid: [], errors: [] };
      }

      const fileArray = Array.from(files);
      const errors: FileError[] = [];
      const validFiles: File[] = [];

      // Check max files limit
      if (fileArray.length > maxFiles) {
        errors.push({
          code: FileErrorCode.FILE_MAX_FILES_ERROR,
          status: FileStatusCode.ERROR,
          message: `Too many files selected. Maximum allowed: ${maxFiles}.`,
        });
        return { valid: [], errors };
      }

      // Validate each file
      fileArray.forEach(file => {
        const error = validateFile(file);
        if (error) {
          errors.push(error);
        } else {
          validFiles.push(file);
        }
      });

      return { valid: validFiles, errors };
    },
    [maxFiles, validateFile]
  );

  const formatFileSize = useCallback((bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }, []);

  const openFileDialog = useCallback(() => {
    fileInputRef.current?.click();
  }, []);

  const clearFiles = useCallback(() => {
    setSelectedFiles([]);
    setValidationError('');
    setUploadResult(null);
    setUploadStatus('');
    setUploadProgress(0);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, []);

  const cancelUpload = useCallback(() => {
    if (cancelUploadFn) {
      cancelUploadFn();
      setCancelUploadFn(null);
      setIsUploading(false);
      setUploadStatus('Upload cancelled');
    }
  }, [cancelUploadFn]);

  const uploadSingleFile = useCallback(
    async (
      file: File
    ): Promise<{ success: boolean; data?: unknown; error?: string }> => {
      // Validate file before starting upload
      const validationError = validateFile(file);
      if (validationError) {
        const result = {
          success: false,
          error: validationError.message,
        };
        setUploadResult(result);
        setUploadStatus(`Validation failed: ${validationError.code}`);
        // Don't call onUploadError for validation errors - these should be handled by onValidationError
        onValidationErrorRef.current?.(validationError.message);
        return result;
      }

      setIsUploading(true);
      onUploadingRef.current?.(true);
      setUploadProgress(0);
      setUploadStatus('Starting upload...');
      setUploadResult(null);

      try {
        const { uploadPromise, cancel } =
          fileServiceRef.current.uploadFileWithCancel(
            file,
            (progressData: UploadProgress) => {
              setUploadProgress(progressData.percentage);
              setUploadStatus(`Uploading... ${progressData.percentage}%`);
            }
          );

        // Store the cancel function
        setCancelUploadFn(() => cancel);

        const result = await uploadPromise;

        setUploadResult(result);
        // console.log('uploadResult', result);

        if (result.success && result.data) {
          setUploadStatus('Upload completed successfully!');
          onUploadCompleteRef.current?.(result.data);
        } else {
          if (result.error !== FileErrorCode.FILE_CANCELLED) {
            setUploadStatus(`Upload failed: ${result.error}`);
            onUploadErrorRef.current?.({
              code: FileErrorCode.UPLOAD_ERROR,
              status: FileStatusCode.ERROR,
              message: result.error || 'Upload failed',
            });
          }
        }

        return result;
      } catch (error) {
        const errorMessage =
          (error as ApiError).response?.data?.message ?? 'Upload failed';
        const result = { success: false, error: errorMessage };

        setUploadResult(result);
        setUploadStatus(`Upload failed: ${errorMessage}`);
        onUploadErrorRef.current?.({
          code: FileErrorCode.UPLOAD_ERROR,
          status: FileStatusCode.ERROR,
          message: errorMessage,
        });

        return result;
      } finally {
        setIsUploading(false);
        setCancelUploadFn(null);
      }
    },
    [validateFile]
  );

  const handleFileChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const files = event.target.files;
      const { valid, errors } = validateFiles(files);

      // Clear previous state
      setValidationError('');
      setSelectedFiles([]);
      setUploadResult(null);
      setUploadStatus('');
      setUploadProgress(0);

      if (errors.length > 0) {
        const errorMessage = errors.map(error => error.message).join('; ');
        setValidationError(errorMessage);
        // Only call onValidationError for validation issues, not onUploadError
        onValidationError?.(errorMessage);
        return;
      }

      if (valid.length > 0) {
        setSelectedFiles(valid);

        // Auto-upload if enabled
        if (autoUpload && valid.length === 1) {
          uploadSingleFile(valid[0]);
        }
      }
    },
    [validateFiles, onValidationError, autoUpload, uploadSingleFile]
  );

  const uploadFiles = useCallback(async (): Promise<void> => {
    if (selectedFiles.length === 0) return;

    // For now, handle single file upload
    if (selectedFiles.length === 1) {
      await uploadSingleFile(selectedFiles[0]);
    } else {
      // Handle multiple files (could be extended)
      console.warn('Multiple file upload not implemented yet');
      // For multiple files, we should implement proper batch upload
      // to avoid calling onUploadError multiple times
    }
  }, [selectedFiles, uploadSingleFile]);

  const isValid = selectedFiles.length > 0 && validationError === '';

  return {
    // File selection
    selectedFiles,
    fileInputRef,
    openFileDialog,
    handleFileChange,
    clearFiles,

    // Validation
    validationError,
    isValid,

    // Upload state
    isUploading,
    uploadProgress,
    uploadStatus,
    uploadResult,

    // Upload functions
    uploadFiles,
    uploadSingleFile,

    // Cancel functionality
    cancelUpload,

    // Utility functions
    validateFile,
    formatFileSize,
  };
}

// Convenience hooks for common use cases
export function useImageUpload(
  options?: Omit<UseFileUploadOptions, 'validation'>
) {
  return useFileUpload({
    ...options,
    validation: {
      maxSize: 10 * 1024 * 1024, // 10MB
      allowedTypes: ['image/jpeg', 'image/jpg', 'image/png'],
      maxFiles: 1,
    },
  });
}

export function useDocumentUpload(
  options?: Omit<UseFileUploadOptions, 'validation'>
) {
  return useFileUpload({
    ...options,
    validation: {
      maxSize: 50 * 1024 * 1024, // 50MB
      allowedTypes: [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'text/plain',
      ],
      maxFiles: 1,
    },
  });
}
