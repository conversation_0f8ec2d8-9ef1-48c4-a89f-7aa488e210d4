'use client';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { SearchIcon } from '@/lib/icons';
import { Plus } from 'lucide-react';

interface PropertyOwnerDashboardFiltersProps {
  search: string;
  onSearchChange: (value: string) => void;
  locationType: string;
  onLocationTypeChange: (value: string) => void;
  status: string;
  onStatusChange: (value: string) => void;
  onSubmitLocation: () => void;
}

const LOCATION_TYPE_OPTIONS = [
  { value: 'all', label: 'Location type' },
  { value: 'residential', label: 'Residential' },
  { value: 'commercial', label: 'Commercial' },
  { value: 'public', label: 'Public' },
];

const STATUS_OPTIONS = [
  { value: 'all', label: 'Status' },
  { value: 'posted', label: 'Posted' },
  { value: 'pending', label: 'Pending Approval' },
  { value: 'feedback', label: 'Feedback Received' },
];

export function PropertyOwnerDashboardFilters({
  search,
  onSearchChange,
  locationType,
  onLocationTypeChange,
  status,
  onStatusChange,
  onSubmitLocation,
}: PropertyOwnerDashboardFiltersProps) {
  return (
    <div className="flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between">
      <div className="flex flex-col gap-3.5 lg:flex-row lg:items-center lg:flex-1">
        {/* Search Input */}
        <div className="relative flex-1 max-w-[359px]">
          <SearchIcon className="absolute left-3 top-1/2 -translate-y-1/2 text-[#A3A3A3] h-4 w-4" />
          <Input
            placeholder="Search locations..."
            value={search ?? ''}
            onChange={e => onSearchChange(e.target.value)}
            className="pl-9 h-[42px] border-neutral-300 placeholder:text-[#adaebc] rounded-lg"
            debounceMs={300}
          />
        </div>

        {/* Location Type Filter */}
        <Select value={locationType} onValueChange={onLocationTypeChange}>
          <SelectTrigger
            aria-label="Location type"
            className="h-[42px] w-[171px] rounded-lg border-neutral-300"
          >
            <SelectValue placeholder="Location type" />
          </SelectTrigger>
          <SelectContent>
            {LOCATION_TYPE_OPTIONS.map(option => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {/* Status Filter */}
        <Select value={status} onValueChange={onStatusChange}>
          <SelectTrigger
            aria-label="Status"
            className="h-[41px] w-[137px] rounded-lg border-neutral-300"
          >
            <SelectValue placeholder="Status" />
          </SelectTrigger>
          <SelectContent>
            {STATUS_OPTIONS.map(option => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Submit Location Button */}
      <Button onClick={onSubmitLocation}>
        <Plus className="h-6 w-6" />
        Submit Location
      </Button>
    </div>
  );
}
