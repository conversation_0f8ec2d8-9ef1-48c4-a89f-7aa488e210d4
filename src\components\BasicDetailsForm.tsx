'use client';

import { useMemo, useState } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import LocationPicker from '@/components/features/locations/LocationPicker';
import { ContactPhoneInput } from '@/components/shared/ContactPhoneInput';
import { Button } from '@/components/ui/button';
import { MoreContactIcon } from '@/lib/icons';
import { locationBasicDetailsSchema } from '@/lib/validations';
import { LocationContact } from '@/types';

interface FormData {
  address: string;
  placeId?: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
  latitude?: number;
  longitude?: number;
  description: string;
  contacts: LocationContact[];
}

interface BasicDetailsProps {
  data: FormData;
  onChange: (data: Partial<FormData>) => void;
}

export default function BasicDetailsForm({
  data,
  onChange,
}: BasicDetailsProps) {
  // Track which fields have been touched/interacted with
  const [touched, setTouched] = useState<Record<string, boolean>>({});

  // Validate form data and get errors
  const validation = useMemo(() => {
    console.log('data', data);
    return locationBasicDetailsSchema.safeParse(data);
  }, [data]);

  const errors = useMemo(() => {
    if (!validation.success) {
      const fieldErrors: Record<string, string> = {};
      validation.error.issues.forEach(error => {
        if (error.path.length > 0) {
          const fieldName = error.path[0];
          if (typeof fieldName === 'string') {
            // Only include error if field has been touched
            if (touched[fieldName]) {
              fieldErrors[fieldName] = error.message;
            }
          }
        }
      });
      return fieldErrors;
    }
    return {};
  }, [validation, touched]);

  const handleInputChange = (field: keyof FormData, value: string) => {
    onChange({ [field]: value });
  };

  const handleInputBlur = (field: keyof FormData) => {
    setTouched(prev => ({ ...prev, [field]: true }));
  };

  const handleLocationChange = (
    address?: string,
    latitude?: number,
    longitude?: number,
    placeId?: string,
    city?: string,
    state?: string,
    country?: string,
    postalCode?: string
  ) => {
    onChange({
      address,
      latitude,
      longitude,
      placeId,
      city,
      state,
      country,
      postalCode,
    });
    // Mark address as touched when location changes
    setTouched(prev => ({ ...prev, address: true }));
  };

  const handlePhoneChange = (index: number, value: string) => {
    const updatedPhones = [...(data.contactPhones || [])];
    updatedPhones[index] = value;
    onChange({ contactPhones: updatedPhones });
  };

  const handleAddPhone = () => {
    const currentPhones =
      data.contactPhones?.length > 0 ? data.contactPhones : [''];
    onChange({ contactPhones: [...currentPhones, ''] });
  };

  const phones = data.contactPhones?.length > 0 ? data.contactPhones : [''];

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <Label htmlFor="address" className="text-base font-medium text-header">
          Address
        </Label>
        <div className="relative">
          <LocationPicker
            defaultAddress={{
              address: data.address,
              lat: data.latitude,
              lng: data.longitude,
            }}
            onChange={handleLocationChange}
          />
        </div>
        {errors.address && (
          <p className="text-sm text-red-500 mt-1">{errors.address}</p>
        )}
      </div>

      <div className="space-y-2">
        <Label
          htmlFor="description"
          className="text-base font-medium text-header"
        >
          Description
        </Label>
        <Textarea
          id="description"
          value={data.description}
          onChange={e => handleInputChange('description', e.target.value)}
          onBlur={() => handleInputBlur('description')}
          placeholder="Describe what makes this location special..."
          className={`w-full min-h-30 max-h-150 ${
            errors.description ? 'border-red-500 focus:border-red-500' : ''
          }`}
        />
        {errors.description && (
          <p className="text-sm text-red-500 mt-1">{errors.description}</p>
        )}
      </div>

      <div className="space-y-2">
        <Label
          htmlFor="contactEmail"
          className="text-base font-medium text-header"
        >
          Contact email
        </Label>
        <Input
          id="contactEmail"
          type="email"
          value={data.contactEmail || ''}
          onChange={e => handleInputChange('contactEmail', e.target.value)}
          onBlur={() => handleInputBlur('contactEmail')}
          placeholder="Enter contact email"
          className={`w-full h-12.5 ${
            errors.contactEmail ? 'border-red-500 focus:border-red-500' : ''
          }`}
        />
        {errors.contactEmail && (
          <p className="text-sm text-red-500 mt-1">{errors.contactEmail}</p>
        )}
      </div>

      <div className="space-y-2">
        <Label
          htmlFor="contactPhone"
          className="text-base font-medium text-header"
        >
          Contact phone
        </Label>
        <div className="space-y-4">
          {phones.map((phone, index) => (
            <ContactPhoneInput
              key={index}
              index={index}
              value={phone}
              onChange={value => handlePhoneChange(index, value)}
            />
          ))}
          <Button
            type="button"
            onClick={handleAddPhone}
            className="flex items-center justify-center gap-2.5 rounded-md bg-[rgba(229,231,235,0.8)] px-4 py-2 text-sm font-medium leading-5 text-gray-900 hover:bg-gray-200 transition-colors"
          >
            <MoreContactIcon className="h-2.5 w-2.5" />
            Add more
          </Button>
        </div>
      </div>
    </div>
  );
}
