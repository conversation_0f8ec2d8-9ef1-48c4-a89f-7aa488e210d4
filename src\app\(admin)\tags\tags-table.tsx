import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { EditIcon, TagIcon, TrashIcon } from '@/lib/icons';
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
} from '@tanstack/react-table';
import { useMemo, useState } from 'react';
import { Checkbox } from '@/components/ui/checkbox';
import ConfirmDialog from '@/components/shared/ConfirmDialog';
import EditTagModal from '@/components/features/tags/EditTagModal';
import { Tag } from '@/types/tag';
import { useDeleteTag } from '@/hooks/api/useTags';
import { toastError } from '@/lib/toast';
import { Loader2Icon } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { IconButton } from '@/components/shared/IconButton';
import TagsTableRowLoading from './tags-table-loading';
import { formatCategoryLabel } from '@/components/features/tags/TagsForm';
import { TagType } from '@/types/enum';

interface TagsTableProps {
  data: Tag[];
  loading: boolean;
  onActionSuccess: (action: 'delete' | 'update', data: Tag) => void;
  checkedList: string[];
  onCheckedListChange: (data: string[]) => void;
  refreshing?: boolean;
}

export default function TagsTable({
  data,
  loading,
  onActionSuccess,
  checkedList,
  onCheckedListChange,
  refreshing = false,
}: TagsTableProps) {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState<boolean>(false);
  const [tagSelected, setTagSelected] = useState<Tag | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState<boolean>(false);

  // Mutations
  const deleteTagMutation = useDeleteTag();
  const updating = deleteTagMutation.isPending;

  const columns: ColumnDef<Tag>[] = useMemo(() => {
    return [
      {
        accessorKey: 'name',
        header: ({ column }) => (
          <Button
            className="p-0"
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            Tag
          </Button>
        ),
        cell: ({ row }) => (
          <div className="flex items-center space-x-4">
            <Checkbox
              id={`checked-tag-${row.original.id}`}
              checked={checkedList.includes(row.original.id)}
              onCheckedChange={checked => {
                const tagId = row.original.id;
                if (checked) {
                  onCheckedListChange([...checkedList, tagId]);
                } else {
                  const result = checkedList.filter(p => p !== tagId);
                  onCheckedListChange(result);
                }
              }}
            />
            <div className="text-sm font-medium break-normal whitespace-normal">
              {row.original.name}
            </div>
          </div>
        ),
      },
      {
        accessorKey: 'type',
        header: ({ column }) => (
          <Button
            className="p-0"
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            Category
          </Button>
        ),
        cell: ({ row }) => (
          <span className="capitalize">
            {formatCategoryLabel(row.original.type as TagType)}
          </span>
        ),
      },
      {
        accessorKey: 'propertyTypes',
        header: ({ column }) => (
          <Button
            className="p-0"
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            Property type
          </Button>
        ),
        cell: ({ row }) => (
          <span className="capitalize">
            {row.original.propertyTypes?.join(', ')}
          </span>
        ),
      },
      {
        id: 'actions',
        header: () => {
          return (
            <div className="flex justify-end items-center">
              {(updating || refreshing) && (
                <Loader2Icon className="h-6 w-6 animate-spin text-primary" />
              )}
            </div>
          );
        },
        cell: ({ row }) => {
          return (
            <div className="flex justify-end gap-1">
              <IconButton
                color="primary"
                size="small"
                onClick={() => {
                  setTagSelected(row.original);
                  setIsEditModalOpen(true);
                }}
              >
                <EditIcon />
              </IconButton>
              <IconButton
                color="primary"
                size="small"
                onClick={() => {
                  setTagSelected(row.original);
                  setIsDeleteDialogOpen(true);
                }}
              >
                <TrashIcon />
              </IconButton>
            </div>
          );
        },
      },
    ];
  }, [checkedList, onCheckedListChange, refreshing, updating]);

  const table = useReactTable({
    data,
    columns,
    state: {
      sorting,
    },
    onSortingChange: setSorting,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
  });

  const handleConfirmDelete = async () => {
    if (tagSelected?.id) {
      try {
        await deleteTagMutation.mutateAsync(tagSelected.id);
        onActionSuccess('delete', tagSelected);
        setIsDeleteDialogOpen(false);
      } catch (error) {
        interface TagsInUseErrorData {
          code: 'TAG_IN_USE';
          usedTags: {
            id: string;
            name: string;
            color?: string;
          }[];
        }
        interface ErrorWithResponse {
          response?: {
            data?: TagsInUseErrorData;
          };
        }
        if (
          typeof error === 'object' &&
          error !== null &&
          (error as ErrorWithResponse).response?.data?.code === 'TAG_IN_USE'
        ) {
          const tagInUseMsg = (
            <div className="flex items-center gap-2 flex-wrap">
              Can&apos;t delete — in use:
              <Badge
                variant="secondary"
                className={`${tagSelected.color ? `bg-${tagSelected.color} text-white` : ''}`}
              >
                <span className="truncate">{tagSelected.name}</span>
              </Badge>
            </div>
          );
          toastError(tagInUseMsg);
        }
        // Other errors are handled in the mutation
      }
    }
  };

  return (
    <>
      <Card className="py-0 rounded-md shadow-sm gap-0">
        <CardContent className="px-0">
          <Table>
            <TableHeader>
              {table.getHeaderGroups().map(headerGroup => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map(header => (
                    <TableHead
                      className="px-6 bg-[#FAFAFA] rounded-md space-x-4 w-[40%]"
                      key={header.id}
                    >
                      <div className="flex items-center space-x-4">
                        {header.id === 'name' && (
                          <Checkbox
                            id="checked-all-tag"
                            checked={
                              checkedList.length > 0 &&
                              data.every(tag => checkedList.includes(tag.id))
                            }
                            onCheckedChange={checked => {
                              const allTagId = data.map(d => d.id);
                              if (checked) {
                                const result = allTagId.filter(
                                  tag => !checkedList.includes(tag)
                                );
                                onCheckedListChange([
                                  ...checkedList,
                                  ...result,
                                ]);
                              } else {
                                onCheckedListChange(
                                  checkedList.filter(
                                    id => !allTagId.includes(id)
                                  )
                                );
                              }
                            }}
                          />
                        )}
                        {header.isPlaceholder
                          ? null
                          : flexRender(
                              header.column.columnDef.header,
                              header.getContext()
                            )}
                      </div>
                    </TableHead>
                  ))}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {loading ? (
                <TagsTableRowLoading />
              ) : (
                <>
                  {table.getRowModel().rows?.length ? (
                    table.getRowModel().rows.map(row => (
                      <TableRow key={row.id} className="h-20">
                        {row.getVisibleCells().map(cell => (
                          <TableCell className="px-6" key={cell.id}>
                            {flexRender(
                              cell.column.columnDef.cell,
                              cell.getContext()
                            )}
                          </TableCell>
                        ))}
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell
                        colSpan={columns.length}
                        className="h-[34.4375rem] text-center"
                      >
                        <div className="h-full flex flex-col items-center justify-center gap-4">
                          <div className="w-29 h-29 bg-[#E5E7EB] flex items-center justify-center rounded-full">
                            <TagIcon color="#D1D5DB" className="w-15 h-13" />
                          </div>
                          <span>No tags yet.</span>
                        </div>
                      </TableCell>
                    </TableRow>
                  )}
                </>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
      <ConfirmDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
        title="Remove tag?"
        description="This tag will be permanently deleted and removed from all locations where it is currently applied."
        confirmText="Remove"
        cancelText="Keep tag"
        onConfirm={handleConfirmDelete}
        loading={updating}
      />
      <EditTagModal
        tag={tagSelected}
        open={isEditModalOpen}
        onOpenChange={setIsEditModalOpen}
      />
    </>
  );
}
