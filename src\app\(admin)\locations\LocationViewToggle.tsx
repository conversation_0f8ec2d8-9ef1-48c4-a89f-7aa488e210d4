'use client';

import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

interface LocationViewToggleProps {
  view: 'locations' | 'requests';
  requestsCount?: number;
  onViewChange: (view: 'locations' | 'requests') => void;
}

export function LocationViewToggle({
  view,
  requestsCount = 0,
  onViewChange,
}: LocationViewToggleProps) {
  const isLocationsView = view === 'locations';

  return (
    <div className="flex items-center gap-2">
      <div className="inline-flex rounded-lg p-1 bg-primary/10 space-x-1">
        <Button
          variant={isLocationsView ? 'secondary' : 'ghost'}
          size="sm"
          onClick={() => onViewChange('locations')}
          className={
            isLocationsView
              ? 'bg-white hover:bg-white hover:text-primary text-primary'
              : 'hover:bg-white text-muted-foreground'
          }
        >
          Locations
        </Button>
        <Button
          variant={isLocationsView ? 'ghost' : 'secondary'}
          size="sm"
          onClick={() => onViewChange('requests')}
          className={
            isLocationsView
              ? 'relative hover:bg-white text-muted-foreground'
              : 'relative bg-white hover:bg-white hover:text-primary text-primary'
          }
        >
          <span className="flex items-center">
            Requests
            {requestsCount > 0 && (
              <Badge
                className={`ml-2 h-5 px-1.5 text-[0.6875rem] text-black bg-primary/10 rounded-full`}
              >
                {requestsCount}
              </Badge>
            )}
          </span>
        </Button>
      </div>
    </div>
  );
}
