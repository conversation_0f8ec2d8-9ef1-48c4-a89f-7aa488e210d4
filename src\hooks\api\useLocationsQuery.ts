'use client';

import { useCallback, useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Location } from '@/types';
import { TagOption } from '@/types/tag';
import {
  useLocations as useLocationsQuery,
  useTagOptions,
} from './useLocations';

export interface LocationsFilters {
  search: string;
  tags: string;
  city: string;
  page: number;
}

export interface UseLocationsReturn {
  // Data
  locations: Location[];
  tagOptions: TagOption[];
  totalPages: number;
  loading: boolean;
  refreshingData: boolean;

  // Filters
  filters: LocationsFilters;

  // Actions
  setSearch: (search: string) => void;
  setTags: (tags: string) => void;
  setCity: (city: string) => void;
  setPage: (page: number) => void;
  resetFilters: () => void;
  refreshData: () => void;
}

const LIMIT = 6;
const DEFAULT_FILTERS: LocationsFilters = {
  search: '',
  tags: '',
  city: '',
  page: 1,
};

export function useLocations(): UseLocationsReturn {
  const router = useRouter();
  const searchParams = useSearchParams();

  // Initialize filters from URL params
  const [filters, setFilters] = useState<LocationsFilters>(() => {
    const search = searchParams.get('search') || '';
    const tags = searchParams.get('tags') || '';
    const city = searchParams.get('city') || '';
    const page = parseInt(searchParams.get('page') || '1', 10);

    return {
      search,
      tags,
      city,
      page: isNaN(page) ? 1 : page,
    };
  });

  // Fetch locations using React Query
  const {
    data: locationsResponse,
    isLoading: loading,
    isFetching: refreshingData,
    refetch,
  } = useLocationsQuery(
    {
      page: filters.page,
      search: filters.search || undefined,
      tagIds: filters.tags !== 'all' ? filters.tags : undefined,
      where: filters.city !== 'all' ? { city: filters.city } : undefined,
      limit: LIMIT,
    },
    { enabled: true }
  );

  // Fetch tag options
  const { data: tagOptions = [] } = useTagOptions();

  const locations = locationsResponse?.data ?? [];
  const totalPages = locationsResponse?.meta.totalPages ?? 1;

  // Update URL params when filters change
  const updateUrlParams = useCallback(
    (newFilters: LocationsFilters) => {
      const params = new URLSearchParams();

      if (newFilters.search) params.set('search', newFilters.search);
      if (newFilters.tags && newFilters.tags !== 'all')
        params.set('tags', newFilters.tags);
      if (newFilters.city && newFilters.city !== 'all')
        params.set('city', newFilters.city);
      if (newFilters.page > 1) params.set('page', newFilters.page.toString());

      const queryString = params.toString();
      const newUrl = queryString ? `?${queryString}` : window.location.pathname;

      router.replace(newUrl, { scroll: false });
    },
    [router]
  );

  // Filter setters
  const setSearch = useCallback(
    (search: string) => {
      const newFilters = { ...filters, search, page: 1 };
      setFilters(newFilters);
      updateUrlParams(newFilters);
    },
    [filters, updateUrlParams]
  );

  const setTags = useCallback(
    (tags: string) => {
      const newFilters = { ...filters, tags, page: 1 };
      setFilters(newFilters);
      updateUrlParams(newFilters);
    },
    [filters, updateUrlParams]
  );

  const setCity = useCallback(
    (city: string) => {
      const newFilters = { ...filters, city, page: 1 };
      setFilters(newFilters);
      updateUrlParams(newFilters);
    },
    [filters, updateUrlParams]
  );

  const setPage = useCallback(
    (page: number) => {
      const newFilters = { ...filters, page };
      setFilters(newFilters);
      updateUrlParams(newFilters);
    },
    [filters, updateUrlParams]
  );

  const resetFilters = useCallback(() => {
    setFilters(DEFAULT_FILTERS);
    updateUrlParams(DEFAULT_FILTERS);
  }, [updateUrlParams]);

  const refreshData = useCallback(() => {
    refetch();
  }, [refetch]);

  // Sync URL params only when searchParams change (browser navigation)
  useEffect(() => {
    const search = searchParams.get('search') || '';
    const tags = searchParams.get('tags') || '';
    const city = searchParams.get('city') || '';
    const page = parseInt(searchParams.get('page') || '1', 10);

    const urlFilters = {
      search,
      tags,
      city,
      page: isNaN(page) ? 1 : page,
    };

    // Only update if different from current filters (prevents infinite loops)
    if (
      urlFilters.search !== filters.search ||
      urlFilters.tags !== filters.tags ||
      urlFilters.city !== filters.city ||
      urlFilters.page !== filters.page
    ) {
      setFilters(urlFilters);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchParams]); // Only depend on searchParams, not filters

  return {
    // Data
    locations,
    tagOptions,
    totalPages,
    loading,
    refreshingData,

    // Filters
    filters,

    // Actions
    setSearch,
    setTags,
    setCity,
    setPage,
    resetFilters,
    refreshData,
  };
}
