'use client';

import { useCallback, useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Location } from '@/types';
import { TagOption } from '@/types/tag';
import { useLocationService, useTagService } from './use-services';
import { toastError } from '@/lib/toast';

export interface LocationsFilters {
  search: string;
  tags: string;
  place: string;
  page: number;
}

export interface UseLocationsReturn {
  // Data
  locations: Location[];
  tagOptions: TagOption[];
  totalPages: number;
  loading: boolean;
  refreshingData: boolean;

  // Filters
  filters: LocationsFilters;

  // Actions
  setSearch: (search: string) => void;
  setTags: (tags: string) => void;
  setPlace: (place: string) => void;
  setPage: (page: number) => void;
  resetFilters: () => void;
  refreshData: () => void;
}

const LIMIT = 6;
const DEFAULT_FILTERS: LocationsFilters = {
  search: '',
  tags: '',
  place: '',
  page: 1,
};

export function useLocations(): UseLocationsReturn {
  const router = useRouter();
  const searchParams = useSearchParams();
  const locationService = useLocationService();
  const tagService = useTagService();

  // State
  const [locations, setLocations] = useState<Location[]>([]);
  const [tagOptions, setTagOptions] = useState<TagOption[]>([]);
  const [totalPages, setTotalPages] = useState<number>(1);
  const [loading, setLoading] = useState<boolean>(true);
  const [refreshingData, setRefreshingData] = useState<boolean>(false);

  // Initialize filters from URL params
  const [filters, setFilters] = useState<LocationsFilters>(() => {
    const search = searchParams.get('search') || '';
    const tags = searchParams.get('tags') || '';
    const place = searchParams.get('place') || '';
    const page = parseInt(searchParams.get('page') || '1', 10);

    return {
      search,
      tags,
      place,
      page: isNaN(page) ? 1 : page,
    };
  });

  // Fetch locations data
  const fetchLocations = useCallback(
    async (isRunBackground?: boolean) => {
      try {
        if (!isRunBackground) setLoading(true);
        else setRefreshingData(true);
        const response = await locationService.getLocations(
          filters.page,
          filters.search || undefined,
          undefined,
          filters.tags || undefined,
          undefined,
          LIMIT
        );
        setLocations(response.data);
        setTotalPages(response.meta.totalPages);
      } catch (error) {
        toastError('Failed to get locations');
        console.error('Error fetching locations:', error);
      } finally {
        if (!isRunBackground) setLoading(false);
        else setRefreshingData(false);
      }
    },
    [locationService, filters]
  );

  // Fetch tag options
  const fetchTagOptions = useCallback(async () => {
    try {
      const response = await tagService.getTags({ limit: 100 });
      setTagOptions(
        response.data.map(data => ({
          value: data.id,
          label: data.name,
          color: data.color,
        }))
      );
    } catch (error) {
      toastError('Failed to get tags');
      console.error('Error fetching tags:', error);
    }
  }, [tagService]);

  // Update URL params when filters change
  const updateUrlParams = useCallback(
    (newFilters: LocationsFilters) => {
      const params = new URLSearchParams();

      if (newFilters.search) params.set('search', newFilters.search);
      if (newFilters.tags && newFilters.tags !== 'all')
        params.set('tags', newFilters.tags);
      if (newFilters.place && newFilters.place !== 'all')
        params.set('place', newFilters.place);
      if (newFilters.page > 1) params.set('page', newFilters.page.toString());

      const queryString = params.toString();
      const newUrl = queryString ? `?${queryString}` : window.location.pathname;

      router.replace(newUrl, { scroll: false });
    },
    [router]
  );

  // Filter setters
  const setSearch = useCallback(
    (search: string) => {
      const newFilters = { ...filters, search, page: 1 };
      setFilters(newFilters);
      updateUrlParams(newFilters);
    },
    [filters, updateUrlParams]
  );

  const setTags = useCallback(
    (tags: string) => {
      const newFilters = { ...filters, tags, page: 1 };
      setFilters(newFilters);
      updateUrlParams(newFilters);
    },
    [filters, updateUrlParams]
  );

  const setPlace = useCallback(
    (place: string) => {
      const newFilters = { ...filters, place, page: 1 };
      setFilters(newFilters);
      updateUrlParams(newFilters);
    },
    [filters, updateUrlParams]
  );

  const setPage = useCallback(
    (page: number) => {
      const newFilters = { ...filters, page };
      setFilters(newFilters);
      updateUrlParams(newFilters);
    },
    [filters, updateUrlParams]
  );

  const resetFilters = useCallback(() => {
    setFilters(DEFAULT_FILTERS);
    updateUrlParams(DEFAULT_FILTERS);
  }, [updateUrlParams]);

  const refreshData = useCallback(() => {
    fetchLocations(true);
  }, [fetchLocations]);

  // Effects
  useEffect(() => {
    fetchLocations();
  }, [fetchLocations]);

  useEffect(() => {
    fetchTagOptions();
  }, [fetchTagOptions]);

  // Sync URL params only when searchParams change (browser navigation)
  useEffect(() => {
    const search = searchParams.get('search') || '';
    const tags = searchParams.get('tags') || '';
    const place = searchParams.get('place') || '';
    const page = parseInt(searchParams.get('page') || '1', 10);

    const urlFilters = {
      search,
      tags,
      place,
      page: isNaN(page) ? 1 : page,
    };

    // Only update if different from current filters (prevents infinite loops)
    if (
      urlFilters.search !== filters.search ||
      urlFilters.tags !== filters.tags ||
      urlFilters.place !== filters.place ||
      urlFilters.page !== filters.page
    ) {
      setFilters(urlFilters);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [searchParams]); // Only depend on searchParams, not filters

  return {
    // Data
    locations,
    tagOptions,
    totalPages,
    loading,
    refreshingData,

    // Filters
    filters,

    // Actions
    setSearch,
    setTags,
    setPlace,
    setPage,
    resetFilters,
    refreshData,
  };
}
