import * as Sentry from '@sentry/nextjs';

/**
 * Sentry context value type
 */
type SentryContextValue = string | number | boolean | null | undefined;

/**
 * Sentry context object
 */
type SentryContext = Record<string, SentryContextValue | SentryContextValue[]>;

// Manual error reporting
export const captureError = (error: Error, context?: SentryContext) => {
  Sentry.captureException(error, {
    contexts: {
      custom: context,
    },
  });
};

// Manual message reporting
export const captureMessage = (
  message: string,
  level: Sentry.SeverityLevel = 'info'
) => {
  Sentry.captureMessage(message, level);
};

/**
 * Sentry user context
 */
export interface SentryUser {
  id: string;
  email?: string;
  username?: string;
  [key: string]: SentryContextValue | SentryContextValue[] | undefined;
}

// Set user context
export const setUser = (user: SentryUser | null) => {
  Sentry.setUser(user);
};

// Clear user context
export const clearUser = () => {
  Sentry.setUser(null);
};

// Set tags for filtering
export const setTag = (key: string, value: string) => {
  Sentry.setTag(key, value);
};

// Set extra context
export const setExtra = (key: string, value: SentryContextValue) => {
  Sentry.setExtra(key, value);
};

// Add breadcrumb for debugging
export const addBreadcrumb = (
  message: string,
  category: string = 'default',
  level: Sentry.SeverityLevel = 'info',
  data?: SentryContext
) => {
  Sentry.addBreadcrumb({
    message,
    category,
    level,
    data,
  });
};

// Wrap async functions for error tracking
export const withSentry = <T extends unknown[], R>(
  fn: (...args: T) => Promise<R>,
  name?: string
) => {
  return async (...args: T): Promise<R> => {
    try {
      const result = await fn(...args);
      return result;
    } catch (error) {
      captureError(error as Error, {
        functionName: name,
        args: JSON.stringify(args),
      });
      throw error;
    }
  };
};

// Monitor API calls
export const monitorApiCall = async <T>(
  apiCall: () => Promise<T>,
  endpoint: string,
  method: string = 'GET'
): Promise<T> => {
  try {
    const result = await apiCall();
    return result;
  } catch (error) {
    captureError(error as Error, { endpoint, method });
    throw error;
  }
};
