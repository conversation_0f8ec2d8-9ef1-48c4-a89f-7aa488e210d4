'use client';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { SearchIcon } from '@/lib/icons';
import { Plus } from 'lucide-react';
import { useEffect, useState } from 'react';
import DataTablePagination from '@/components/shared/DataTablePagination';
import TagsTable from './tags-table';
import AddTagModal from '@/components/features/tags/AddTagModal';
import { Tag } from '@/types/tag';
import { ChevronDown } from 'lucide-react';
import ConfirmDialog from '@/components/shared/ConfirmDialog';
import { toastError } from '@/lib/toast';
import { Badge } from '@/components/ui/badge';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { useTags, useBulkDeleteTags } from '@/hooks/api/useTags';
import { useDebounce } from '@/hooks/useDebounce';
import { TAG_TYPE_OPTIONS } from '@/types/constant';

const LIMIT = 6;
export default function TagsContainer() {
  const [search, setSearch] = useState<string>('');
  const [type, setType] = useState<string>('all');
  const [page, setPage] = useState<number>(1);
  const [checkedList, setCheckedList] = useState<string[]>([]);
  const [isAddTagModalOpen, setIsAddTagModalOpen] = useState<boolean>(false);
  const [isBulkDeleteDialogOpen, setIsBulkDeleteDialogOpen] =
    useState<boolean>(false);

  // Debounce search to avoid too many API calls
  const debouncedSearch = useDebounce(search, 300);

  // Reset page to 1 when search or type changes
  useEffect(() => {
    setPage(1);
  }, [debouncedSearch, type]);

  // Fetch tags using React Query
  const {
    data: tagsResponse,
    isLoading: loading,
    isFetching: isRefreshingData,
  } = useTags({
    page,
    search: debouncedSearch || undefined,
    propertyType: type !== 'all' ? type : undefined,
    limit: LIMIT,
  });

  const tagsData = tagsResponse?.data ?? [];
  const totalPage = tagsResponse?.meta.totalPages ?? 1;
  const bulkDeleteMutation = useBulkDeleteTags();

  const handleBulkDelete = async () => {
    try {
      if (!checkedList.length) return;
      await bulkDeleteMutation.mutateAsync(checkedList);
      setCheckedList([]);
      setPage(1);
    } catch (error: unknown) {
      // Handle special error case for tags in use
      interface TagsInUseErrorData {
        code: 'TAGS_IN_USE';
        usedTags: {
          id: string;
          name: string;
          color?: string;
        }[];
      }
      interface ErrorWithResponse {
        response?: {
          data?: TagsInUseErrorData;
        };
      }

      if (
        typeof error === 'object' &&
        error !== null &&
        (error as ErrorWithResponse).response?.data?.code === 'TAGS_IN_USE'
      ) {
        const apiError = (error as ErrorWithResponse).response!.data!;
        const usedTags = apiError.usedTags;
        const tagCount = usedTags.length;
        const tagsInUseMsg = (
          <div className="flex items-center gap-2 flex-wrap">
            Can&apos;t delete — in use:
            {usedTags
              .slice(0, tagCount > 6 ? 5 : tagCount)
              .map((tag, index) => (
                <Badge
                  key={index}
                  variant="secondary"
                  className={`${tag.color ? `bg-${tag.color} text-white` : ''}`}
                >
                  <span className="truncate">{tag.name}</span>
                </Badge>
              ))}
            {tagCount > 6 && (
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="text-right">
                    <Badge variant="secondary">
                      +{tagCount - 5} more{tagCount - 5 > 1 ? 's' : ''}
                    </Badge>
                  </div>
                </TooltipTrigger>
                <TooltipContent
                  className="w-auto flex flex-col gap-2 bg-accent-foreground !z-[9999999999] max-h-80 dark"
                  side="right"
                >
                  <div className="flex flex-col gap-2">
                    {usedTags?.slice(5).map(tag => (
                      <Badge
                        key={tag.id}
                        variant="secondary"
                        className={`${tag.color ? `bg-${tag.color} text-white` : ''}`}
                      >
                        <span className="truncate">{tag.name}</span>
                      </Badge>
                    ))}
                  </div>
                </TooltipContent>
              </Tooltip>
            )}
          </div>
        );
        toastError(tagsInUseMsg);
      }
      // Other errors are handled in the mutation
    }
  };

  const handleActionSuccess = (action: 'delete' | 'update', data: Tag) => {
    if (action === 'delete') {
      setCheckedList(prev => prev.filter(p => p !== data.id));
      setPage(1);
    }
    // Update is handled automatically by React Query cache invalidation
  };

  useEffect(() => {
    setCheckedList([]);
  }, [type, debouncedSearch]);

  return (
    <>
      <h1 className="text-2xl font-bold">Tags</h1>
      <div className="text-base">Add or edit tags.</div>
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:flex-1">
          <div className="relative w-full md:w-[11.25rem] xl:w-[16rem]">
            <SearchIcon className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search by name..."
              value={search ?? ''}
              onChange={e => {
                setSearch(e.target.value);
              }}
              className="pl-9"
              debounceMs={300}
            />
          </div>
          <div className="flex flex-wrap items-center gap-2">
            <Select value={type} onValueChange={v => setType(v)}>
              <SelectTrigger aria-label="Tags">
                <SelectValue placeholder="All Tags" />
              </SelectTrigger>
              <SelectContent>
                {[
                  { value: 'all', label: 'Type' },
                  ...Object.entries(TAG_TYPE_OPTIONS).map(([value, label]) => ({
                    value,
                    label,
                  })),
                ].map(type => (
                  <SelectItem
                    className="capitalize"
                    key={type.value}
                    value={type.value}
                  >
                    {type.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            className="w-[10rem] h-[2.625rem] flex justify-between items-center"
            onClick={() => {
              if (checkedList.length) setIsBulkDeleteDialogOpen(true);
              else toastError('Please select one or more tags.');
            }}
          >
            Bulk Actions <ChevronDown />
          </Button>
          <Button
            variant="default"
            className="w-[12.8125rem] h-[2.625rem]"
            onClick={() => setIsAddTagModalOpen(true)}
          >
            <Plus /> Create New Tag
          </Button>
        </div>
      </div>
      <TagsTable
        data={tagsData}
        loading={loading}
        onActionSuccess={handleActionSuccess}
        checkedList={checkedList}
        onCheckedListChange={setCheckedList}
        refreshing={isRefreshingData}
      />
      <DataTablePagination
        page={page}
        totalPages={totalPage}
        onPageChange={setPage}
      />
      <AddTagModal
        open={isAddTagModalOpen}
        onOpenChange={setIsAddTagModalOpen}
      />
      <ConfirmDialog
        open={isBulkDeleteDialogOpen}
        onOpenChange={setIsBulkDeleteDialogOpen}
        title="Bulk delete tags?"
        description="These tags will be permanently deleted and removed from all locations where they are currently applied."
        confirmText="Remove"
        cancelText="Keep tags"
        onConfirm={handleBulkDelete}
        loading={bulkDeleteMutation.isPending}
      />
    </>
  );
}
