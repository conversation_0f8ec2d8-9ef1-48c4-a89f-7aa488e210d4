'use client';

import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

interface UsersViewToggleProps {
  view: 'active' | 'requests';
  requestsCount: number;
  onViewChange: (view: 'active' | 'requests') => void;
}

export function UsersViewToggle({
  view,
  requestsCount,
  onViewChange,
}: UsersViewToggleProps) {
  const isActiveView = view === 'active';

  return (
    <div className="flex items-center gap-2">
      <div className="inline-flex rounded-lg p-1 bg-primary/10 space-x-1">
        <Button
          variant={isActiveView ? 'secondary' : 'ghost'}
          size="sm"
          onClick={() => onViewChange('active')}
          className={
            isActiveView
              ? 'bg-white hover:bg-white hover:text-primary text-primary'
              : 'hover:bg-white text-muted-foreground'
          }
        >
          Active Users
        </Button>
        <Button
          variant={isActiveView ? 'ghost' : 'secondary'}
          size="sm"
          onClick={() => onViewChange('requests')}
          className={
            isActiveView
              ? 'relative hover:bg-white text-muted-foreground'
              : 'relative bg-white hover:bg-white hover:text-primary text-primary'
          }
        >
          Requests
          {requestsCount > 0 && (
            <Badge
              className={`ml-2 h-5 px-1.5 text-[0.6875rem] text-black bg-primary/10 rounded-full`}
            >
              {requestsCount}
            </Badge>
          )}
        </Button>
      </div>
    </div>
  );
}
