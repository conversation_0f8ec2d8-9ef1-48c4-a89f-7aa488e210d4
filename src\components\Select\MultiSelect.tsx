'use client';

import * as React from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import { ChevronDown } from 'lucide-react';
import { CheckIcon } from '@/lib/icons';

export type MultiSelectOption = {
  value: string;
  label: string;
  color?: string;
  disabled?: boolean;
};

export interface MultiSelectProps {
  options: MultiSelectOption[];
  value: string[];
  onChange: (next: string[]) => void;
  placeholder?: string;
  searchPlaceholder?: string;
  className?: string;
  emptyMessage?: string;
  maxBadgeCount?: number;
}
export default function MultiSelect({
  options,
  value,
  onChange,
  placeholder = 'Select',
  searchPlaceholder = 'Search...',
  className,
  emptyMessage = 'No results found',
  maxBadgeCount = 3,
}: MultiSelectProps) {
  const [open, setOpen] = React.useState(false);

  const selectedSet = React.useMemo(() => new Set(value), [value]);
  const selectedOptions = React.useMemo(
    () => options.filter(o => selectedSet.has(o.value)),
    [options, selectedSet]
  );

  const toggle = React.useCallback(
    (val: string) => {
      if (selectedSet.has(val)) {
        onChange(value.filter(v => v !== val));
      } else {
        onChange([...value, val]);
      }
    },
    [onChange, selectedSet, value]
  );

  const selectedText = React.useMemo(() => {
    const labels = selectedOptions.map(opt => opt.label);
    const visible = labels.slice(0, maxBadgeCount);
    const hiddenCount = labels.length - visible.length;
    if (hiddenCount > 0) {
      return `${visible.join(', ')} +${hiddenCount}`;
    }
    return visible.join(', ');
  }, [selectedOptions, maxBadgeCount]);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          type="button"
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn('w-full justify-between h-10', className)}
        >
          {selectedOptions.length === 0 ? (
            <span className="text-muted-foreground">{placeholder}</span>
          ) : (
            <span className="flex-1 text-left truncate text-sm text-slate-900">
              {selectedText}
            </span>
          )}
          <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-60" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="p-0 w-[20rem]" align="start">
        <Command loop>
          <div className="border-b">
            <CommandInput placeholder={searchPlaceholder} />
          </div>
          <CommandList>
            <CommandEmpty className="py-6 text-center text-sm text-muted-foreground">
              {emptyMessage}
            </CommandEmpty>
            <CommandGroup>
              {options.map(opt => {
                const checked = selectedSet.has(opt.value);
                return (
                  <CommandItem
                    key={opt.value}
                    disabled={opt.disabled}
                    value={`${opt.label} ${opt.value}`}
                    onSelect={() => toggle(opt.value)}
                    className={cn(
                      'gap-2 cursor-pointer hover:bg-transparent data-[selected=true]:bg-primary-50 data-[selected=true]:text-inherit',
                      checked && 'bg-primary-50'
                    )}
                  >
                    <CheckIcon
                      className={`h-4 w-4 ${checked ? 'opacity-100' : 'opacity-0'} shrink-0`}
                    />
                    <span className="truncate">{opt.label}</span>
                  </CommandItem>
                );
              })}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
