'use client';

import { SessionProvider } from 'next-auth/react';
import { AuthProvider } from '@/contexts/auth-context';
import { Toaster } from '@/components/ui/sonner';
import QueryProvider from './QueryProvider';

export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <SessionProvider basePath="/nextapi/auth">
      <AuthProvider>
        <QueryProvider>{children}</QueryProvider>
        <Toaster
          position="top-right"
          offset={{ top: 136, right: '17.5%' }}
          expand={true}
          closeButton={true}
          visibleToasts={5}
        />
      </AuthProvider>
    </SessionProvider>
  );
}
