import { NextAuthOptions } from 'next-auth';
import { JWT } from 'next-auth/jwt';
import CredentialsProvider from 'next-auth/providers/credentials';
import { Role } from '@/types/enum';
import { authService, type AuthenticatedUser } from './services';

const debugLogEnabled = false;

// -----------------------------------------------------------------------------
// 🔒 Global cache to ensure refresh requests don't run concurrently per user
// -----------------------------------------------------------------------------
const globalCache = globalThis as {
  __refreshPromises?: Map<string, Promise<JWT>>;
  __lastRefreshTime?: Map<string, number>;
  __cachedTokens?: Map<string, JWT>;
};
if (!globalCache.__refreshPromises) {
  globalCache.__refreshPromises = new Map<string, Promise<JWT>>();
}
if (!globalCache.__lastRefreshTime) {
  globalCache.__lastRefreshTime = new Map<string, number>();
}
if (!globalCache.__cachedTokens) {
  globalCache.__cachedTokens = new Map<string, JWT>();
}
const refreshPromises: Map<
  string,
  Promise<JWT>
> = globalCache.__refreshPromises;
const lastRefreshTime: Map<string, number> = globalCache.__lastRefreshTime;
const cachedTokens: Map<string, JWT> = globalCache.__cachedTokens;

// -----------------------------------------------------------------------------
// Helper function to refresh token safely (using shared promise cache)
// -----------------------------------------------------------------------------

async function getOrRefreshToken(token: JWT): Promise<JWT> {
  const bufferTime = 5 * 60 * 1000; // 5 minutes before expiry
  if (Date.now() < (token.accessTokenExpires as number) - bufferTime) {
    return token; // token still valid
  }

  const key = token.sub || token.userId || 'anonymous';
  if (!key) throw new Error('Missing user identifier');

  const keyStr = key.toString();
  const now = Date.now();
  const lastRefresh = lastRefreshTime.get(keyStr);

  // If a refresh happened within the last 10 seconds, return the cached refreshed token
  // This prevents rapid successive refresh attempts with the same old token
  if (lastRefresh && now - lastRefresh < 10000) {
    const cached = cachedTokens.get(keyStr);
    if (cached) {
      if (process.env.NODE_ENV === 'development' && debugLogEnabled) {
        console.log(
          '⏭️  Token was just refreshed, returning cached token (last refresh:',
          Math.floor((now - lastRefresh) / 1000),
          'seconds ago)'
        );
      }
      return cached;
    }
  }

  // If there's already a refresh in progress for this user, wait for it
  // Double-check pattern to prevent race conditions
  let existingPromise = refreshPromises.get(keyStr);
  if (existingPromise) {
    if (process.env.NODE_ENV === 'development' && debugLogEnabled) {
      console.log('⏳ Token refresh already in progress, waiting...');
    }
    return existingPromise;
  }

  // Use the cached token's refresh token if available and recent (within 30 seconds)
  // This ensures we always use the latest refresh token available
  // The cached token's refresh token is the most recent one we have
  const cachedToken = cachedTokens.get(keyStr);
  const tokenToUse = cachedToken || token;
  const refreshTokenToUse = tokenToUse.refreshToken as string;

  // Otherwise, start a new refresh
  if (process.env.NODE_ENV === 'development' && debugLogEnabled) {
    console.log('🔄 Starting new token refresh...');
    console.log('OLD ACCESS TOKEN:', tokenToUse.accessToken);
    console.log('OLD REFRESH TOKEN:', refreshTokenToUse);
    console.log(
      'ACCESS TOKEN EXPIRES:',
      new Date(tokenToUse.accessTokenExpires as number).toISOString()
    );
    if (cachedToken) {
      console.log(
        '📦 Using cached token for refresh (most recent refresh token)'
      );
    }
  }

  // Create refresh promise with the most recent refresh token
  const refreshPromise = refreshAccessToken({
    ...tokenToUse,
    refreshToken: refreshTokenToUse,
  });

  // Double-check before setting to prevent race conditions
  // If another request set a promise while we were creating ours, use theirs instead
  // This prevents multiple refresh attempts with the same old refresh token
  existingPromise = refreshPromises.get(keyStr);
  if (existingPromise) {
    if (process.env.NODE_ENV === 'development' && debugLogEnabled) {
      console.log(
        '⏳ Another refresh started concurrently, using existing promise...'
      );
    }
    // Cancel our promise by catching any errors silently (the other promise will handle it)
    refreshPromise.catch(() => {
      // Silently ignore - the other promise will handle the refresh
    });
    return existingPromise;
  }

  // Atomically set the promise
  refreshPromises.set(keyStr, refreshPromise);

  try {
    const refreshedToken = await refreshPromise;

    // Check if refresh failed (error embedded in token)
    if (refreshedToken.error) {
      if (process.env.NODE_ENV === 'development' && debugLogEnabled) {
        console.error(
          '❌ Token refresh failed with error:',
          refreshedToken.error
        );
      }
      // Clear caches on error
      cachedTokens.delete(keyStr);
      lastRefreshTime.delete(keyStr);
      return refreshedToken;
    }

    // Cache the refreshed token and record the time
    cachedTokens.set(keyStr, refreshedToken);
    lastRefreshTime.set(keyStr, Date.now());

    if (process.env.NODE_ENV === 'development' && debugLogEnabled) {
      console.log('NEW ACCESS TOKEN:', refreshedToken.accessToken);
      console.log('NEW REFRESH TOKEN:', refreshedToken.refreshToken);
      console.log('✅ Token refresh completed successfully');
    }
    return refreshedToken;
  } catch (err) {
    console.error('Token refresh failed:', err);
    // Clear caches on error
    cachedTokens.delete(keyStr);
    lastRefreshTime.delete(keyStr);
    throw err;
  } finally {
    // Clear the promise after a short delay
    setTimeout(() => refreshPromises.delete(keyStr), 1000);

    // Clear the cached token and last refresh time after 30 seconds
    setTimeout(() => {
      cachedTokens.delete(keyStr);
      lastRefreshTime.delete(keyStr);
    }, 30000);
  }
}

// -----------------------------------------------------------------------------
// Main NextAuth configuration
// -----------------------------------------------------------------------------
export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' },
      },
      async authorize(credentials): Promise<AuthenticatedUser | null> {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        try {
          const authenticatedUser = await authService.authenticate({
            email: credentials.email,
            password: credentials.password,
          });

          return authenticatedUser;
        } catch {
          return null;
        }
      },
    }),
  ],
  session: {
    strategy: 'jwt',
    maxAge: 24 * 60 * 60, // 24 hours
  },
  secret: process.env.NEXTAUTH_SECRET,
  callbacks: {
    async redirect({ url, baseUrl }) {
      if (url.startsWith('/')) {
        return `${baseUrl}${url}`;
      }
      if (new URL(url).origin === baseUrl) {
        return url;
      }
      return baseUrl;
    },
    async jwt({ token, user, trigger, session }): Promise<JWT> {
      // Initial sign in
      if (user) {
        const authUser = user as AuthenticatedUser;

        if (process.env.NODE_ENV === 'development' && debugLogEnabled) {
          console.log('🔐 Initial sign-in, storing tokens in JWT');
          console.log('   - Access token:', authUser.accessToken ? '✓' : '✗');
          console.log('   - Refresh token:', authUser.refreshToken ? '✓' : '✗');
          console.log(
            '   - Access token expires:',
            new Date(authUser.accessTokenExpires).toISOString()
          );
          console.log(
            '   - Refresh token expires:',
            new Date(authUser.refreshTokenExpires).toISOString()
          );
        }

        return {
          ...token,
          sub: authUser.id,
          email: authUser.email,
          firstName: authUser.firstName,
          lastName: authUser.lastName,
          accessToken: authUser.accessToken,
          refreshToken: authUser.refreshToken,
          role: authUser.role,
          avatar: authUser.avatar,
          accessTokenExpires: authUser.accessTokenExpires,
          refreshTokenExpires: authUser.refreshTokenExpires,
        };
      }

      // Session update trigger - update user info
      if (trigger === 'update' && session?.user) {
        return {
          ...token,
          ...session.user,
        };
      }

      // Check and refresh if needed
      return await getOrRefreshToken(token);
    },
    async session({ session, token }) {
      if (token) {
        // Update user info
        session.user.id = token.sub!;
        session.user.email = token.email as string;
        session.user.firstName = token.firstName as string;
        session.user.lastName = token.lastName as string;
        session.user.role = token.role as Role;
        session.user.avatar = token.avatar as string;

        // Update session info
        session.accessToken = token.accessToken as string;
        session.refreshToken = token.refreshToken as string;
        session.error = token.error as string;
      }
      return session;
    },
  },
  pages: {
    signIn: '/sign-in',
  },
  debug: process.env.NODE_ENV === 'development',
};

// Simple token refresh function
async function refreshAccessToken(token: JWT): Promise<JWT> {
  try {
    if (process.env.NODE_ENV === 'development' && debugLogEnabled) {
      console.log('📡 Calling backend /auth/refresh endpoint...');
    }

    const refreshed = await authService.refreshToken(
      token.refreshToken as string
    );

    if (process.env.NODE_ENV === 'development' && debugLogEnabled) {
      console.log('📡 Backend returned new tokens successfully');
      console.log('   - New access token:', refreshed.accessToken ? '✓' : '✗');
      console.log(
        '   - New refresh token:',
        refreshed.refreshToken ? '✓' : '✗'
      );
      console.log(
        '   - Access token expire time:',
        refreshed.accessTokenExpireTime
      );
      console.log(
        '   - Refresh token expire time:',
        refreshed.refreshTokenExpireTime
      );
    }

    return {
      ...token,
      accessToken: refreshed.accessToken,
      accessTokenExpires: refreshed.accessTokenExpireTime * 1000,
      refreshToken: refreshed.refreshToken ?? token.refreshToken,
      refreshTokenExpires: refreshed.refreshTokenExpireTime * 1000,
      error: undefined,
    };
  } catch (error) {
    console.error('❌ Token refresh failed (user will be logged out):', error);
    if (process.env.NODE_ENV === 'development' && debugLogEnabled) {
      console.error('   - Refresh token used:', token.refreshToken);
      console.error(
        '   - Error details:',
        error instanceof Error ? error.message : String(error)
      );
    }
    return { ...token, error: 'RefreshAccessTokenError' };
  }
}
