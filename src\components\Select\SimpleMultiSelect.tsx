'use client';

import * as React from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Check, ChevronDown } from 'lucide-react';

export type SimpleOption = {
  value: string;
  label: string;
  disabled?: boolean;
};

export interface SimpleMultiSelectProps {
  options: SimpleOption[];
  value: string[];
  onChange: (next: string[]) => void;
  placeholder?: string;
  className?: string;
  groupLabel?: string;
}

export default function SimpleMultiSelect({
  options,
  value,
  onChange,
  placeholder = 'Select',
  className,
  groupLabel,
}: SimpleMultiSelectProps) {
  const [open, setOpen] = React.useState(false);
  const selectedSet = React.useMemo(() => new Set(value), [value]);
  const triggerRef = React.useRef<HTMLButtonElement>(null);
  const [maxWidth, setMaxWidth] = React.useState(0);

  React.useEffect(() => {
    if (triggerRef.current) {
      setMaxWidth(triggerRef.current.offsetWidth);
    }
  }, [triggerRef]);

  const toggle = React.useCallback(
    (val: string) => {
      if (selectedSet.has(val)) {
        onChange(value.filter(v => v !== val));
      } else {
        onChange([...value, val]);
      }
    },
    [onChange, selectedSet, value]
  );

  const triggerText = React.useMemo(() => {
    if (value.length === 0) return placeholder;
    if (value.length === 1) {
      const opt = options.find(o => o.value === value[0]);
      return opt?.label ?? placeholder;
    }
    const selected = options
      .filter(opt => value.includes(opt.value))
      .map(p => p.label);
    return selected.join(', ');
  }, [options, placeholder, value]);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger
        asChild
        ref={triggerRef}
        className="group"
        data-state={open}
      >
        <Button
          type="button"
          variant="outline"
          aria-expanded={open}
          className={cn('w-full justify-between h-10 border-input', className)}
        >
          <span
            className={cn(
              value.length === 0 && 'text-muted-foreground',
              'truncate max-w-3/4'
            )}
          >
            {triggerText}
          </span>
          <ChevronDown
            className={cn(
              'ml-2 h-4 w-4 shrink-0 opacity-60 rotate-0 transition-all duration-300',
              open && 'rotate-180'
            )}
          />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="p-0" align="start" style={{ width: maxWidth }}>
        <div className="py-2">
          {groupLabel && (
            <div className="px-9 pb-2 text-sm font-medium text-popover-foreground">
              {groupLabel}
            </div>
          )}
          <div
            className="max-h-64 overflow-y-auto px-1"
            onWheel={e => e.stopPropagation()}
          >
            {options.map(opt => {
              const selected = selectedSet.has(opt.value);
              return (
                <button
                  key={opt.value}
                  type="button"
                  disabled={opt.disabled}
                  onClick={() => toggle(opt.value)}
                  className={cn(
                    'w-full flex items-center gap-2 px-3 py-1.5 text-sm text-popover-foreground leading-5 hover:bg-accent disabled:opacity-50 disabled:pointer-events-none rounded-sm mb-1',
                    selected && 'bg-accent/60'
                  )}
                >
                  <Check
                    className={cn(
                      'h-4 w-4 opacity-0 shrink-0',
                      selected && 'opacity-100'
                    )}
                  />
                  <span className="truncate">{opt.label}</span>
                </button>
              );
            })}
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}
