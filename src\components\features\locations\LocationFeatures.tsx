'use client';

interface LocationFeaturesProps {
  data: {
    icon: React.ElementType;
    label: string;
  }[];
}

export default function LocationFeatures({ data }: LocationFeaturesProps) {
  return (
    <div className="flex flex-col sm:flex-row items-start sm:items-center gap-x-6 gap-y-3 text-xs sm:text-sm">
      {data.map(({ icon: Icon, label }) => (
        <div
          key={label}
          className="inline-flex items-center gap-2 text-gray-700"
        >
          <Icon className="h-4 w-4 text-primary" />
          <span>{label}</span>
        </div>
      ))}
    </div>
  );
}
