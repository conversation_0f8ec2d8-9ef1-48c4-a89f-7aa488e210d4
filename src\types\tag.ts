import { PropertyType, TagType } from './enum';

export interface Tag {
  id: string;
  name: string;
  type: TagType | string; // TagType enum value (e.g., 'building_type', 'structure_type')
  scope?: 'location' | 'both' | 'image'; // Tag scope from API
  color?: string;
  propertyTypes?: PropertyType[]; // Array of property types this tag applies to
}

export interface TagOption {
  value: string;
  label: string;
  color?: string;
  type?: TagType | string; // TagType enum value (e.g., 'building_type', 'structure_type')
}

export interface CreateTagData {
  name: string;
  type: string;
  color?: string;
  propertyType: PropertyType;
}

export interface UpdateTagData {
  name: string;
  type: string;
  color?: string;
  propertyType: PropertyType;
}
