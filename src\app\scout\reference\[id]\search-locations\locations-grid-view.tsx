'use client';

import InfiniteScroll from 'react-infinite-scroll-component';
import { Location } from '@/types';
import LocationCard, {
  LocationCardSkeleton,
} from '@/components/features/locations/LocationCard';
import { useSearchLocationPanel } from './search-location-context';
import { useRouter } from 'next/navigation';

interface LocationsGridViewProps {
  locations: Location[];
  fetchMoreData: () => void;
  hasMore: boolean;
  onAddLocation: (data: Location) => void;
  referenceId: string;
  loading: boolean;
}

export default function LocationsGridView({
  locations,
  fetchMoreData,
  hasMore,
  onAddLocation,
  referenceId,
  loading,
}: LocationsGridViewProps) {
  const router = useRouter();
  const { isPanelOpen } = useSearchLocationPanel();

  return (
    <div id="scrollableDiv" className="h-[calc(100vh-17.5rem)] overflow-auto">
      <InfiniteScroll
        scrollableTarget="scrollableDiv"
        dataLength={locations.length}
        next={fetchMoreData}
        hasMore={hasMore}
        loader={<p className="text-center py-4">Loading...</p>}
      >
        <div
          className={`grid grid-cols-1 xl:grid-cols-2 gap-6 2xl:grid-cols-3 ${isPanelOpen ? '2xl:grid-cols-2' : '2xl:grid-cols-3'}`}
        >
          {loading
            ? Array.from({ length: 6 }).map((_, index) => (
                <LocationCardSkeleton key={index} />
              ))
            : locations.map((location, index) => {
                return (
                  <LocationCard
                    key={index}
                    location={location}
                    onAddLocation={onAddLocation}
                    onViewDetail={location => {
                      router.push(
                        `/scout/reference/${referenceId}/view-details/${location.id}`
                      );
                    }}
                  />
                );
              })}
        </div>
      </InfiniteScroll>
    </div>
  );
}
