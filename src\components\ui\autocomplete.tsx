'use client';

import * as React from 'react';
import { Check, Loader2, X } from 'lucide-react';

import { cn } from '@/lib/utils';
import { Input, type InputProps } from '@/components/ui/input';
import {
  Popover,
  PopoverAnchor,
  PopoverContent,
} from '@/components/ui/popover';
import {
  Command,
  CommandGroup,
  CommandItem,
  CommandList,
} from '@/components/ui/command';

export type AutocompleteOption<TValue extends React.Key = string> = {
  /**
   * Unique option identifier submitted in forms.
   */
  value: TValue;
  /**
   * Primary text displayed inside the dropdown and input after selection.
   */
  label: string;
  /**
   * Optional secondary text displayed under the label.
   */
  description?: string;
  /**
   * Optional icon shown before the label.
   */
  icon?: React.ReactNode;
  /**
   * Optional keyword bag used for filtering.
   */
  keywords?: string[];
  /**
   * Disable interactions for the option.
   */
  disabled?: boolean;
};

export type AutocompleteInputProps<TValue extends React.Key = string> = {
  /**
   * Array of options rendered inside the dropdown list.
   */
  options: AutocompleteOption<TValue>[];
  /**
   * Currently selected option value (controlled).
   */
  selectedValue?: TValue | null;
  /**
   * Initial selected option value (uncontrolled).
   */
  defaultSelectedValue?: TValue | null;
  /**
   * Callback fired when the selected option changes.
   */
  onSelectedValueChange?: (
    value: TValue | null,
    option: AutocompleteOption<TValue> | null
  ) => void;
  /**
   * Current text value inside the input (controlled).
   */
  inputValue?: string;
  /**
   * Initial text value for the input (uncontrolled).
   */
  defaultInputValue?: string;
  /**
   * Callback fired whenever the text input changes.
   */
  onInputValueChange?: (value: string) => void;
  /**
   * Placeholder text displayed when the input is empty.
   */
  placeholder?: string;
  /**
   * Optional React node or string displayed when no matches are found.
   */
  emptyState?: React.ReactNode;
  /**
   * Optional React node or string displayed while loading results.
   */
  loadingState?: React.ReactNode;
  /**
   * Whether the dropdown should show a loading state.
   */
  isLoading?: boolean;
  /**
   * Show a clear button inside the input.
   */
  clearable?: boolean;
  /**
   * Optional content rendered before the input text (e.g., icon).
   */
  startAdornment?: React.ReactNode;
  /**
   * Optional content rendered after the input text.
   */
  endAdornment?: React.ReactNode;
  /**
   * Custom class applied to the wrapper div.
   */
  className?: string;
  /**
   * Custom class applied to the input element.
   */
  inputClassName?: string;
  /**
   * Custom class applied to the popover content element.
   */
  popoverClassName?: string;
  /**
   * Custom class applied to every option row.
   */
  optionClassName?: string;
  /**
   * How the filtering logic should match the text.
   */
  matchFrom?: 'start' | 'any';
  /**
   * Maximum amount of options that remain scrollable without stretching the panel.
   */
  maxVisibleOptions?: number;
  /**
   * Close the dropdown after the user selects an option.
   */
  closeOnSelect?: boolean;
  /**
   * Custom renderer for each option row.
   */
  renderOption?: (
    option: AutocompleteOption<TValue>,
    state: { isSelected: boolean; query: string }
  ) => React.ReactNode;
  /**
   * Enable "Add \"{query}\"" row when user types text that doesn't match an option.
   */
  allowCreateOption?: boolean;
  /**
   * Callback when user chooses the "Add \"{query}\"" row.
   * Should create the new option and return it (or a promise of it).
   */
  onCreateOption?: (
    label: string
  ) => AutocompleteOption<TValue> | Promise<AutocompleteOption<TValue>>;
  /**
   * Custom label for the create-option row.
   * Defaults to: Add "{query}"
   */
  createOptionLabel?: (label: string) => string;
} & Omit<InputProps, 'value' | 'defaultValue'>;

function mergeRefs<TValue>(
  ...refs: Array<React.Ref<TValue>>
): React.RefCallback<TValue> {
  return (node: TValue) => {
    refs.forEach(ref => {
      if (!ref) return;
      if (typeof ref === 'function') {
        ref(node);
      } else {
        (ref as React.MutableRefObject<TValue>).current = node;
      }
    });
  };
}

function highlightMatch(text: string, query: string) {
  if (!query) return text;

  const escaped = query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  const regex = new RegExp(`(${escaped})`, 'ig');

  return text.split(regex).map((segment, index) => {
    if (!segment) return null;
    const isMatch = segment.toLowerCase() === query.toLowerCase();
    return (
      <span
        key={`${segment}-${index}`}
        className={cn(
          isMatch && 'text-primary-600 dark:text-primary-500 font-semibold'
        )}
      >
        {segment}
      </span>
    );
  });
}

function AutocompleteInputInner<TValue extends React.Key = string>(
  {
    options,
    selectedValue: controlledSelectedValue,
    defaultSelectedValue = null,
    onSelectedValueChange,
    inputValue: controlledInputValue,
    defaultInputValue = '',
    onInputValueChange,
    placeholder = 'Search…',
    emptyState = 'No results found',
    loadingState,
    isLoading = false,
    clearable = true,
    startAdornment,
    endAdornment,
    className,
    inputClassName,
    popoverClassName,
    optionClassName,
    matchFrom = 'any',
    closeOnSelect = true,
    renderOption,
    disabled,
    onChange: externalOnChange,
    onClick: externalOnClick,
    onMouseDown: externalOnMouseDown,
    onFocus: externalOnFocus,
    onBlur: externalOnBlur,
    onKeyDown: externalOnKeyDown,
    allowCreateOption,
    onCreateOption,
    createOptionLabel,
    id,
    ...restInputProps
  }: AutocompleteInputProps<TValue>,
  forwardedRef: React.ForwardedRef<HTMLInputElement>
) {
  const [open, setOpen] = React.useState(false);
  const [activeIndex, setActiveIndex] = React.useState<number | null>(null);
  const [internalSelectedValue, setInternalSelectedValue] =
    React.useState<TValue | null>(defaultSelectedValue ?? null);
  const [internalInputValue, setInternalInputValue] = React.useState(
    defaultInputValue ?? ''
  );
  const [isCreatingOption, setIsCreatingOption] = React.useState(false);
  const listboxId = React.useId();
  const inputRef = React.useRef<HTMLInputElement>(null);
  const combinedRef = mergeRefs(inputRef, forwardedRef);

  const isInputControlled = controlledInputValue !== undefined;
  const isSelectionControlled = controlledSelectedValue !== undefined;

  const selectedValue = isSelectionControlled
    ? controlledSelectedValue
    : internalSelectedValue;
  const inputValue = isInputControlled
    ? (controlledInputValue ?? '')
    : internalInputValue;

  const selectedOption =
    React.useMemo<AutocompleteOption<TValue> | null>(() => {
      if (selectedValue === null || selectedValue === undefined) return null;
      return options.find(option => option.value === selectedValue) ?? null;
    }, [options, selectedValue]);

  React.useEffect(() => {
    if (isInputControlled) return;
    if (selectedOption) {
      setInternalInputValue(selectedOption.label);
    }
  }, [isInputControlled, selectedOption]);

  const normalizedQuery = inputValue.trim().toLowerCase();
  const filteredOptions = React.useMemo(() => {
    if (!normalizedQuery) return options;

    return options.filter(option => {
      const label = option.label.toLowerCase();
      const description = option.description?.toLowerCase() ?? '';
      const keywords = option.keywords?.join(' ').toLowerCase() ?? '';
      const haystack = [label, description, keywords].filter(Boolean).join(' ');

      if (matchFrom === 'start') {
        return label.startsWith(normalizedQuery);
      }

      return haystack.includes(normalizedQuery);
    });
  }, [options, normalizedQuery, matchFrom]);

  const hasExactLabelMatch = React.useMemo(() => {
    if (!normalizedQuery) return false;
    return options.some(
      option => option.label.toLowerCase() === normalizedQuery
    );
  }, [options, normalizedQuery]);

  React.useEffect(() => {
    if (!open || filteredOptions.length === 0) {
      setActiveIndex(null);
      return;
    }
    setActiveIndex(0);
  }, [open, filteredOptions.length]);

  const handleOpen = (nextOpen: boolean) => {
    if (disabled) return;
    setOpen(nextOpen);
  };

  const clearSelection = () => {
    if (!isSelectionControlled) {
      setInternalSelectedValue(null);
    }
    if (
      onSelectedValueChange &&
      selectedValue !== null &&
      selectedValue !== undefined
    ) {
      onSelectedValueChange(null, null);
    }
  };

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const nextValue = event.target.value;

    externalOnChange?.(event);

    if (!isInputControlled) {
      setInternalInputValue(nextValue);
    }
    onInputValueChange?.(nextValue);

    if (selectedValue !== null && selectedValue !== undefined) {
      clearSelection();
    }
    if (!open && !disabled) {
      setOpen(true);
    }
  };

  const handleMouseDown = (event: React.MouseEvent<HTMLInputElement>) => {
    externalOnMouseDown?.(event);
    if (event.defaultPrevented || disabled) return;
    // Open dropdown on first pointer down so a single click is enough
    handleOpen(true);
  };

  const handleSelect = (option: AutocompleteOption<TValue>) => {
    if (!isInputControlled) {
      setInternalInputValue(option.label);
    }
    if (!isSelectionControlled) {
      setInternalSelectedValue(option.value);
    }
    onInputValueChange?.(option.label);
    onSelectedValueChange?.(option.value, option);
    setActiveIndex(null);
    if (closeOnSelect) {
      setOpen(false);
      inputRef.current?.blur();
    }
  };

  const handleCreateOption = async () => {
    const rawLabel = inputValue.trim();
    if (!rawLabel || !onCreateOption || isCreatingOption) return;

    try {
      setIsCreatingOption(true);
      const created = await onCreateOption(rawLabel);
      if (!created) return;

      // Auto-select newly created option for this form
      if (!isInputControlled) {
        setInternalInputValue(created.label);
      }
      if (!isSelectionControlled) {
        setInternalSelectedValue(created.value);
      }
      onInputValueChange?.(created.label);
      onSelectedValueChange?.(created.value, created);

      if (closeOnSelect) {
        setOpen(false);
        inputRef.current?.blur();
      }
    } finally {
      setIsCreatingOption(false);
    }
  };

  const handleClear = () => {
    if (!isInputControlled) {
      setInternalInputValue('');
    }
    clearSelection();
    inputRef.current?.focus();
    if (!disabled) {
      setOpen(true);
    }
  };

  const scrollOptionIntoView = (index: number) => {
    if (index < 0) return;
    const optionNode = document.getElementById(`${listboxId}-option-${index}`);
    optionNode?.scrollIntoView({ block: 'nearest' });
  };

  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (!open && ['ArrowDown', 'ArrowUp'].includes(event.key)) {
      handleOpen(true);
    }

    if (event.key === 'ArrowDown') {
      event.preventDefault();
      setActiveIndex(prev => {
        const next =
          prev === null ? 0 : Math.min(prev + 1, filteredOptions.length - 1);
        scrollOptionIntoView(next);
        return next;
      });
      return;
    }

    if (event.key === 'ArrowUp') {
      event.preventDefault();
      setActiveIndex(prev => {
        if (prev === null) return filteredOptions.length - 1;
        const next = Math.max(prev - 1, 0);
        scrollOptionIntoView(next);
        return next;
      });
      return;
    }

    if (event.key === 'Enter' && activeIndex !== null) {
      event.preventDefault();
      const nextOption = filteredOptions[activeIndex];
      if (nextOption) {
        handleSelect(nextOption);
      }
      return;
    }

    if (event.key === 'Escape') {
      handleOpen(false);
      inputRef.current?.blur();
    }
  };

  const handleBlur = (event: React.FocusEvent<HTMLInputElement>) => {
    externalOnBlur?.(event);
    requestAnimationFrame(() => {
      if (!document.activeElement?.closest('[data-slot="command"]')) {
        handleOpen(false);
      }
    });
  };

  const handleFocus = (event: React.FocusEvent<HTMLInputElement>) => {
    externalOnFocus?.(event);
    handleOpen(true);
  };

  const nothingToShow = !isLoading && filteredOptions.length === 0;

  const queryForHighlight = inputValue.trim();

  return (
    <div className={cn('flex w-full flex-col gap-1.5', className)}>
      <Popover open={open} modal={false}>
        <PopoverAnchor asChild>
          <div className="relative">
            {startAdornment && (
              <div className="pointer-events-none absolute inset-y-0 left-3 flex items-center">
                {startAdornment}
              </div>
            )}
            <Input
              ref={combinedRef}
              id={id}
              role="combobox"
              aria-expanded={open}
              aria-autocomplete="list"
              aria-controls={listboxId}
              aria-activedescendant={
                activeIndex !== null
                  ? `${listboxId}-option-${activeIndex}`
                  : undefined
              }
              placeholder={placeholder}
              value={inputValue}
              disabled={disabled}
              onMouseDown={handleMouseDown}
              onFocus={handleFocus}
              onBlur={handleBlur}
              onKeyDown={event => {
                handleKeyDown(event);
                externalOnKeyDown?.(event);
              }}
              autoComplete="off"
              className={cn(
                startAdornment && 'pl-10',
                (clearable || endAdornment) && 'pr-10',
                inputClassName
              )}
              {...restInputProps}
              onChange={handleInputChange}
              onClick={externalOnClick}
            />
            {(clearable && inputValue && !disabled) || endAdornment ? (
              <div className="absolute inset-y-0 right-3 flex items-center gap-2">
                {clearable && inputValue && !disabled && (
                  <button
                    type="button"
                    onClick={handleClear}
                    className="text-muted-foreground hover:text-foreground transition-colors"
                    aria-label="Clear value"
                  >
                    <X className="size-4" />
                  </button>
                )}
                {endAdornment}
              </div>
            ) : null}
          </div>
        </PopoverAnchor>
        <PopoverContent
          align="start"
          sideOffset={4}
          className={cn(
            'w-[var(--radix-popover-trigger-width)] p-0',
            'rounded-lg border border-slate-200 bg-white shadow-[0px_8px_25px_rgba(15,23,41,0.08)]',
            popoverClassName
          )}
          onOpenAutoFocus={event => event.preventDefault()}
        >
          <Command shouldFilter={false} className="w-full" data-slot="command">
            <CommandList
              id={listboxId}
              role="listbox"
              aria-live="polite"
              className="max-h-full overflow-y-auto"
            >
              {allowCreateOption &&
                onCreateOption &&
                !isLoading &&
                normalizedQuery &&
                !hasExactLabelMatch && (
                  <CommandItem
                    value={inputValue}
                    disabled={isCreatingOption}
                    onSelect={handleCreateOption}
                    className="cursor-pointer rounded-md px-2 py-2 text-sm text-primary-700 hover:bg-primary-50 aria-disabled:opacity-60"
                  >
                    {createOptionLabel ? (
                      createOptionLabel(inputValue.trim())
                    ) : (
                      <>
                        <span className="max-w-40 truncate">
                          Add &quot;{inputValue.trim()}
                        </span>
                        <span className="inline-block">&quot;</span>
                      </>
                    )}
                  </CommandItem>
                )}
              {isLoading ? (
                <div className="flex items-center justify-center gap-2 py-6 text-sm text-muted-foreground">
                  {loadingState ?? (
                    <>
                      <Loader2 className="size-4 animate-spin" />
                      Fetching results…
                    </>
                  )}
                </div>
              ) : null}
              {!isLoading && nothingToShow ? (
                <div className="py-6 text-center text-sm text-muted-foreground">
                  {emptyState}
                </div>
              ) : null}
              {!isLoading && filteredOptions.length > 0 && (
                <CommandGroup className="p-1">
                  {filteredOptions.map((option, index) => {
                    const isSelected = selectedOption?.value === option.value;
                    const isActive = activeIndex === index;
                    const optionId = `${listboxId}-option-${index}`;
                    const defaultRender = (
                      <div className="flex-1">
                        <p className="text-sm font-medium text-slate-900">
                          {highlightMatch(option.label, queryForHighlight)}
                        </p>
                        {option.description && (
                          <p className="text-xs text-muted-foreground">
                            {highlightMatch(
                              option.description,
                              queryForHighlight
                            )}
                          </p>
                        )}
                      </div>
                    );

                    return (
                      <CommandItem
                        id={optionId}
                        key={option.value as React.Key}
                        value={String(option.value)}
                        disabled={option.disabled}
                        onSelect={() => handleSelect(option)}
                        onMouseEnter={() => setActiveIndex(index)}
                        className={cn(
                          'cursor-pointer rounded-md pl-8 pr-2 py-2 text-sm transition-colors',
                          isActive && 'bg-primary-50 text-slate-900',
                          optionClassName
                        )}
                      >
                        <div className="flex w-full items-center gap-2">
                          {option.icon && (
                            <span className="text-muted-foreground flex size-8 items-center justify-center">
                              {option.icon}
                            </span>
                          )}
                          {renderOption
                            ? renderOption(option, {
                                isSelected,
                                query: queryForHighlight,
                              })
                            : defaultRender}
                          <Check
                            className={cn(
                              'size-4 shrink-0 text-primary opacity-0 transition-opacity',
                              isSelected && 'opacity-100'
                            )}
                          />
                        </div>
                      </CommandItem>
                    );
                  })}
                </CommandGroup>
              )}
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
}

type AutocompleteInputComponent = (<TValue extends React.Key = string>(
  props: AutocompleteInputProps<TValue> & React.RefAttributes<HTMLInputElement>
) => React.ReactElement) & {
  displayName?: string;
};

const AutocompleteInput = React.forwardRef(
  AutocompleteInputInner
) as AutocompleteInputComponent;

AutocompleteInput.displayName = 'AutocompleteInput';

export { AutocompleteInput };
