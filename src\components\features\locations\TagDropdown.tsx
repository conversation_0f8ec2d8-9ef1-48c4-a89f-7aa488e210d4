'use client';

import { useState, useMemo, useEffect } from 'react';
import SearchBox, {
  SearchBoxOption,
  SearchBoxGroup,
} from '@/components/SearchBox';
import { Tag } from '@/types/tag';
import { Plus } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { toastInfo } from '@/lib/toast';

interface TagDropdownProps {
  imageId?: string; // Optional for single image mode
  currentTags?: Tag[];
  onTagsChange?: (imageId: string, tags: Tag[]) => void;
  trigger?: React.ReactNode;
  // Bulk mode props
  bulkMode?: boolean;
  selectedImageIds?: string[];
  onBulkTagsChange?: (imageIds: string[], tags: Tag[]) => void;
}

// Mock tags similar to the Figma design
const MOCK_TAGS: SearchBoxGroup[] = [
  {
    label: 'Recommended based on property type',
    options: [
      { value: 'bedroom', label: 'Bedroom' },
      { value: 'bathroom', label: 'Bathroom' },
      { value: 'living-room', label: 'Living room' },
      { value: 'laundry-room', label: 'Laundry room' },
      { value: 'kitchen', label: 'Kitchen' },
      { value: 'dining-room', label: 'Dining room' },
      { value: 'office', label: 'Office' },
      { value: 'garage', label: 'Garage' },
    ],
  },
  {
    label: 'All tags',
    options: [
      { value: 'clothing-store', label: 'Clothing store' },
      { value: 'outdoor-pool', label: 'Outdoor pool' },
      { value: 'gym', label: 'Gym' },
      { value: 'restaurant', label: 'Restaurant' },
      { value: 'cafe', label: 'Cafe' },
      { value: 'parking', label: 'Parking' },
      { value: 'elevator', label: 'Elevator' },
      { value: 'balcony', label: 'Balcony' },
      { value: 'garden', label: 'Garden' },
      { value: 'rooftop', label: 'Rooftop' },
    ],
  },
];

export default function TagDropdown({
  imageId,
  currentTags = [],
  onTagsChange,
  trigger,
  bulkMode = false,
  selectedImageIds = [],
  onBulkTagsChange,
}: TagDropdownProps) {
  const [open, setOpen] = useState(false);
  const [bulkSelectedValues, setBulkSelectedValues] = useState<string[]>([]);
  const [hasChanged, setHasChanged] = useState(false);

  // Convert current tags to selected values
  // In bulk mode, track selected values internally for multi-select
  const selectedValues = useMemo(() => {
    if (bulkMode) {
      return bulkSelectedValues; // Use tracked selection in bulk mode
    }
    return currentTags.map(tag => tag.id);
  }, [currentTags, bulkMode, bulkSelectedValues]);

  // Show toast when dropdown closes if a change was made
  useEffect(() => {
    if (!open && hasChanged) {
      toastInfo('Tags updated.', { iconClassName: 'text-[#EF4444]' });
      setHasChanged(false);
    }
  }, [open, hasChanged]);

  const handleMultiSelect = (selectedOptions: SearchBoxOption[]) => {
    // Update selected values for bulk mode to show check icons
    if (bulkMode) {
      const newSelectedValues = selectedOptions.map(opt => opt.value);
      setBulkSelectedValues(newSelectedValues);
    }

    // Convert SearchBoxOption[] to Tag[]
    const newTags: Tag[] = selectedOptions.map(option => {
      // In bulk mode, always create new tags (replacing)
      // In single mode, preserve existing tag properties if available
      if (bulkMode) {
        return {
          id: option.value,
          name: option.label,
          type: 'image',
          scope: 'image',
        };
      }
      const existingTag = currentTags.find(tag => tag.id === option.value);
      return (
        existingTag || {
          id: option.value,
          name: option.label,
          type: 'image',
          scope: 'image',
        }
      );
    });

    // Check if tags actually changed before calling callbacks
    const currentTagIds = currentTags.map(tag => tag.id).sort();
    const newTagIds = newTags.map(tag => tag.id).sort();
    const tagsChanged =
      JSON.stringify(currentTagIds) !== JSON.stringify(newTagIds);

    if (tagsChanged) {
      if (bulkMode && onBulkTagsChange && selectedImageIds.length > 0) {
        // Bulk mode: replace tags for all selected images
        onBulkTagsChange(selectedImageIds, newTags);
        setHasChanged(true);
      } else if (imageId && onTagsChange) {
        // Single image mode: update tags for one image
        onTagsChange(imageId, newTags);
        setHasChanged(true);
      }
    }
  };

  const defaultTrigger = (
    <Badge variant="neutral" className="font-semibold cursor-pointer">
      Add tag
      <Plus className="h-4 w-4" />
    </Badge>
  );

  // Reset bulk selection when dropdown closes in bulk mode
  const handleOpenChange = (newOpen: boolean) => {
    setOpen(newOpen);
    if (!newOpen && bulkMode) {
      // Reset selection when closing in bulk mode
      setBulkSelectedValues([]);
    }
  };

  return (
    <SearchBox
      groups={MOCK_TAGS}
      multiSelect={true}
      selectedValues={selectedValues}
      onMultiSelect={handleMultiSelect}
      placeholder="Search tags"
      emptyMessage="No tags found"
      open={open}
      onOpenChange={handleOpenChange}
      trigger={trigger || defaultTrigger}
      popoverClassName="w-[329px]"
    />
  );
}
