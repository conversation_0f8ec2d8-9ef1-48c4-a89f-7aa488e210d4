'use client';

import { useState, useCallback, useEffect } from 'react';
import { DateRange } from 'react-day-picker';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/auth-context';
import { Role, Reference } from '@/types';
import { useProductionHouseService } from '@/hooks/use-services';
import { ProductionHouse } from '@/lib/services/production-service';
import AddReferenceModal from '@/components/project/AddReferenceModal';
import { DashboardHeader } from '@/components/dashboard/DashboardHeader';
import { DashboardFilters } from '@/components/dashboard/DashboardFilters';
import { ProjectGrid } from '@/components/dashboard/ProjectGrid';
import { ProjectSkeletonGrid } from '@/components/dashboard/ProjectSkeleton';
import { EmptyProjectList } from '@/components/dashboard/EmptyProjectList';
import { NoProjectFound } from '@/components/dashboard/NoProjectFound';
import { useDashboardProjects } from '@/hooks/useDashboardProjects';

export function ScoutProductionAdminDashboardView() {
  const { user } = useAuth();
  const router = useRouter();
  const productionHouseService = useProductionHouseService();

  const [search, setSearch] = useState<string>('');
  const [status, setStatus] = useState<string>('all');
  const [dateRange, setDateRange] = useState<DateRange | undefined>();
  const [isAddReferenceModalOpen, setIsAddReferenceModalOpen] =
    useState<boolean>(false);
  const [productionHouseOptions, setProductionHouseOptions] = useState<
    ProductionHouse[]
  >([]);

  const isScout = user?.role === Role.Scout;
  const isProductionAdmin = user?.role === Role.ProductionAdmin;

  const {
    projectData,
    isLoading,
    isInitialLoading,
    isLoadingMore,
    hasMore,
    totalCount,
    hasFilters,
    fetchMoreData,
  } = useDashboardProjects({
    search,
    status,
    dateRange,
  });

  const handleCreateProject = () => {
    setIsAddReferenceModalOpen(true);
  };

  const handleAddReferenceSuccess = (response: Reference) => {
    // Both Scout and ProductionAdmin use scout reference routes for now
    // TODO: Update to shared /project/[projectId] route when implemented
    router.push(`/scout/reference/${response.id}`);
  };

  const fetchProductionHouses = useCallback(async () => {
    try {
      const response = await productionHouseService.getProductionHouses();
      setProductionHouseOptions(response);
    } catch (error) {
      console.error('Error fetching production houses:', error);
    }
  }, [productionHouseService]);

  useEffect(() => {
    // Fetch production houses for both Scout and ProductionAdmin
    // Scouts need it for filtering, both need it for creating projects
    if (isScout || isProductionAdmin) {
      fetchProductionHouses();
    }
  }, [isScout, isProductionAdmin, fetchProductionHouses]);

  return (
    <>
      <div className="w-full flex justify-center">
        <div className="space-y-6 xl:w-[70.5rem] p-6 xl:px-0 flex flex-col">
          <DashboardHeader userName={user?.firstName} totalCount={totalCount} />

          {isInitialLoading ? (
            <ProjectSkeletonGrid count={3} />
          ) : totalCount === 0 && !hasFilters ? (
            <EmptyProjectList onCreateProject={handleCreateProject} />
          ) : (
            <div className="flex flex-col w-full space-y-6 flex-1">
              <DashboardFilters
                search={search}
                onSearchChange={setSearch}
                status={status}
                onStatusChange={setStatus}
                dateRange={dateRange}
                onDateRangeChange={setDateRange}
                onCreateProject={handleCreateProject}
              />

              {totalCount > 0 && (
                <p className="text-base font-medium text-slate-950">
                  {totalCount} {totalCount === 1 ? 'project' : 'projects'}
                </p>
              )}

              {projectData.length === 0 && !isLoading && hasFilters ? (
                <NoProjectFound />
              ) : (
                <ProjectGrid
                  projects={projectData}
                  isLoading={isLoading}
                  isLoadingMore={isLoadingMore}
                  hasMore={hasMore}
                  onLoadMore={fetchMoreData}
                />
              )}
            </div>
          )}
        </div>
      </div>
      {(isScout || isProductionAdmin) && (
        <AddReferenceModal
          productionHouseOptions={productionHouseOptions}
          open={isAddReferenceModalOpen}
          onOpenChange={setIsAddReferenceModalOpen}
          onSuccess={handleAddReferenceSuccess}
        />
      )}
    </>
  );
}
