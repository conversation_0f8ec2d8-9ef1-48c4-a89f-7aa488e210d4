'use client';

import * as React from 'react';
import {
  CheckIcon,
  ChevronDownIcon,
  PlusIcon,
  SearchIcon,
  XIcon,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { TagOption } from '@/types/tag';
import { DeleteIcon, PencilIcon } from '@/lib/icons';
import { IconButton } from '../shared/IconButton';
import { Input } from './input';

interface SearchableSelectProps {
  options: TagOption[];
  value?: string | string[];
  onValueChange: (value: string | string[]) => void;
  placeholder?: string;
  searchPlaceholder?: string;
  emptyText?: string;
  onCreateNew?: (name: string) => void;
  createText?: (name: string) => string;
  disabled?: boolean;
  className?: string;
  multiple?: boolean;
  editable?: boolean;
  onDelete?: (value: string) => void;
  onEdit?: (oldValue: string, newLabel: string) => void;
}

export function SearchableSelect({
  options,
  value,
  onValueChange,
  placeholder = 'Select option...',
  searchPlaceholder = 'Search...',
  emptyText = 'No results found.',
  onCreateNew,
  createText = (name: string) => `Create "${name.trim()}"`,
  disabled = false,
  className,
  multiple = false,
  editable = false,
  onDelete,
  onEdit,
}: SearchableSelectProps) {
  const [open, setOpen] = React.useState(false);
  const [search, setSearch] = React.useState('');
  const [hoveredItem, setHoveredItem] = React.useState<string | null>(null);
  const [editingItem, setEditingItem] = React.useState<string | null>(null);
  const [editValue, setEditValue] = React.useState('');
  const [isCreating, setIsCreating] = React.useState(false);

  React.useEffect(() => {
    if (open) {
      setSearch('');
    }
  }, [open]);

  // Handle both single and multiple values
  const selectedValues = React.useMemo(() => {
    if (!value) return [];
    return Array.isArray(value) ? value : [value];
  }, [value]);

  const selectedOptions = options.filter(opt =>
    selectedValues.includes(opt.value)
  );

  // Display text for trigger button
  const displayText = React.useMemo(() => {
    if (selectedOptions.length === 0) return placeholder;
    if (multiple) {
      if (selectedOptions.length === 1) return selectedOptions[0].label;
      return `${selectedOptions.length} selected`;
    }
    return selectedOptions[0].label;
  }, [selectedOptions, placeholder, multiple]);

  // Filter options based on search
  const filteredOptions = React.useMemo(() => {
    if (!search.trim()) return options;
    const searchLower = search.toLowerCase();
    return options.filter(opt => opt.label.toLowerCase().includes(searchLower));
  }, [options, search]);

  // Check if search doesn't match any option
  const showCreateNew =
    onCreateNew &&
    search.trim() &&
    !filteredOptions.some(
      opt => opt.label.toLowerCase() === search.toLowerCase()
    );

  const handleSelect = (selectedValue: string) => {
    if (multiple) {
      const newValues = selectedValues.includes(selectedValue)
        ? selectedValues.filter(v => v !== selectedValue)
        : [...selectedValues, selectedValue];
      onValueChange(newValues);
      // Don't close dropdown for multiple select
    } else {
      onValueChange(selectedValue);
      setOpen(false);
      setSearch('');
    }
  };

  const handleCreateNew = async () => {
    if (onCreateNew && search.trim() && !isCreating) {
      setIsCreating(true);
      try {
        await Promise.resolve(onCreateNew(search.trim()));
        setSearch('');
        if (!multiple) {
          setOpen(false);
        }
      } finally {
        setIsCreating(false);
      }
    }
  };

  const handleDelete = (optionValue: string, e: React.MouseEvent) => {
    e.stopPropagation();
    if (onDelete) {
      onDelete(optionValue);
    }
  };

  const handleEditStart = (option: TagOption, e: React.MouseEvent) => {
    e.stopPropagation();
    setEditingItem(option.value);
    setEditValue(option.label);
  };

  const handleEditSubmit = (optionValue: string, e: React.MouseEvent) => {
    e.stopPropagation();
    if (
      onEdit &&
      editValue.trim() &&
      editValue !== options.find(o => o.value === optionValue)?.label
    ) {
      onEdit(optionValue, editValue.trim());
    }
    setEditingItem(null);
    setEditValue('');
  };

  const handleEditCancel = (e: React.MouseEvent) => {
    e.stopPropagation();
    setEditingItem(null);
    setEditValue('');
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn(
            'w-full justify-between h-[50px] text-black font-normal border-border hover:bg-accent/40 hover:border-border',
            selectedOptions.length === 0 && 'text-muted-foreground',
            className
          )}
          disabled={disabled}
        >
          <span className="truncate">{displayText}</span>
          <ChevronDownIcon
            className={cn(
              'ml-2 h-4 w-4 shrink-0 text-muted-foreground transition-transform duration-200',
              open && 'rotate-180'
            )}
          />
        </Button>
      </PopoverTrigger>
      <PopoverContent
        className="w-(--radix-popover-trigger-width) p-0"
        align="start"
        sideOffset={1}
      >
        <Command shouldFilter={false}>
          <div className="flex items-center gap-2 border-b px-3 py-1">
            <SearchIcon className="h-4 w-4 shrink-0 opacity-50" />
            <input
              value={search}
              onChange={e => setSearch(e.target.value)}
              placeholder={searchPlaceholder}
              className="flex h-10 w-full rounded-md bg-transparent py-3 text-sm text-slate-950 outline-none placeholder:text-popover-foreground/50 disabled:cursor-not-allowed disabled:opacity-50"
            />
          </div>
          {showCreateNew && (
            <div className="border-b cursor-pointer">
              <Button
                variant="ghost"
                onClick={handleCreateNew}
                isLoading={isCreating}
                disabled={isCreating}
                className="flex w-full items-center gap-2 px-3 py-2 text-sm text-slate-900 leading-5 font-normal transition-colors"
              >
                <PlusIcon className="h-4 w-4 shrink-0" />
                <span className="flex-1 text-left">
                  {isCreating ? 'Creating…' : createText(search.trim())}
                </span>
              </Button>
            </div>
          )}
          <CommandList>
            <CommandEmpty>{emptyText}</CommandEmpty>
            <CommandGroup className="p-1">
              {filteredOptions.map(option => {
                const isSelected = selectedValues.includes(option.value);
                const isHovered = hoveredItem === option.value;
                const isEditing = editingItem === option.value;
                return (
                  <CommandItem
                    key={option.value}
                    value={option.value}
                    onSelect={() => !isEditing && handleSelect(option.value)}
                    onMouseEnter={() =>
                      editable && setHoveredItem(option.value)
                    }
                    onMouseLeave={() => editable && setHoveredItem(null)}
                    className={cn(
                      'pl-8 py-1.5 relative flex items-center justify-between gap-2',
                      isSelected && !isEditing && 'bg-primary-50!',
                      !isEditing
                        ? 'data-[selected=true]:bg-primary-50! hover:bg-primary-50!'
                        : 'data-[selected=true]:bg-transparent!'
                    )}
                  >
                    <div className="flex items-center flex-1 min-w-0">
                      <CheckIcon
                        className={cn(
                          'absolute left-2 h-4 w-4',
                          isSelected ? 'opacity-100' : 'opacity-0'
                        )}
                      />
                      {isEditing ? (
                        <Input
                          value={editValue}
                          onChange={e => setEditValue(e.target.value)}
                          onClick={e => e.stopPropagation()}
                          onKeyDown={e => {
                            if (e.key === 'Enter') {
                              e.stopPropagation();
                              handleEditSubmit(
                                option.value,
                                e as unknown as React.MouseEvent<
                                  HTMLElement,
                                  MouseEvent
                                >
                              );
                            } else if (e.key === 'Escape') {
                              e.stopPropagation();
                              handleEditCancel(
                                e as unknown as React.MouseEvent<
                                  HTMLElement,
                                  MouseEvent
                                >
                              );
                            }
                          }}
                          className="flex-1 h-9 px-2 py-1 text-sm border border-border rounded outline-none focus:ring-2 focus:ring-primary"
                          autoFocus
                        />
                      ) : (
                        <span className="truncate">{option.label}</span>
                      )}
                    </div>
                    {editable && isEditing && (
                      <div className="flex items-center gap-1 shrink-0">
                        <button
                          onClick={e => handleEditSubmit(option.value, e)}
                          className="p-1 hover:bg-green-100 rounded transition-colors"
                        >
                          <CheckIcon className="h-3.5 w-3.5 text-green-600" />
                        </button>
                        <button
                          onClick={handleEditCancel}
                          className="p-1 hover:bg-red-100 rounded transition-colors"
                        >
                          <XIcon className="h-3.5 w-3.5 text-red-600" />
                        </button>
                      </div>
                    )}
                    {editable && !isEditing && isHovered && (
                      <div className="flex items-center gap-2 shrink-0">
                        <IconButton
                          onClick={e => handleEditStart(option, e)}
                          className="size-5"
                          size="small"
                        >
                          <PencilIcon className="size-4 text-primary" />
                        </IconButton>
                        <IconButton
                          onClick={e => handleDelete(option.value, e)}
                          size="small"
                          className="size-5"
                        >
                          <DeleteIcon className="size-4 text-primary" />
                        </IconButton>
                      </div>
                    )}
                  </CommandItem>
                );
              })}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
