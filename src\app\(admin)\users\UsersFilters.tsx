'use client';

import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { SearchIcon } from '@/lib/icons';

interface UsersFiltersProps {
  search: string;
  role: string;
  status: string;
  isActiveView: boolean;
  onSearchChange: (value: string) => void;
  onRoleChange: (value: string) => void;
  onStatusChange: (value: string) => void;
}

export function UsersFilters({
  search,
  role,
  status,
  isActiveView,
  onSearchChange,
  onRoleChange,
  onStatusChange,
}: UsersFiltersProps) {
  return (
    <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:flex-1">
      <div className="relative w-[16rem]">
        <SearchIcon className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 h-4 w-4" />
        <Input
          placeholder="Search by name or email..."
          value={search ?? ''}
          onChange={e => onSearchChange(e.target.value)}
          className="pl-9"
          debounceMs={300}
        />
      </div>
      <div className="flex flex-wrap items-center gap-2">
        <Select value={role} onValueChange={onRoleChange}>
          <SelectTrigger aria-label="Category">
            <SelectValue placeholder="Category" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Roles</SelectItem>
            <SelectItem value="super_admin">Super Admin</SelectItem>
            <SelectItem value="production_admin">Production Admin</SelectItem>
            <SelectItem value="scout">Scout</SelectItem>
            <SelectItem value="property_owner">Property Owner</SelectItem>
          </SelectContent>
        </Select>

        {isActiveView && (
          <Select value={status} onValueChange={onStatusChange}>
            <SelectTrigger aria-label="Status">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="pending">Pending</SelectItem>
              <SelectItem value="disabled">Disabled</SelectItem>
            </SelectContent>
          </Select>
        )}
      </div>
    </div>
  );
}
