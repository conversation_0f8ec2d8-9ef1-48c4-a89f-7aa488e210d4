'use client';
import { Reference, ReferenceItem, ReferenceWithFeedback } from '@/types';
import { Button } from '../ui/button';
import { ArrowLeft } from 'lucide-react';
import { Heading } from '../ui/typography';
import { Body } from '../ui/typography';
import { ShareIcon } from '@/lib/icons';
import { useRouter } from 'next/navigation';
import ShareReferenceListModal from './ShareReferenceListModal';
import { useState } from 'react';
import { formatDateRange } from '@/lib/utils';
import ReferencePreviewItem from './ReferencePreviewItem';
import ConfirmDialog from '@/components/shared/ConfirmDialog';
import { toastSuccess } from '@/lib/toast';
import ImageFullScreen from '../ImageFullScreen';
import { AddCommentModal } from '../comment/AddCommentModal';
import { CommentModal } from '../comment/CommentModal';

interface ReferenceListPreviewProps {
  reference: Reference;
  isViewer?: boolean;
}

export default function ReferenceListPreview({
  reference,
  isViewer = false,
}: ReferenceListPreviewProps) {
  const router = useRouter();
  const [shareReferenceListOpen, setShareReferenceListOpen] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [submitDialogOpen, setSubmitDialogOpen] = useState(false);
  const [detailScreen, setDetailScreen] = useState({
    open: false,
    index: 0,
  });
  const [selectedItem, setSelectedItem] = useState<ReferenceItem>();
  const [commentModalOpen, setCommentModalOpen] = useState(false);
  const [referenceWithFeedback, setReferenceWithFeedback] =
    useState<ReferenceWithFeedback>({
      ...reference,
      items: reference.items.map(item => ({
        ...item,
        like: false,
        dislike: false,
      })),
    });

  const handleGoBack = () => {
    router.back();
  };

  const handleLike = (itemId: string) => {
    setReferenceWithFeedback(prev => ({
      ...prev,
      items: prev.items.map(item =>
        item.id === itemId
          ? { ...item, like: !item.like, dislike: false }
          : item
      ),
    }));
  };

  const handleDislike = (itemId: string) => {
    setReferenceWithFeedback(prev => ({
      ...prev,
      items: prev.items.map(item =>
        item.id === itemId
          ? { ...item, dislike: !item.dislike, like: false }
          : item
      ),
    }));
  };

  const handleSelect = (itemId: string, index: number) => {
    setSelectedItem(
      referenceWithFeedback.items.find(item => item.id === itemId)
    );
    setDetailScreen({ open: true, index });
  };

  const handleOpenComment = (itemId: string) => {
    setSelectedItem(
      referenceWithFeedback.items.find(item => item.id === itemId)
    );
    setCommentModalOpen(true);
  };

  const handleSubmitFeedback = () => {
    console.log('handleSubmitFeedback');
    setSubmitDialogOpen(false);
    toastSuccess('Feedback submitted successfully.');
    setIsSubmitted(true);
  };

  return (
    <div>
      <div className="flex gap-4 items-center justify-between">
        <div className="flex gap-2 items-center">
          {!isViewer && (
            <Button variant="ghost" onClick={handleGoBack}>
              <ArrowLeft className="size-4" />
            </Button>
          )}
          <div className="flex flex-col">
            <Heading level={3}>
              {reference.projectName} {isViewer ? '' : 'preview'}
            </Heading>
            <Body className="text-[#737373] text-sm leading-5">
              {reference.productionHouse.name} •{' '}
              {formatDateRange(
                new Date(reference.shootDateStart),
                new Date(reference.shootDateEnd)
              )}{' '}
              • {reference.items?.length ?? 0}{' '}
              {reference.items?.length && reference.items.length > 1
                ? 'references'
                : 'reference'}
            </Body>
          </div>
        </div>
        {isViewer ? (
          <Button
            variant="default"
            onClick={() => setSubmitDialogOpen(true)}
            disabled={isSubmitted}
          >
            Submit Feedback
          </Button>
        ) : (
          <Button
            variant="default"
            onClick={() => setShareReferenceListOpen(true)}
          >
            <ShareIcon />
            Share List
          </Button>
        )}
      </div>
      <div className="flex flex-col gap-4 mt-11">
        {referenceWithFeedback.items?.map(item => (
          <ReferencePreviewItem
            key={item.id}
            item={item}
            isViewer={isViewer}
            isSubmitted={isSubmitted}
            onLike={handleLike}
            onDislike={handleDislike}
            onSelect={handleSelect}
            onOpenComment={handleOpenComment}
          />
        ))}
      </div>
      <ShareReferenceListModal
        open={shareReferenceListOpen}
        onOpenChange={setShareReferenceListOpen}
        referenceId={reference.id}
      />
      <ConfirmDialog
        open={submitDialogOpen}
        onOpenChange={setSubmitDialogOpen}
        title="Submit feedback?"
        description="Once you submit, your feedback will be sent to the scout."
        confirmText="Submit"
        cancelText="Keep editing"
        onConfirm={handleSubmitFeedback}
        onCancel={() => setSubmitDialogOpen(false)}
      />
      <ImageFullScreen
        open={detailScreen.open}
        images={selectedItem?.images.map(image => image.image) ?? []}
        onOpenChange={open =>
          setDetailScreen({ open, index: detailScreen.index })
        }
        startIndex={detailScreen.index}
      />
      {selectedItem && (
        <AddCommentModal
          comments={['']}
          location={selectedItem.location}
          open={commentModalOpen}
          onOpenChange={setCommentModalOpen}
        />
      )}
      <CommentModal
        open={commentModalOpen}
        onOpenChange={setCommentModalOpen}
        reference={reference}
        referenceItem={selectedItem}
      />
    </div>
  );
}
