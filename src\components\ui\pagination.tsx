import * as React from 'react';
import {
  ChevronLeftIcon,
  ChevronRightIcon,
  MoreHorizontalIcon,
} from 'lucide-react';

import { cn } from '@/lib/utils';
import { Button, buttonVariants } from '@/components/ui/button';

function Pagination({ className, ...props }: React.ComponentProps<'nav'>) {
  return (
    <nav
      role="navigation"
      aria-label="pagination"
      data-slot="pagination"
      className={cn('mx-auto flex w-full justify-center', className)}
      {...props}
    />
  );
}

function PaginationContent({
  className,
  ...props
}: React.ComponentProps<'ul'>) {
  return (
    <ul
      data-slot="pagination-content"
      className={cn('flex flex-row items-center gap-2', className)}
      {...props}
    />
  );
}

function PaginationItem({ ...props }: React.ComponentProps<'li'>) {
  return <li data-slot="pagination-item" {...props} />;
}

type PaginationLinkProps = {
  isActive?: boolean;
} & Pick<React.ComponentProps<typeof Button>, 'size'> &
  React.ComponentProps<'a'>;

function PaginationLink({
  className,
  isActive,
  // size = 'icon',
  ...props
}: PaginationLinkProps) {
  return (
    <a
      aria-current={isActive ? 'page' : undefined}
      data-slot="pagination-link"
      data-active={isActive}
      className={cn(
        buttonVariants({
          variant: 'outline',
          // size,
        }),
        `h-10 w-9.5 text-black shadow-none ${isActive ? '' : 'bg-white border-0'}`,
        className
      )}
      {...props}
    />
  );
}

// function PaginationPrevious({
//   className,
//   ...props
// }: React.ComponentProps<typeof PaginationLink>) {
//   return (
//     <PaginationLink
//       aria-label="Go to previous page"
//       size="default"
//       className={cn(
//         'gap-1 px-2.5 sm:pl-2.5 border border-border rounded-lg text-black h-8.5 w-9',
//         className
//       )}
//       {...props}
//     >
//       <ChevronLeftIcon /> Previous
//     </PaginationLink>
//   );
// }

function PaginationPrevious({
  className,
  ...props
}: React.ComponentProps<typeof Button> & {
  onClick: () => void;
}) {
  const { onClick } = props;
  const handleClick = () => {
    onClick();
  };
  return (
    <Button
      variant="outline"
      size="default"
      aria-label="Go to next page"
      className={cn(
        'gap-1 px-2.5 sm:pr-2.5 border-0 rounded-lg text-black h-8.5 shadow-none',
        className
      )}
      onClick={handleClick}
    >
      <ChevronLeftIcon /> Previous
    </Button>
  );
}

function PaginationNext({
  className,
  ...props
}: React.ComponentProps<typeof Button> & {
  onClick: () => void;
}) {
  const { onClick } = props;
  const handleClick = () => {
    onClick();
  };
  return (
    <Button
      variant="outline"
      size="default"
      aria-label="Go to next page"
      className={cn(
        'gap-1 px-2.5 sm:pr-2.5 border-0 rounded-lg text-black h-8.5 shadow-none',
        className
      )}
      onClick={handleClick}
    >
      Next <ChevronRightIcon />
    </Button>
  );
}

function PaginationEllipsis({
  className,
  ...props
}: React.ComponentProps<'span'>) {
  return (
    <span
      aria-hidden
      data-slot="pagination-ellipsis"
      className={cn('flex size-9 items-center justify-center', className)}
      {...props}
    >
      <MoreHorizontalIcon className="size-4" />
      <span className="sr-only">More pages</span>
    </span>
  );
}

export {
  Pagination,
  PaginationContent,
  PaginationLink,
  PaginationItem,
  PaginationPrevious,
  PaginationNext,
  PaginationEllipsis,
};
