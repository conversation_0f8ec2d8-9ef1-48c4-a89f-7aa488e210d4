'use client';

import ScrollContainer from 'react-indiana-drag-scroll';
import React from 'react';
import 'react-indiana-drag-scroll/dist/style.css';

interface DragScrollContainerProps {
  className?: string;
  children: React.ReactNode;
  hideScrollbar?: boolean;
  direction?: 'x' | 'y' | 'both';
}

export const DragScrollContainer: React.FC<DragScrollContainerProps> = ({
  className = '',
  children,
  hideScrollbar = true,
  direction = 'x',
}) => {
  const scrollClass =
    direction === 'x'
      ? 'overflow-x-auto overflow-y-hidden'
      : direction === 'y'
        ? 'overflow-y-auto overflow-x-hidden'
        : 'overflow-auto';

  return (
    <ScrollContainer
      className={`cursor-grab active:cursor-grabbing select-none ${scrollClass} ${
        hideScrollbar ? 'scroll-hidden' : ''
      } ${className}`}
      vertical={direction !== 'x'}
      horizontal={direction !== 'y'}
    >
      {children}
    </ScrollContainer>
  );
};
