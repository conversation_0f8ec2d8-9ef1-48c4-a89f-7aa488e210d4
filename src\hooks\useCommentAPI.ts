import { commentService } from '@/lib/services';

export const useCommentAPI = () => {
  const getReferenceItemComments = async (
    referenceListId?: string,
    referenceItemId?: string
  ) => {
    if (!referenceListId || !referenceItemId) {
      return [];
    }
    return commentService.getReferenceItemComments(
      referenceListId,
      referenceItemId
    );
  };

  const createReferenceItemComment = async (
    referenceListId: string,
    referenceItemId: string,
    content: string
  ) => {
    if (!referenceListId || !referenceItemId || !content.trim()) {
      throw new Error('Reference list ID, item ID, and content are required');
    }
    return commentService.createReferenceItemComment(
      referenceListId,
      referenceItemId,
      content.trim()
    );
  };

  const deleteReferenceItemComment = async (
    referenceListId: string,
    referenceItemId: string,
    commentId: string
  ) => {
    if (!referenceListId || !referenceItemId || !commentId) {
      throw new Error(
        'Reference list ID, item ID, and comment ID are required'
      );
    }
    return commentService.deleteReferenceItemComment(
      referenceListId,
      referenceItemId,
      commentId
    );
  };

  const updateReferenceItemComment = async (
    referenceListId: string,
    referenceItemId: string,
    commentId: string,
    content: string
  ) => {
    if (!referenceListId || !referenceItemId || !commentId || !content.trim()) {
      throw new Error(
        'Reference list ID, item ID, comment ID, and content are required'
      );
    }
    return commentService.updateReferenceItemComment(
      referenceListId,
      referenceItemId,
      commentId,
      content.trim()
    );
  };

  return {
    getReferenceItemComments,
    createReferenceItemComment,
    deleteReferenceItemComment,
    updateReferenceItemComment,
  };
};
