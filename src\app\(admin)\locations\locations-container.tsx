'use client';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { SearchIcon } from '@/lib/icons';
import { Plus } from 'lucide-react';
import { useMemo, useState, useEffect, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Routes } from '@/lib/routes';
import LocationsTable from './locations-table';
import DataTablePagination from '@/components/shared/DataTablePagination';
import {
  useLocations,
  useLocationRequests,
  useTagOptions,
  useLocationCities,
} from '@/hooks/api/useLocations';
import { LocationViewToggle } from './LocationViewToggle';
import { PaginationState } from '@tanstack/react-table';
import { useDebounce } from '@/hooks/useDebounce';

const LIMIT = 6;

export default function LocationsContainer() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [view, setView] = useState<'locations' | 'requests'>(() => {
    const viewParam = searchParams.get('view');
    return viewParam === 'requests' ? 'requests' : 'locations';
  });
  const [search, setSearch] = useState<string>('');
  const [tags, setTags] = useState<string>('all');
  const [municipality, setMunicipality] = useState<string>('all');
  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: LIMIT,
  });

  // Debounce search
  const debouncedSearch = useDebounce(search, 300);

  // Fetch tag options
  const { data: tagOptions = [] } = useTagOptions();

  // Queries
  const isLocationsView = view === 'locations';
  const {
    data: locationsResponse,
    isLoading: loadingLocations,
    isFetching: refreshingLocations,
  } = useLocations(
    {
      page: pagination.pageIndex + 1,
      search: debouncedSearch || undefined,
      tagIds: tags !== 'all' ? tags : undefined,
      where: municipality !== 'all' ? { city: municipality } : undefined,
      limit: LIMIT,
    },
    { enabled: isLocationsView }
  );
  const { data: cities = [] } = useLocationCities();

  const {
    data: requestsResponse,
    isLoading: loadingRequests,
    isFetching: refreshingRequests,
  } = useLocationRequests(
    {
      page: pagination.pageIndex + 1,
      search: debouncedSearch || undefined,
      tagIds: tags !== 'all' ? tags : undefined,
      where: municipality !== 'all' ? { city: municipality } : undefined,
      limit: LIMIT,
    },
    { enabled: !isLocationsView }
  );

  const locationsList = useMemo(
    () => locationsResponse?.data ?? [],
    [locationsResponse]
  );
  const totalItems = locationsResponse?.meta.totalItems ?? 0;
  const requestsList = useMemo(
    () => requestsResponse?.data ?? [],
    [requestsResponse]
  );
  const requestsTotalItems = requestsResponse?.meta.totalItems ?? 0;
  const requestsCount = requestsResponse?.meta.totalItems ?? 0;

  const loading = isLocationsView ? loadingLocations : loadingRequests;
  const refreshing = isLocationsView ? refreshingLocations : refreshingRequests;

  // Sync view to URL (?view=locations|requests)
  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    if (view === 'locations') {
      params.delete('view');
    } else {
      params.set('view', view);
    }
    const query = params.toString();
    router.replace(query ? `?${query}` : '?', { scroll: false });
  }, [view, router]);

  // Reset pagination when view, search, tags, or municipality changes
  useEffect(() => {
    setPagination(prev => ({ ...prev, pageIndex: 0 }));
  }, [view, debouncedSearch, tags, municipality]);

  const pageCount = useMemo(
    () =>
      Math.max(
        Math.ceil((isLocationsView ? totalItems : requestsTotalItems) / LIMIT),
        1
      ),
    [isLocationsView, totalItems, requestsTotalItems]
  );

  const handleActionSuccess = useCallback(() => {
    setPagination(prev => ({ ...prev, pageIndex: 0 }));
  }, []);

  const locationsMain = useMemo(
    () => (
      <LocationsTable
        data={isLocationsView ? locationsList : requestsList}
        loading={loading}
        refreshing={refreshing}
        isLocationsView={isLocationsView}
        pagination={pagination}
        pageCount={pageCount}
        onPaginationChange={setPagination}
        onActionSuccess={handleActionSuccess}
      />
    ),
    [
      isLocationsView,
      locationsList,
      requestsList,
      loading,
      refreshing,
      pagination,
      pageCount,
      handleActionSuccess,
    ]
  );

  return (
    <div className="flex justify-center mt-4">
      <div className="space-y-6 py-4 px-4 sm:px-8 xl:max-w-[70.5rem] 2xl:mx-auto 2xl:px-0">
        <h1 className="text-2xl font-bold">Locations</h1>
        <div className="text-base">
          Add, update, and organize all locations.
        </div>
        <LocationViewToggle
          view={view}
          requestsCount={requestsCount}
          onViewChange={setView}
        />
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:flex-1">
            <div className="relative w-full md:w-[11.25rem] xl:w-[16rem]">
              <SearchIcon className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search by name..."
                value={search}
                onChange={e => setSearch(e.target.value)}
                className="pl-9"
                debounceMs={300}
              />
            </div>
            <div className="flex flex-wrap items-center gap-4">
              <Select value={tags} onValueChange={v => setTags(v)}>
                <SelectTrigger aria-label="Tags" className="max-w-40">
                  <SelectValue
                    placeholder="All Tags"
                    className="truncate text-ellipsis"
                  />
                </SelectTrigger>
                <SelectContent>
                  {[{ value: 'all', label: 'All Tags' }, ...tagOptions].map(
                    tag => (
                      <SelectItem key={tag.value} value={tag.value}>
                        {tag.label}
                      </SelectItem>
                    )
                  )}
                </SelectContent>
              </Select>

              <Select
                value={municipality}
                onValueChange={v => setMunicipality(v)}
              >
                <SelectTrigger aria-label="Municipality">
                  <SelectValue placeholder="Municipality" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Municipality</SelectItem>
                  {Array.isArray(cities) &&
                    cities.length > 0 &&
                    cities.map(city => (
                      <SelectItem key={city} value={city}>
                        {city}
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          <Button
            variant="default"
            className="w-full sm:w-[12.8125rem] h-[2.625rem]"
            onClick={() => router.push(Routes.ADMIN_CREATE_LOCATION)}
          >
            <Plus /> Submit Location
          </Button>
        </div>
        <div className="overflow-auto">{locationsMain}</div>
        <DataTablePagination
          page={pagination.pageIndex + 1}
          totalPages={pageCount}
          onPageChange={nextPage =>
            setPagination(prev => ({ ...prev, pageIndex: nextPage - 1 }))
          }
        />
      </div>
    </div>
  );
}
