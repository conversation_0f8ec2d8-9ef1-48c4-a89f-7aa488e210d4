'use client';
import { Photo } from '@/types/location';
import AllPhotoGrid from '@/components/AllPhotoGrid';
import { ArrowLeft } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { use, useCallback, useEffect, useState } from 'react';
import { Heading } from '@/components/ui/typography';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import Loading from './loading';
import ImageFullScreen from '@/components/ImageFullScreen';
import { toastError } from '@/lib/toast';
import { useLocationService } from '@/hooks/use-services';

export default function AllPhotos({
  params,
}: {
  params: Promise<{ id: string; locationId: string }>;
}) {
  const { id, locationId } = use(params);
  const router = useRouter();
  const locationService = useLocationService();
  const [images, setImages] = useState<Photo[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [isDetailScreenOpen, setIsDetailScreenOpen] = useState<boolean>(false);
  const [startIndex, setStartIndex] = useState<number>(0);

  const fetchImages = useCallback(async () => {
    try {
      setLoading(true);
      const response = await locationService.getLocationById(locationId);
      // const result: Photo[] = response.images.map(img => ({id: img.id}));
      setImages(response.images);
    } catch {
      toastError('Failed to get photos');
    } finally {
      setLoading(false);
    }
  }, [locationId, locationService]);

  useEffect(() => {
    if (!locationId) return;
    fetchImages();
  }, [fetchImages, locationId]);

  return (
    <>
      {loading && <Loading />}
      {!loading && (
        <div className="w-full flex justify-center">
          <div className="space-y-11 w-full xl:w-[70.5rem] p-11 xl:px-0">
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-4">
              <div
                onClick={() => {
                  router.push(
                    `/scout/reference/${id}/view-details/${locationId}`
                  );
                }}
                className="cursor-pointer inline-flex items-center gap-3 text-sm text-gray-600 hover:text-gray-900 transition-colors"
              >
                <ArrowLeft className="h-5 w-5" />
                <div className="flex flex-col justify-start">
                  <Heading level={3}>All Photos</Heading>
                  <p>Downtown Creative Loft</p>
                </div>
              </div>
              <Button
                onClick={() => {
                  router.push(
                    `/scout/reference/${id}/select-images/${locationId}`
                  );
                }}
                className="w-full sm:w-[9.6875rem] h-12"
              >
                <Plus />
                Add to List
              </Button>
            </div>
            <AllPhotoGrid
              images={images}
              onClickImage={image => {
                setIsDetailScreenOpen(true);
                const index = images.findIndex(img => image.id === img.id);
                setStartIndex(index);
              }}
            />
          </div>
        </div>
      )}
      <ImageFullScreen
        open={isDetailScreenOpen}
        images={images}
        onOpenChange={setIsDetailScreenOpen}
        startIndex={startIndex}
      />
    </>
  );
}
