import { TagOption } from '@/types/tag';
import { httpClient } from '../http-client';
import { Location } from '@/types';
import { Address } from '@/app/scout/reference/[id]/search-locations/search-location-context';

export interface PhotosData {
  file: File;
  tags: TagOption[];
}

export interface CreateLocationData {
  address: string;
  placeId?: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
  description: string;
  tagIds: string[];
  size: string;
  images: {
    url: string;
    order: number;
    key: string;
    tagIds: string[];
  }[];
  latitude?: number;
  longitude?: number;
}

const buildQueryParams = (params: {
  page?: number;
  search?: string;
  status?: string;
  tagIds?: string;
  where?: Address;
  limit?: number;
  size?: string;
}) => {
  const { page, search, status, tagIds, limit, where, size } = params;
  const queryParams: string[] = [];
  if (page) {
    queryParams.push(`page=${page}`);
  }
  if (limit) {
    queryParams.push(`limit=${limit}`);
  }
  if (search && search !== '') {
    queryParams.push(`search=${search}`);
  }
  if (status && status !== 'all') {
    queryParams.push(`status=${status}`);
  }
  if (tagIds && tagIds !== 'all') {
    queryParams.push(`tagIds=${tagIds}`);
  }
  if (where && Object.keys(where).length) {
    const result = [];
    if (where.city) result.push(where.city);
    if (where.state) result.push(where.state);
    if (where.country) result.push(where.country);
    if (where.postalCode) result.push(where.postalCode);
    queryParams.push(`where=${result.join(',')}`);
  }
  if (size) {
    queryParams.push(`size=${size}`);
  }
  return queryParams.join('&');
};

export interface UpdateLocationData extends Partial<CreateLocationData> {
  status?: 'active' | 'inactive' | 'pending';
  placeId?: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
}

/**
 * Location Service - Handles all location-related API operations
 */
export class LocationService {
  /**
   * Get all locations
   */
  async getLocations(
    page: number,
    search?: string,
    status?: string,
    tagIds?: string,
    where?: Address,
    limit?: number,
    size?: string
  ): Promise<{
    data: Location[];
    meta: {
      currentPage: number;
      totalPages: number;
      itemsPerPage: number;
      totalItems: number;
      itemCount: number;
    };
  }> {
    const queryParams = buildQueryParams({
      page,
      search,
      status,
      tagIds,
      where,
      limit,
      size,
    });

    return httpClient.get<{
      data: Location[];
      meta: {
        currentPage: number;
        totalPages: number;
        itemsPerPage: number;
        totalItems: number;
        itemCount: number;
      };
    }>(`/locations?${queryParams}`);
  }

  /**
   * Get all location requests
   */
  async getLocationsRequests(
    page: number,
    search?: string,
    tagIds?: string,
    where?: Address,
    limit?: number,
    size?: string
  ): Promise<{
    data: Location[];
    meta: {
      currentPage: number;
      totalPages: number;
      itemsPerPage: number;
      totalItems: number;
      itemCount: number;
    };
  }> {
    const queryParams = buildQueryParams({
      page,
      search,
      tagIds,
      where,
      limit,
      size,
    });

    return httpClient.get<{
      data: Location[];
      meta: {
        currentPage: number;
        totalPages: number;
        itemsPerPage: number;
        totalItems: number;
        itemCount: number;
      };
    }>(`/locations/requests?${queryParams}`);
  }

  /**
   * Update location status (accept / reject request)
   */
  async updateLocationStatus(
    id: string,
    status: 'active' | 'inactive'
  ): Promise<Location> {
    return httpClient.patch<Location>(`/locations/${id}`, { status });
  }
  /**
   * Get location by ID
   */
  async getLocationById(id: string): Promise<Location> {
    return httpClient.get<Location>(`/locations/${id}`);
  }

  /**
   * Create new location
   */
  async createLocation(locationData: CreateLocationData): Promise<Location> {
    return httpClient.post<Location>('/locations', locationData);
  }

  /**
   * Update location by ID
   */
  async updateLocation(
    id: string,
    locationData: UpdateLocationData
  ): Promise<Location> {
    return httpClient.patch<Location>(`/locations/${id}`, locationData);
  }

  /**
   * Delete location by ID
   */
  async deleteLocation(id: string): Promise<void> {
    return httpClient.delete<void>(`/locations/${id}`);
  }

  /**
   * Search locations by query
   */
  async searchLocations(query: string): Promise<Location[]> {
    return httpClient.get<Location[]>(
      `/locations/search?q=${encodeURIComponent(query)}`
    );
  }

  /**
   * Get locations by status
   */
  async getLocationsByStatus(
    status: 'active' | 'inactive' | 'pending'
  ): Promise<Location[]> {
    return httpClient.get<Location[]>(`/locations?status=${status}`);
  }

  /**
   * Upload location images
   */
  async uploadLocationImages(
    locationId: string,
    files: File[]
  ): Promise<{ urls: string[] }> {
    const formData = new FormData();
    files.forEach((file, index) => {
      formData.append(`images[${index}]`, file);
    });
    formData.append('locationId', locationId);

    return httpClient.uploadFile<{ urls: string[] }>(
      '/locations/images',
      files[0],
      {
        locationId,
        imageCount: files.length.toString(),
      }
    );
  }

  /**
   * Get all location cities
   */
  async getLocationCities(): Promise<{
    data: string[];
  }> {
    return httpClient.get<{
      data: string[];
    }>(`/locations/cities`);
  }
}

// Export singleton instance
export const locationService = new LocationService();
