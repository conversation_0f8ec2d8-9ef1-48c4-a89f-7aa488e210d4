'use client';

import type { Location } from '@/types';

import { LocationPhotosSection } from '@/components/features/locations/LocationPhotosSection';
import { LocationMainInfo } from '@/components/features/locations/LocationMainInfo';
import { LocationDetailCard } from './LocationDetailCard';
import { LocationDetailLoading } from './LocationDetailLoading';
import { AddLocationComment } from './AddLocationComment';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import { Routes } from '@/lib/routes';

interface LocationDetailProps {
  location?: Location;
  loading?: boolean;
  onSeeAllPhotos?: () => void;
  onSubmit?: () => void;
}

export default function LocationDetail({
  location,
  loading,
  onSeeAllPhotos,
  onSubmit,
}: LocationDetailProps) {
  if (loading) {
    return <LocationDetailLoading />;
  }

  if (!location) {
    return null;
  }

  return (
    <div className="w-full flex justify-center">
      <div className="space-y-6 xl:w-[70.5rem] p-6 xl:px-0">
        {/* Breadcrumbs */}
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href={Routes.ADMIN_LOCATIONS}>
                Locations
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>Scoutr location</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>

        <LocationPhotosSection
          location={location}
          onSeeAllPhotos={onSeeAllPhotos}
        />

        <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6 items-start">
          <LocationMainInfo location={location} />
          <LocationDetailCard location={location} onSubmit={onSubmit} />
        </div>

        {/* Comments section */}
        <div className="mt-6">
          <AddLocationComment
            comments={[]}
            loading={false}
            onComment={content => {
              // TODO: Implement comment API for locations
              console.log('Add comment:', content);
            }}
            onDelete={commentId => {
              // TODO: Implement delete comment API
              console.log('Delete comment:', commentId);
            }}
            onEdit={(commentId, content) => {
              // TODO: Implement edit comment API
              console.log('Edit comment:', commentId, content);
            }}
            onClose={() => {
              // TODO: Implement close comment API
              console.log('Close comment');
            }}
          />
        </div>
      </div>
    </div>
  );
}
