# Contributing Guide

This guide outlines the coding standards and conventions for the Scoutr Web App.

## Table of Contents

1. [Naming Conventions](#naming-conventions)
2. [Folder Structure](#folder-structure)
3. [Component Standards](#component-standards)
4. [Form Patterns](#form-patterns)
5. [Data Fetching](#data-fetching)
6. [TypeScript Guidelines](#typescript-guidelines)

## Naming Conventions

### Files

- **Components**: PascalCase (`AddTagModal.tsx`)
- **Hooks**: camelCase (`useTags.ts`)
- **Services**: kebab-case (`tag-service.ts`)
- **Types**: kebab-case (`tag.ts`)
- **Utils**: camelCase (`formatDate.ts`)
- **Pages**: lowercase (`page.tsx`, `layout.tsx`)

### Code

- **Components**: PascalCase (`export function TagsContainer()`)
- **Hooks**: camelCase with `use` prefix (`export function useTags()`)
- **Variables/Functions**: camelCase (`const handleSubmit = () => {}`)
- **Constants**: UPPER_SNAKE_CASE (`const LIMIT = 6`)
- **Boolean variables**: Prefix with `is`, `has`, `should` (`isLoading`, `hasError`)

## Folder Structure

```
src/
├── app/                    # Next.js app router pages
│   ├── admin/             # Admin pages
│   └── (auth)/            # Auth pages
├── components/
│   ├── features/           # Feature-specific components
│   │   ├── tags/
│   │   ├── locations/
│   │   └── users/
│   ├── shared/            # Shared/reusable components
│   └── ui/                 # Base UI components (Shadcn)
├── hooks/
│   ├── api/                # React Query hooks
│   └── use*.ts             # Custom hooks
├── lib/
│   ├── validations/        # Zod schemas
│   ├── types/              # TypeScript types
│   └── query-keys.ts       # React Query keys
└── types/                  # Type definitions
```

## Component Standards

### Component Structure Order

1. **Imports** (grouped):

   ```tsx
   // React
   import { useState } from 'react';
   // Next.js
   import { useRouter } from 'next/navigation';
   // UI Components
   import { Button } from '@/components/ui/button';
   // Utils/Hooks
   import { useTags } from '@/hooks/api/useTags';
   // Types
   import { Tag } from '@/types/tag';
   ```

2. **Types/Interfaces**
3. **Constants**
4. **Component** (default export)
5. **Sub-components** (if any)

### Component Template

See `src/.templates/component.tsx.template` for the standard component structure.

## Form Patterns

### All Forms Use React Hook Form + Zod

```tsx
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { formSchema } from '@/lib/validations';

const form = useForm({
  resolver: zodResolver(formSchema),
  defaultValues: {
    /* ... */
  },
});
```

### Validation Schemas

- All schemas in `src/lib/validations/`
- Export from `src/lib/validations/index.ts`
- Use Zod for type-safe validation

### Form Modal Template

See `src/.templates/form-modal.tsx.template` for the standard form modal structure.

## Data Fetching

### Use React Query for All Server State

```tsx
import { useTags } from '@/hooks/api/useTags';

const { data, isLoading, error } = useTags({ page: 1, limit: 10 });
```

### Query Keys

- Use `queryKeys` from `@/lib/query-keys`
- Follow the factory pattern for consistency

### Mutations

```tsx
import { useCreateTag } from '@/hooks/api/useTags';

const createTag = useCreateTag();
await createTag.mutateAsync(tagData);
```

## TypeScript Guidelines

### Type Safety

- ✅ Use interfaces for component props
- ✅ Use `type` for unions and intersections
- ✅ Avoid `any` - use `unknown` if needed
- ✅ Use proper generic types

### Common Types

- Import from `@/lib/types` for common types
- Use `ApiResponse<T>` for API responses
- Use `PaginationParams` for pagination

### Strict Mode

- TypeScript strict mode is enabled
- All code must pass type checking
- Use proper type guards when needed

## Loading States

### Standardized Components

- `LoadingSpinner` - Page-level loading
- `TableSkeleton` / `TableRowSkeleton` - Table loading
- `InlineSpinner` - Button loading states
- `PageLoading` - Full page loading

### Usage

```tsx
import { LoadingSpinner, InlineSpinner } from '@/components/shared';

// Page loading
<LoadingSpinner size="lg" text="Loading..." />

// Button loading
<Button disabled={loading}>
  Save {loading && <InlineSpinner className="ml-2" />}
</Button>
```

## Code Quality

### Linting

- Run `yarn lint` before committing
- Fix all linting errors
- Follow ESLint and Prettier rules

### Testing

- Write tests for complex logic
- Test user interactions
- Test error handling

## Git Workflow

1. Create feature branch from `main`
2. Make changes following conventions
3. Run linting and type checking
4. Commit with descriptive messages
5. Create pull request

## Questions?

Refer to:

- `src/.templates/` for component templates
- `CODEBASE_AUDIT.md` for detailed architecture decisions
- Existing code for examples
