'use client';

import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  SortingState,
  PaginationState,
  useReactTable,
} from '@tanstack/react-table';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { getRoleDisplayName, getUserInitials } from '@/lib/utils';
import { User } from '@/types/user';
import { EditIcon, TrashIcon, UsersIcon } from '@/lib/icons';
import { Loader2Icon } from 'lucide-react';
import { IconButton } from '@/components/shared/IconButton';
import { Card, CardContent, CardTitle } from '@/components/ui/card';
import { useMemo, useState } from 'react';
import { format } from 'date-fns';

interface UsersTableProps {
  data: User[];
  loading: boolean;
  refreshing: boolean;
  isActiveView: boolean;
  columns: ColumnDef<User>[];
  pagination: PaginationState;
  pageCount: number;
  onPaginationChange: (pagination: PaginationState) => void;
}

const getStatusBadgeVariant = (status?: string) => {
  switch (status?.toLowerCase()) {
    case 'active':
      return 'success';
    case 'pending':
      return 'warning';
    case 'disabled':
      return 'danger';
    default:
      return 'secondary';
  }
};

export function UsersTable({
  data,
  loading,
  isActiveView,
  columns,
  pagination,
  pageCount,
  onPaginationChange,
}: UsersTableProps) {
  const [sorting, setSorting] = useState<SortingState>([]);

  const table = useReactTable({
    data,
    columns,
    pageCount,
    state: {
      sorting,
      pagination,
    },
    onSortingChange: setSorting,
    onPaginationChange: updaterOrValue => {
      // onPaginationChange may expect either a PaginationState or an Updater function.
      // This ensures compatibility with OnChangeFn<PaginationState>.
      if (typeof updaterOrValue === 'function') {
        onPaginationChange(updaterOrValue(pagination));
      } else {
        onPaginationChange(updaterOrValue);
      }
    },
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    manualPagination: true,
    manualSorting: true,
  });

  if (loading) {
    return null; // Loading handled by parent
  }

  return (
    <Card className="py-0 rounded-md shadow-sm gap-0">
      <CardTitle className="h-[3.875rem] flex items-center px-6 bg-[#FAFAFA] rounded-md">
        {isActiveView ? 'Active Users' : 'Requests'}
      </CardTitle>
      <CardContent className="px-0">
        <div className="border-t">
          <Table>
            <TableHeader>
              {table.getHeaderGroups().map(headerGroup => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map(header => (
                    <TableHead className="px-6 bg-[#FAFAFA]" key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  ))}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map(row => (
                  <TableRow key={row.id} className="group/row">
                    {row.getVisibleCells().map(cell => (
                      <TableCell className="px-6" key={cell.id}>
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext()
                        )}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell
                    colSpan={columns.length}
                    className="h-[32.375rem] text-center"
                  >
                    <div className="h-full flex flex-col items-center justify-center gap-4">
                      <div className="w-29 h-29 bg-[#E5E7EB] flex items-center justify-center rounded-full">
                        <UsersIcon color="#D1D5DB" className="w-15 h-13" />
                      </div>
                      <span>No users yet.</span>
                    </div>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
}

export function useUsersColumns({
  isActiveView,
  refreshing,
  handleEdit,
  handleDelete,
  rejectUser,
  acceptUser,
}: {
  isActiveView: boolean;
  refreshing: boolean;
  handleEdit: (userId: string) => () => void;
  handleDelete: (userId: string) => () => void;
  rejectUser: (userId: string) => void;
  acceptUser: (userId: string) => void;
}): ColumnDef<User>[] {
  return useMemo(() => {
    const usersColumns: ColumnDef<User>[] = [
      {
        accessorKey: 'name',
        header: ({ column }) => (
          <Button
            className="p-0"
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            User
          </Button>
        ),
        cell: ({ row }) => (
          <div className="flex items-center space-x-2">
            <Avatar className="h-8 w-8">
              <AvatarImage src={row.original.avatar} alt="avatar" />
              <AvatarFallback className="text-xs">
                {getUserInitials(row.original.firstName)}
              </AvatarFallback>
            </Avatar>
            <span className="text-sm font-medium hidden sm:block">
              {row.original.firstName} {row.original.lastName}
            </span>
          </div>
        ),
      },
      {
        accessorKey: 'email',
        header: ({ column }) => (
          <Button
            className="p-0"
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            Email
          </Button>
        ),
        cell: ({ row }) => <span>{row.getValue('email')}</span>,
      },
      {
        accessorKey: 'role',
        header: ({ column }) => (
          <div className="w-32 whitespace-nowrap">
            <Button
              className="p-0"
              variant="ghost"
              onClick={() =>
                column.toggleSorting(column.getIsSorted() === 'asc')
              }
            >
              Role
            </Button>
          </div>
        ),
        cell: ({ row }) => (
          <Badge variant="secondary" className="capitalize">
            {getRoleDisplayName(row.original.role)}
          </Badge>
        ),
      },
    ];

    const requestsColumns: ColumnDef<User>[] = [
      {
        accessorKey: 'name',
        header: ({ column }) => (
          <Button
            className="p-0"
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            User
          </Button>
        ),
        cell: ({ row }) => (
          <div className="flex items-center space-x-2">
            <Avatar className="h-8 w-8">
              <AvatarImage src={row.original.avatar} alt="avatar" />
              <AvatarFallback className="text-xs">
                {getUserInitials(row.original.firstName)}
              </AvatarFallback>
            </Avatar>
            <span className="text-sm font-medium hidden sm:block">
              {row.original.firstName} {row.original.lastName}
            </span>
          </div>
        ),
      },
      {
        accessorKey: 'role',
        header: ({ column }) => (
          <div className="w-32 whitespace-nowrap">
            <Button
              className="p-0"
              variant="ghost"
              onClick={() =>
                column.toggleSorting(column.getIsSorted() === 'asc')
              }
            >
              Role
            </Button>
          </div>
        ),
        cell: ({ row }) => (
          <Badge variant="secondary" className="capitalize">
            {getRoleDisplayName(row.original.role)}
          </Badge>
        ),
      },
      {
        accessorKey: 'requestType',
        header: ({ column }) => (
          <Button
            className="p-0"
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            Request type
          </Button>
        ),
        cell: ({ row }) => <span>{row.getValue('requestType') || '-'}</span>,
      },
      {
        accessorKey: 'project',
        header: ({ column }) => (
          <Button
            className="p-0"
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            Project
          </Button>
        ),
        cell: ({ row }) => <span>{row.getValue('project') || '-'}</span>,
      },
      {
        accessorKey: 'dateRequested',
        header: ({ column }) => (
          <Button
            className="p-0"
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            Date requested
          </Button>
        ),
        cell: ({ row }) => {
          const dateValue = row.getValue('dateRequested') as
            | string
            | Date
            | undefined;
          if (!dateValue) {
            return <span>-</span>;
          }
          const date =
            typeof dateValue === 'string' ? new Date(dateValue) : dateValue;
          if (isNaN(date.getTime())) {
            return <span>-</span>;
          }
          return <span>{format(date, 'MMM d, yyyy HH:mm')}</span>;
        },
      },
    ];

    const statusColumn: ColumnDef<User> = {
      accessorKey: 'status',
      header: ({ column }) => (
        <div className="w-32 whitespace-nowrap">
          <Button
            className="p-0"
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            Status
          </Button>
        </div>
      ),
      cell: ({ row }) => (
        <Badge
          variant={getStatusBadgeVariant(row.original.status)}
          className="capitalize"
        >
          {row.original.status}
        </Badge>
      ),
    };

    const actionsColumn: ColumnDef<User> = {
      id: 'actions',
      header: () => {
        return (
          <div className="flex justify-end items-center">
            {refreshing && (
              <Loader2Icon className="h-6 w-6 animate-spin text-primary" />
            )}
          </div>
        );
      },
      cell: ({ row }) => {
        const user = row.original;
        if (isActiveView) {
          return (
            <div className="flex justify-end gap-1">
              <IconButton
                color="primary"
                size="small"
                onClick={handleEdit(user.id)}
              >
                <EditIcon />
              </IconButton>
              <IconButton
                color="primary"
                size="small"
                onClick={handleDelete(user.id)}
              >
                <TrashIcon />
              </IconButton>
            </div>
          );
        }
        return (
          <div className="flex justify-end gap-3">
            <Button
              onClick={() => rejectUser(user.id)}
              size="sm"
              variant="outline"
            >
              Reject
            </Button>
            <Button onClick={() => acceptUser(user.id)} size="sm">
              Accept
            </Button>
          </div>
        );
      },
    };

    if (isActiveView) {
      return [...usersColumns, statusColumn, actionsColumn];
    } else {
      return [...requestsColumns, actionsColumn];
    }
  }, [
    isActiveView,
    refreshing,
    handleEdit,
    handleDelete,
    rejectUser,
    acceptUser,
  ]);
}
