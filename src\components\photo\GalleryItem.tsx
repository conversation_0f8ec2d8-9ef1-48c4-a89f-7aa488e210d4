import { forwardRef, useCallback, useEffect, useRef } from 'react';
import { FileError, FileErrorCode, FileUpload } from '@/types';
import Image from 'next/image';
import React from 'react';
import { DropdownMenu } from '../ui/dropdown-menu';
import { DropdownMenuTrigger } from '../ui/dropdown-menu';
import { MoreHorizIcon } from '@/lib/icons';
import { DropdownMenuContent } from '../ui/dropdown-menu';
import { DropdownMenuItem } from '../ui/dropdown-menu';
import { Checkbox } from '../ui/checkbox';
import { CheckedState } from '@radix-ui/react-checkbox';
import { Badge } from '../ui/badge';
import { useFileUpload } from '@/hooks/useFileUpload';
import { toastError } from '@/lib/toast';
import { Tooltip, TooltipContent, TooltipTrigger } from '../ui/tooltip';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

interface GalleryItemProps {
  index: number;
  photo: FileUpload;
  uploadSlotVersion?: number;
  activeUploadCount?: number;
  onCanUpload?: (uuid: string) => boolean;
  onEdit?: () => void;
  onRemove?: () => void;
  onCheckedChange?: (checked: CheckedState) => void;
  onUploadComplete?: (file: FileUpload) => void;
  onUploadError?: (error: FileError) => void;
  onUploading?: (file: FileUpload) => void;
}

export const GalleryItem = forwardRef<HTMLDivElement, GalleryItemProps>(
  (
    {
      index,
      photo,
      uploadSlotVersion = 0,
      activeUploadCount = 0,
      onCanUpload,
      onEdit,
      onRemove,
      onCheckedChange,
      onUploadComplete,
      onUploadError,
      onUploading,
    }: GalleryItemProps,
    ref
  ) => {
    const fileKey = useRef<string | null>(null);
    const uploadAttemptedRef = useRef(false);

    // Make the item sortable
    const {
      attributes,
      listeners,
      setNodeRef,
      transform,
      transition,
      isDragging,
    } = useSortable({
      id: photo.uuid,
      disabled: photo.isUploading || !photo.uuid,
    });

    // Combine refs - use sortable ref, but also support external ref
    const combinedRef = useCallback(
      (node: HTMLDivElement | null) => {
        setNodeRef(node);
        if (typeof ref === 'function') {
          ref(node);
        } else if (ref) {
          ref.current = node;
        }
      },
      [setNodeRef, ref]
    );

    const style = {
      transform: CSS.Transform.toString(transform),
      transition,
      opacity: isDragging ? 0.5 : 1,
    };

    const { uploadSingleFile, uploadProgress, isUploading, cancelUpload } =
      useFileUpload({
        onUploading: (isUploading: boolean) => {
          onUploading?.({ ...photo, isUploading });
        },
        onUploadComplete: (data: unknown) => {
          const result = data as { url: string; key: string };
          const updatedPhoto = {
            ...photo,
            url: result.url,
            key: result.key,
            isUploading: false,
            isUploaded: true,
            isUploadSuccess: true,
            isUploadError: false,
          };
          onUploadComplete?.(updatedPhoto);
        },
        onUploadError: (error: FileError) => {
          toastError(error.message);
          switch (error.code) {
            case FileErrorCode.FILE_SIZE_ERROR:
            case FileErrorCode.FILE_TYPE_ERROR:
              onRemove?.();
              break;
            default:
              // Update the upload state to reflect the error
              onUploading?.({
                ...photo,
                isUploading: false,
                isUploaded: false,
                isUploadSuccess: false,
                isUploadError: true,
              });
              onUploadError?.(error);
              break;
          }
        },
        onValidationError: (error: string) => {
          toastError(error);
          onRemove?.();
        },
      });

    useEffect(() => {
      const shouldTryUpload =
        !photo.url &&
        photo.file &&
        !photo.isUploading &&
        !photo.isUploaded &&
        !photo.isUploadError &&
        fileKey.current !== photo.uuid &&
        !uploadAttemptedRef.current;

      if (shouldTryUpload) {
        // Atomically check and reserve an upload slot
        const canUpload = onCanUpload ? onCanUpload(photo.uuid) : true;

        if (canUpload) {
          fileKey.current = photo.uuid;
          uploadAttemptedRef.current = true;
          uploadSingleFile(photo.file as File);
        }
      }

      // Reset attempt flag if upload fails or is removed
      if (photo.isUploadError || (!photo.file && uploadAttemptedRef.current)) {
        uploadAttemptedRef.current = false;
        if (photo.isUploadError || !photo.file) {
          fileKey.current = null;
        }
      }
    }, [
      photo.file,
      photo.url,
      photo.isUploading,
      photo.isUploaded,
      photo.isUploadError,
      photo.uuid,
      uploadSlotVersion,
      activeUploadCount,
      uploadSingleFile,
      onCanUpload,
    ]);

    // Memoize the checkbox change handler
    const handleCheckedChange = useCallback(
      (checked: CheckedState) => {
        onCheckedChange?.(checked);
      },
      [onCheckedChange]
    );

    // Memoize the edit handler
    const handleEdit = useCallback(() => {
      onEdit?.();
    }, [onEdit]);

    // Memoize the remove handler
    const handleRemove = useCallback(() => {
      // If the photo is currently uploading, cancel the upload first
      if (isUploading) {
        cancelUpload();
      }
      onRemove?.();
    }, [isUploading, cancelUpload, onRemove]);

    const tagCount = photo.tags?.length ?? 0;
    const inlineStyles: React.CSSProperties = {
      opacity: 1,
      transformOrigin: '0 0',
      height: index === 0 ? 337 : 169,
      ...(index === 0
        ? { gridRowStart: 'span 2', gridColumnStart: 'span 2' }
        : {}),
    };

    return (
      <div
        ref={combinedRef}
        className="relative w-full h-full group border rounded overflow-hidden cursor-grab active:cursor-grabbing"
        style={{ ...inlineStyles, ...style } as React.CSSProperties}
        {...listeners}
        {...attributes}
      >
        {photo.imageSrc ? (
          <Image
            src={photo.imageSrc}
            alt={photo.file?.name || 'photo'}
            fill
            className="w-full h-full object-cover"
            sizes="(max-width: 768px) 100vw, 50vw"
          />
        ) : (
          <div className="w-full h-full bg-gray-200 flex items-center justify-center">
            <span className="text-gray-500 text-sm">No image</span>
          </div>
        )}
        {isUploading && (
          <>
            <div
              className="absolute bottom-0 right-0 w-full bg-black/80 transition-all duration-300"
              style={{ height: `${100 - uploadProgress}%` }}
            ></div>
            <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 text-white text-sm px-2 rounded-sm bg-black/60">
              {uploadProgress}%
            </div>
          </>
        )}
        {!isUploading &&
          !photo.isUploaded &&
          !photo.isUploadError &&
          !photo.url && (
            <>
              <div
                className="absolute bottom-0 right-0 w-full bg-black/80 transition-all duration-300"
                style={{ height: '100%' }}
              ></div>
              <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 text-white text-sm px-2 rounded-sm bg-black/60">
                Pending
              </div>
            </>
          )}
        <Checkbox
          id="gallery-item-checkbox"
          checked={photo.isChecked ?? false}
          onCheckedChange={handleCheckedChange}
          className="absolute left-4 top-4 z-10"
          onPointerDown={e => e.stopPropagation()}
          onMouseDown={e => e.stopPropagation()}
        />
        <div
          className="absolute top-4 right-4 w-6 h-6 cursor-pointer flex items-center justify-center bg-black/20 hover:bg-black/40 transition-colors rounded-sm z-10"
          onPointerDown={e => e.stopPropagation()}
          onMouseDown={e => e.stopPropagation()}
        >
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <MoreHorizIcon className="cursor-pointer w-4 h-4" />
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={handleEdit}>Edit</DropdownMenuItem>
              <DropdownMenuItem onClick={handleRemove}>Remove</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
        <div className="absolute bottom-4 right-4 left-4 gap-2 flex flex-col">
          {tagCount > 0 &&
            photo.tags?.slice(0, tagCount > 3 ? 2 : tagCount).map(tag => (
              <div className="text-right" key={tag.id}>
                <Badge
                  variant="secondary"
                  className={`${tag.color ? `bg-${tag.color} text-white` : ''}`}
                >
                  {tag.name}
                </Badge>
              </div>
            ))}
          {tagCount > 3 && (
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="text-right">
                  <Badge variant="secondary">
                    +{tagCount - 2} more{tagCount - 2 > 1 ? 's' : ''}
                  </Badge>
                </div>
              </TooltipTrigger>
              <TooltipContent
                className="w-auto flex flex-col gap-2 bg-accent-foreground"
                side="right"
              >
                <div className="flex flex-col gap-2">
                  {photo.tags?.slice(2).map(tag => (
                    <Badge
                      key={tag.id}
                      variant="secondary"
                      className={`capitalize ${tag.color ? `bg-${tag.color} text-white` : ''}`}
                    >
                      {tag.name}
                    </Badge>
                  ))}
                </div>
              </TooltipContent>
            </Tooltip>
          )}
        </div>
      </div>
    );
  }
);

GalleryItem.displayName = 'GalleryItem';
