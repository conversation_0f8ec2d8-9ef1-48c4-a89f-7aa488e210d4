'use client';

import { useSearchParams, useRouter } from 'next/navigation';
import { useState, useEffect } from 'react';
import { toastSuccess } from '@/lib/toast';
import { Heading } from '@/components/ui/typography';
import { Body } from '@/components/ui/typography';
import Image from 'next/image';
import { authService } from '@/lib/services/auth-service';
import { Routes } from '@/lib/routes';
import { handleError } from '@/lib/error-handler';
import Loading from '@/app/loading';

export default function ConfirmAccountPage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [isSuccess, setIsSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const token = searchParams.get('token');

  useEffect(() => {
    const confirmAccount = async () => {
      if (!token) {
        setError('Invalid confirmation link. Please check your email.');
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        await authService.confirmEmail(token);
        setIsLoading(false);
        setIsSuccess(true);
        toastSuccess('Account created.');
        // Redirect to login after a short delay to show the success message
        setTimeout(() => {
          router.push(Routes.SIGN_IN);
        }, 1500);
      } catch (err) {
        console.error('Error confirming account:', err);
        const { message } = handleError(
          err,
          'Failed to confirm account. Please try again.'
        );
        setError(message);
        setIsLoading(false);
      }
    };

    confirmAccount();
  }, [token, router]);

  if (isLoading) {
    return <Loading />;
  }

  return (
    <div className="w-full h-screen overflow-auto flex items-center justify-center">
      <div className="space-y-12 max-w-[28.5rem] w-full h-full flex flex-col items-center justify-center">
        <div className="text-center space-y-2">
          <div className="flex items-center justify-center">
            <Image
              src="/assets/logo.svg"
              alt="Scoutr Logo"
              width={138}
              height={0}
            />
          </div>
          {error ? (
            <>
              <Heading
                level={3}
                className="text-header font-semibold text-center"
              >
                Confirmation Failed
              </Heading>
              <Body className="text-center max-w-[25rem] mx-auto text-red-500">
                {error}
              </Body>
            </>
          ) : isSuccess ? (
            <>
              <Heading
                level={3}
                className="text-header font-semibold text-center"
              >
                Account Confirmed!
              </Heading>
              <Body className="text-center max-w-[25rem] mx-auto">
                Your account has been successfully confirmed. Redirecting to
                login...
              </Body>
            </>
          ) : (
            <>
              <Heading
                level={3}
                className="text-header font-semibold text-center"
              >
                Confirming your account...
              </Heading>
              <Body className="text-center max-w-[25rem] mx-auto">
                Please wait while we confirm your email address.
              </Body>
            </>
          )}
        </div>
      </div>
    </div>
  );
}
