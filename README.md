# Scoutr - Next.js 15 Application

A modern Next.js 15 application with TypeScript, authentication, beautiful UI components, and comprehensive route protection.

## Features

- ⚡ **Next.js 15** - Latest version with App Router
- 🔐 **Authentication** - NextAuth.js with credentials provider
- 🎨 **Beautiful UI** - shadcn/ui components with Tailwind CSS
- 📝 **TypeScript** - Full type safety with strict mode
- 🛡️ **Route Protection** - Public and private routes with middleware
- 🎯 **Code Quality** - ESLint and Prettier configuration
- 📱 **Responsive Design** - Mobile-first approach
- 🔔 **Notifications** - Toast notifications with Sonner
- 🔄 **React Query** - Server state management with caching
- ✅ **Form Validation** - React Hook Form + Zod integration
- 🗺️ **Google Maps** - Location picking and autocomplete

## Tech Stack

- **Framework**: Next.js 15 (App Router)
- **Language**: TypeScript 5+
- **Styling**: Tailwind CSS 4
- **UI Components**: shadcn/ui
- **Authentication**: NextAuth.js
- **State Management**: React Query (TanStack Query)
- **Form Management**: React Hook Form + Zod
- **Maps**: Google Maps API (@vis.gl/react-google-maps)
- **Code Quality**: ESLint + Prettier
- **Notifications**: Sonner
- **Error Tracking**: Sentry

## Getting Started

### Prerequisites

- Node.js 18+
- yarn
- Google Maps API key (for location features)

### Installation

1. Clone the repository:

```bash
git clone <repository-url>
cd Scoutr-Web-App
```

2. Install dependencies:

```bash
yarn install
```

3. Create environment variables:

```bash
cp .env.example .env.local
```

4. Configure environment variables in `.env.local`:

```env
# NextAuth
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-secret-key-here

# Google Maps
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=your-google-maps-api-key

# Backend API (if different from Next.js API routes)
NEXT_PUBLIC_API_URL=http://localhost:8000

# Sentry (optional)
NEXT_PUBLIC_SENTRY_DSN=your-sentry-dsn
```

5. Run the development server:

```bash
yarn dev
```

6. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Project Structure

```
src/
├── app/                          # Next.js App Router pages
│   ├── admin/                    # Admin pages
│   │   ├── tags/                 # Tags management
│   │   ├── locations/            # Locations management
│   │   └── users/                # Users management
│   ├── auth/                     # Authentication pages
│   │   └── signin/               # Sign-in page
│   ├── dashboard/                # Protected dashboard
│   ├── scout/                    # Scout-specific pages
│   ├── viewer/                   # Viewer-specific pages
│   ├── nextapi/                  # Next.js API routes (custom route)
│   │   └── auth/                 # NextAuth API routes
│   ├── layout.tsx                # Root layout
│   └── page.tsx                  # Landing page
├── components/
│   ├── features/                 # Feature-specific components
│   │   ├── tags/                 # Tag-related components
│   │   ├── locations/            # Location-related components
│   │   └── users/                # User-related components
│   ├── shared/                   # Shared/reusable components
│   │   ├── LoadingSpinner.tsx    # Loading spinner
│   │   ├── TableSkeleton.tsx     # Table loading skeleton
│   │   ├── ConfirmDialog.tsx     # Confirmation dialog
│   │   └── DataTablePagination.tsx
│   ├── layout/                   # Layout components
│   │   ├── Navbar.tsx            # Navigation bar
│   │   ├── Footer.tsx            # Footer
│   │   └── providers.tsx         # Context providers
│   └── ui/                       # Base UI components (Shadcn)
├── hooks/
│   ├── api/                      # React Query hooks
│   │   ├── useTags.ts            # Tags data fetching
│   │   ├── useLocations.ts       # Locations data fetching
│   │   └── useUsers.ts           # Users data fetching
│   ├── useAuth.ts                # Authentication hook
│   ├── useDebounce.ts            # Debounce utility
│   └── usePageNavbar.ts          # Dynamic navbar items
├── lib/
│   ├── validations/              # Zod validation schemas
│   │   ├── tag.ts                # Tag validation
│   │   ├── user.ts               # User validation
│   │   ├── location.ts           # Location validation
│   │   └── password.ts           # Password validation
│   ├── types/                    # TypeScript type definitions
│   │   └── common.ts            # Common types
│   ├── services/                 # API service layer
│   │   ├── tag-service.ts        # Tag API calls
│   │   ├── location-service.ts   # Location API calls
│   │   └── user-service.ts       # User API calls
│   ├── query-keys.ts             # React Query key factories
│   ├── api-client.ts             # HTTP client with auth
│   └── routes.ts                 # Application routes
├── contexts/                     # React contexts
│   └── auth-context.tsx          # Authentication context
├── types/                        # Type definitions
│   ├── index.ts                  # Main type exports
│   └── enum.ts                   # Enumerations
├── .templates/                   # Component templates
│   ├── component.tsx.template    # Component template
│   ├── hook.ts.template          # Hook template
│   └── form-modal.tsx.template   # Form modal template
└── middleware.ts                 # Route protection middleware
```

## Architecture

### Data Fetching

The application uses **React Query** for all server state management:

```tsx
import { useTags } from '@/hooks/api/useTags';

const { data, isLoading, error } = useTags({ page: 1, limit: 10 });
```

- Automatic caching and background refetching
- Optimistic updates for mutations
- Standardized error handling
- Loading states handled automatically

### Form Management

All forms use **React Hook Form + Zod** for validation:

```tsx
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { tagSchema } from '@/lib/validations';

const form = useForm({
  resolver: zodResolver(tagSchema),
  defaultValues: { name: '', description: '' },
});
```

- Type-safe validation schemas
- Automatic error handling
- Consistent form patterns

### Authentication

Authentication is handled by **NextAuth.js**:

- Session-based authentication
- Protected routes via middleware
- Role-based access control (Admin/User)
- Token refresh handling

### API Routes

The application uses a custom `/nextapi/` route prefix to avoid conflicts with backend REST API endpoints at `/api/`.

## Available Scripts

- `yarn dev` - Start development server
- `yarn build` - Build for production
- `yarn start` - Start production server
- `yarn lint` - Run ESLint
- `yarn format` - Format code with Prettier

## Code Quality

The project includes:

- **ESLint** - Next.js and TypeScript rules
- **Prettier** - Consistent code formatting
- **TypeScript** - Strict mode enabled
- **Husky** - Git hooks for pre-commit checks

## Development Guidelines

### Naming Conventions

- **Components**: PascalCase (`AddTagModal.tsx`)
- **Hooks**: camelCase (`useTags.ts`)
- **Services**: kebab-case (`tag-service.ts`)
- **Types**: kebab-case (`tag.ts`)
- **Pages**: lowercase (`page.tsx`, `layout.tsx`)

### Component Structure

1. Imports (grouped: React, Next.js, UI, Utils, Types)
2. Types/Interfaces
3. Constants
4. Component (default export)
5. Sub-components (if any)

See `src/.templates/` for component templates.

### Adding New Features

1. Create feature folder in `src/components/features/`
2. Create React Query hooks in `src/hooks/api/`
3. Create validation schemas in `src/lib/validations/`
4. Add query keys to `src/lib/query-keys.ts`
5. Create service functions in `src/lib/services/`

## Documentation

- **[CONTRIBUTING.md](./CONTRIBUTING.md)** - Detailed contributing guide
- **[CODEBASE_AUDIT.md](./CODEBASE_AUDIT.md)** - Architecture decisions and refactor plan
- **[src/.templates/README.md](./src/.templates/README.md)** - Component templates guide

## Deployment

The application can be deployed to:

- **Vercel** (recommended for Next.js)
- **Netlify**
- **Railway**
- Any platform supporting Node.js

### Environment Variables

Ensure all required environment variables are set in your deployment platform:

- `NEXTAUTH_URL` - Application URL
- `NEXTAUTH_SECRET` - Secret for session encryption
- `NEXT_PUBLIC_GOOGLE_MAPS_API_KEY` - Google Maps API key
- `NEXT_PUBLIC_API_URL` - Backend API URL (if different)

## Contributing

Please read [CONTRIBUTING.md](./CONTRIBUTING.md) for details on our code of conduct and the process for submitting pull requests.

## License

This project is licensed under the MIT License.

## Support

For support, please open an issue in the repository or contact the development team.
