'use client';

import type { Location } from '@/types';

import { Heading } from '@/components/ui/typography';
import { Badge } from '@/components/ui/badge';

interface LocationMainInfoProps {
  location: Location;
}

export function LocationMainInfo({ location }: LocationMainInfoProps) {
  return (
    <div className="flex-1 space-y-3">
      <div className="flex flex-wrap items-start justify-between gap-2">
        <Heading level={2}>{location.title}</Heading>
        {location.status && (
          <Badge
            variant="secondary"
            className="rounded-full bg-amber-100 text-amber-900 border border-amber-200 px-3 py-1 text-xs font-medium"
          >
            {location.status}
          </Badge>
        )}
      </div>

      <p className="text-sm text-gray-600">
        {location.address}, {location.city}, {location.country}
      </p>

      <div className="flex flex-wrap gap-2">
        {location.tags?.map((tag, index) => (
          <Badge
            key={`tag-${tag.id ?? index}`}
            variant="secondary"
            className="rounded-full px-3 py-1 text-xs font-medium"
          >
            {tag.name}
          </Badge>
        ))}
      </div>

      {location.description && (
        <p className="mt-4 text-sm leading-relaxed text-gray-800">
          {location.description}
        </p>
      )}
    </div>
  );
}
