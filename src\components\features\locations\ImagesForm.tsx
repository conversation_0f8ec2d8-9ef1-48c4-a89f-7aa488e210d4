'use client';

import { useCallback, useRef, useState, useEffect } from 'react';
import { Photo } from '@/types/location';
import { filesValidator } from '@/lib/utils/fileValidator';
import { FileService } from '@/lib/services/file-service';
import { UploadResult } from '@/types/file';
import { generateUUID } from '@/lib/utils';
import UploadSidebar, { UploadItem } from '@/components/shared/UploadSidebar';
import ImageGallery from './ImageGallery';
import { UploadIcon } from '@/lib/icons';
import { toastError } from '@/lib/toast';

interface ImagesFormProps {
  data: Photo[];
  gridCol?: number;
  onChange: (data: Photo[]) => void;
  onUploadStatusChange?: (hasUploading: boolean) => void;
}

const MAX_CONCURRENT_UPLOADS = 3;
const MAX_IMAGES = 100;

export default function ImagesForm({
  data,
  gridCol = 3,
  onChange,
  onUploadStatusChange,
}: ImagesFormProps) {
  const [isDragging, setIsDragging] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [uploads, setUploads] = useState<UploadItem[]>([]);
  const [uploadQueue, setUploadQueue] = useState<File[]>([]);
  const [activeUploads, setActiveUploads] = useState<Set<string>>(new Set());
  const [selectedImageIds, setSelectedImageIds] = useState<string[]>([]);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const fileServiceRef = useRef(new FileService());
  const cancelFunctionsRef = useRef<Map<string, () => void>>(new Map());
  const dataRef = useRef<Photo[]>(data);
  const fileToUploadIdRef = useRef<Map<File, string>>(new Map());
  const previousUploadCountRef = useRef(0);
  const userManuallyClosedRef = useRef(false);

  // Keep dataRef in sync with data prop
  useEffect(() => {
    dataRef.current = data;
  }, [data]);

  // Track upload status for parent component
  useEffect(() => {
    const hasUploading = uploads.some(u => u.status === 'uploading');
    onUploadStatusChange?.(hasUploading);

    const currentUploadCount = uploads.length + uploadQueue.length;
    const hasNewUploads = currentUploadCount > previousUploadCountRef.current;

    // Only auto-open sidebar for new uploads, not if user manually closed it
    if (hasNewUploads && !userManuallyClosedRef.current) {
      setSidebarOpen(true);
      userManuallyClosedRef.current = false; // Reset flag when new uploads come in
    }

    previousUploadCountRef.current = currentUploadCount;
  }, [uploads, uploadQueue.length, onUploadStatusChange]);

  // Handle manual sidebar close
  const handleSidebarOpenChange = useCallback((open: boolean) => {
    setSidebarOpen(open);
    if (!open) {
      userManuallyClosedRef.current = true;
    }
  }, []);

  const startUpload = useCallback(
    async (file: File) => {
      // Find the uploadId for this file from the map
      const uploadId = fileToUploadIdRef.current.get(file) || generateUUID();

      // If we didn't have a mapping, create one
      if (!fileToUploadIdRef.current.has(file)) {
        fileToUploadIdRef.current.set(file, uploadId);
      }

      // Update the upload status from pending to uploading
      setUploads(prev =>
        prev.map(u =>
          u.id === uploadId ? { ...u, status: 'uploading' as const } : u
        )
      );

      setActiveUploads(prev => new Set(prev).add(uploadId));

      try {
        const { uploadPromise, cancel } =
          fileServiceRef.current.uploadFileWithCancel(file, progressData => {
            setUploads(prev =>
              prev.map(u =>
                u.id === uploadId
                  ? { ...u, progress: progressData.percentage }
                  : u
              )
            );
          });

        // Store cancel function for later use
        cancelFunctionsRef.current.set(uploadId, cancel);

        const result: UploadResult = await uploadPromise;

        setActiveUploads(prev => {
          const newSet = new Set(prev);
          newSet.delete(uploadId);
          return newSet;
        });

        if (result.success && result.data) {
          // Extract URL and key from result (FileCompleteResponse)
          const uploadData = result.data as { url: string; key: string };

          // Use ref to get the latest data to avoid stale closure issues
          const currentData = dataRef.current;
          const newPhoto: Photo = {
            url: uploadData.url,
            key: uploadData.key,
            order: currentData.length,
            tags: [],
          };

          // Update formData with all images
          onChange([...currentData, newPhoto]);

          // Update upload status
          setUploads(prev =>
            prev.map(u =>
              u.id === uploadId
                ? {
                    ...u,
                    status: 'success',
                    progress: 100,
                    url: uploadData.url,
                    key: uploadData.key,
                  }
                : u
            )
          );

          // Clean up cancel function
          cancelFunctionsRef.current.delete(uploadId);
        } else {
          setUploads(prev =>
            prev.map(u =>
              u.id === uploadId
                ? {
                    ...u,
                    status: 'error',
                    error: result.error || 'Upload failed',
                  }
                : u
            )
          );
          cancelFunctionsRef.current.delete(uploadId);
        }
      } catch (error) {
        setActiveUploads(prev => {
          const newSet = new Set(prev);
          newSet.delete(uploadId);
          return newSet;
        });
        setUploads(prev =>
          prev.map(u =>
            u.id === uploadId
              ? {
                  ...u,
                  status: 'error',
                  error:
                    error instanceof Error ? error.message : 'Upload failed',
                }
              : u
          )
        );
        cancelFunctionsRef.current.delete(uploadId);
      }
    },
    [onChange]
  );

  // Process upload queue
  useEffect(() => {
    const processQueue = async () => {
      // Check if we can start more uploads
      const availableSlots = MAX_CONCURRENT_UPLOADS - activeUploads.size;
      if (availableSlots <= 0 || uploadQueue.length === 0) return;

      // Get files to upload
      const filesToUpload = uploadQueue.slice(0, availableSlots);
      const remainingQueue = uploadQueue.slice(availableSlots);

      setUploadQueue(remainingQueue);

      // Start uploads
      filesToUpload.forEach(file => {
        startUpload(file);
      });
    };

    processQueue();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [uploadQueue.length, activeUploads.size, startUpload]);

  const handleFiles = useCallback(
    (files: FileList | null) => {
      if (!files || files.length === 0) return;

      const acceptedFiles = filesValidator(Array.from(files));

      // Calculate current total images
      const currentImagesCount = data.length;
      const currentUploadsCount = uploads.length;
      const totalCurrent = currentImagesCount + currentUploadsCount;

      // Calculate how many files can be added
      const availableSlots = MAX_IMAGES - totalCurrent;

      if (availableSlots <= 0) {
        toastError(
          `Maximum limit of ${MAX_IMAGES} images reached. Please remove some images before adding new ones.`
        );
        return;
      }

      // Only process files that fit within the limit
      const filesToProcess = acceptedFiles.slice(0, availableSlots);
      const rejectedCount = acceptedFiles.length - filesToProcess.length;

      if (rejectedCount > 0) {
        toastError(
          `Only ${filesToProcess.length} of ${acceptedFiles.length} image${acceptedFiles.length === 1 ? '' : 's'} can be added. Maximum limit of ${MAX_IMAGES} images reached.`
        );
      }

      if (filesToProcess.length === 0) return;

      // Create pending upload items immediately for all files
      const pendingUploads: UploadItem[] = filesToProcess.map(file => {
        const uploadId = generateUUID();
        // Map file to uploadId for later lookup
        fileToUploadIdRef.current.set(file, uploadId);
        return {
          id: uploadId,
          file,
          progress: 0,
          status: 'pending' as const,
          previewImage: URL.createObjectURL(file),
        };
      });

      setUploads(prev => [...prev, ...pendingUploads]);

      // Add to upload queue
      setUploadQueue(prev => [...prev, ...filesToProcess]);

      // Open sidebar when files are added
      if (filesToProcess.length > 0) {
        setSidebarOpen(true);
      }
    },
    [data.length, uploads.length]
  );

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
    handleFiles(e.dataTransfer.files);
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  const handleBrowseClick = () => {
    fileInputRef.current?.click();
  };

  const handleCancelUpload = useCallback((id: string) => {
    setUploads(prev => {
      const upload = prev.find(u => u.id === id);

      // If it's pending, remove it from queue and uploads
      if (upload?.status === 'pending') {
        // Remove file from queue
        setUploadQueue(queue => queue.filter(f => f !== upload.file));
        // Remove file from mapping
        fileToUploadIdRef.current.delete(upload.file);
        // Remove upload item
        return prev.filter(u => u.id !== id);
      }

      // If it's uploading, cancel the upload
      const cancelFn = cancelFunctionsRef.current.get(id);
      if (cancelFn) {
        cancelFn();
        cancelFunctionsRef.current.delete(id);
        setActiveUploads(active => {
          const newSet = new Set(active);
          newSet.delete(id);
          return newSet;
        });
        return prev.map(u =>
          u.id === id ? { ...u, status: 'error', error: 'Cancelled' } : u
        );
      }

      return prev;
    });
  }, []);

  const handleDismissUpload = useCallback((id: string) => {
    setUploads(prev => prev.filter(u => u.id !== id));
  }, []);

  const handleImageSelect = useCallback(
    (imageId: string, selected: boolean) => {
      setSelectedImageIds(prev =>
        selected ? [...prev, imageId] : prev.filter(id => id !== imageId)
      );
    },
    []
  );

  const handleImageTagsChange = useCallback(
    (imageId: string, tags: Photo['tags']) => {
      const updatedImages = data.map((image, index) => {
        const currentImageId = image.id || `image-${index}`;
        if (currentImageId === imageId) {
          return { ...image, tags };
        }
        return image;
      });
      onChange(updatedImages);
    },
    [data, onChange]
  );

  const handleBulkTagsChange = useCallback(
    (imageIds: string[], tags: Photo['tags']) => {
      const updatedImages = data.map((image, index) => {
        const currentImageId = image.id || `image-${index}`;
        if (imageIds.includes(currentImageId)) {
          // Replace tags for selected images
          return { ...image, tags };
        }
        return image;
      });
      onChange(updatedImages);
    },
    [data, onChange]
  );

  const handleReorder = useCallback(
    (reorderedImages: Photo[]) => {
      onChange(reorderedImages);
    },
    [onChange]
  );

  // Show gallery if images exist, otherwise show upload form
  if (data.length > 0) {
    return (
      <div className="w-full flex gap-6">
        {/* Main Content */}
        <div className="flex-1">
          <ImageGallery
            images={data}
            gridCol={gridCol}
            selectedImageIds={selectedImageIds}
            onImageSelect={handleImageSelect}
            onImageTagsChange={handleImageTagsChange}
            onBulkTagsChange={handleBulkTagsChange}
            onFilesSelect={handleFiles}
            onReorder={handleReorder}
          />
        </div>

        {/* Upload Sidebar */}
        <UploadSidebar
          uploads={uploads}
          open={sidebarOpen}
          onOpenChange={handleSidebarOpenChange}
          onCancel={handleCancelUpload}
          onDismiss={handleDismissUpload}
        />
      </div>
    );
  }

  return (
    <div className="w-full max-w-[672px] flex flex-col gap-3 relative">
      {/* Title and Subtitle */}
      <div className="flex flex-col gap-3">
        <h1 className="text-2xl font-semibold leading-9 text-neutral-900">
          Add images for the location
        </h1>
        <p className="text-base leading-7 text-neutral-600">
          Upload up to {MAX_IMAGES} images for the location.
        </p>
      </div>

      {/* Drag and Drop Area */}
      <div className="relative">
        <div
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          onClick={handleBrowseClick}
          className={`
            bg-neutral-50 border-2 border-dashed rounded-xl
            h-[240px] w-full
            flex flex-col items-center justify-center
            cursor-pointer transition-colors
            ${
              isDragging
                ? 'border-primary-300 bg-primary-50'
                : 'border-neutral-300 hover:border-primary-200'
            }
          `}
        >
          {/* Upload Icon */}
          <div className="flex flex-col items-center gap-5">
            <div className="w-16 h-16 rounded-full bg-neutral-200 flex items-center justify-center">
              <UploadIcon className="w-8 h-6 text-[#737373]" />
            </div>

            {/* Text Content */}
            <div className="flex flex-col items-center gap-1">
              <p className="text-xl leading-7 text-neutral-700 text-center">
                Drag and drop images here
              </p>
              <p className="text-base leading-6 text-neutral-500 text-center">
                or click to browse your files
              </p>
            </div>
          </div>
        </div>

        {/* File Input */}
        <input
          ref={fileInputRef}
          type="file"
          accept="image/jpeg,image/jpg,image/png"
          multiple
          onChange={e => handleFiles(e.target.files)}
          className="hidden"
        />

        {/* Supported Formats Text */}
        <p className="text-sm leading-5 text-neutral-500 text-center mt-4">
          Supported formats: JPG, PNG. Max size: 10MB per file.
        </p>
      </div>

      {/* Upload Sidebar */}
      <UploadSidebar
        uploads={uploads}
        open={sidebarOpen}
        onOpenChange={handleSidebarOpenChange}
        onCancel={handleCancelUpload}
        onDismiss={handleDismissUpload}
      />
    </div>
  );
}
