'use client';

import { useAuth } from '@/contexts/auth-context';
import { Role } from '@/types';
import { AdminDashboardView } from '@/components/dashboard/AdminDashboardView';
import { PropertyOwnerDashboardView } from '@/components/dashboard/PropertyOwnerDashboardView';
import { ScoutProductionAdminDashboardView } from '@/components/dashboard/ScoutProductionAdminDashboardView';
import { DashboardLoading } from '@/components/dashboard/DashboardLoading';

export default function DashboardPage() {
  const { user } = useAuth();

  // Show loading state if user is not yet loaded
  if (!user) {
    return <DashboardLoading />;
  }

  // Render dashboard based on user role
  if (user.role === Role.SuperAdmin) {
    return <AdminDashboardView />;
  }

  if (user.role === Role.PropertyOwner) {
    return <PropertyOwnerDashboardView />;
  }

  // Default to Scout/ProductionAdmin dashboard
  return <ScoutProductionAdminDashboardView />;
}
