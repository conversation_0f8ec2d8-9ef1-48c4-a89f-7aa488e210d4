import {
  authService,
  userService,
  dashboardService,
  locationService,
  tagService,
  assetService,
  notificationService,
  productionHouseService,
  referenceService,
} from '@/lib/services';

/**
 * Custom hooks for domain services
 * These provide a clean interface for components to access domain-specific functionality
 */

// Authentication service hook
export function useAuthService() {
  return authService;
}

// User service hook
export function useUserService() {
  return userService;
}

// Dashboard service hook
export function useDashboardService() {
  return dashboardService;
}

// Location service hook
export function useLocationService() {
  return locationService;
}

// tag service hook
export function useTagService() {
  return tagService;
}

// Asset service hook
export function useAssetService() {
  return assetService;
}

// Notification service hook
export function useNotificationService() {
  return notificationService;
}

// Production house service hook
export function useProductionHouseService() {
  return productionHouseService;
}

// Reference service hook
export function useReferenceService() {
  return referenceService;
}
