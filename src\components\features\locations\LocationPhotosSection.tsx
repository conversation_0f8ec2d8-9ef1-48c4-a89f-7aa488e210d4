'use client';

import Image from 'next/image';
import { Plus, MessageCircle } from 'lucide-react';

import type { Location } from '@/types';
import { Badge } from '@/components/ui/badge';

interface LocationPhotosSectionProps {
  location: Location;
  onSeeAllPhotos?: () => void;
}

export function LocationPhotosSection({
  location,
  onSeeAllPhotos,
}: LocationPhotosSectionProps) {
  const handleSeeAll = () => {
    if (onSeeAllPhotos) {
      onSeeAllPhotos();
    }
  };

  if (!location.images || location.images.length === 0) {
    return null;
  }

  return (
    <div className="grid gap-2 sm:gap-6 grid-cols-2 sm:grid-cols-4 grid-rows-2">
      {location.images[0] && (
        <div className="relative row-span-2 col-span-2 rounded-xl overflow-hidden aspect-[552/337] group">
          <Image
            src={location.images[0].url}
            alt="Most relevant photo"
            fill
            className="object-cover hover:scale-105 transition-transform duration-300"
          />
          {/* Tags */}
          {location.images[0].tags && location.images[0].tags.length > 0 && (
            <div className="absolute top-2 right-2 flex flex-wrap gap-1 justify-end">
              {location.images[0].tags.slice(0, 1).map((tag, tagIndex) => (
                <Badge
                  key={tag.id ?? tagIndex}
                  variant="secondary"
                  className="bg-white/90 border border-gray-200 text-gray-700 text-xs font-medium px-2 py-0.5"
                >
                  {tag.name}
                </Badge>
              ))}
            </div>
          )}
          {/* Comment count */}
          {(location.images[0].commentCount ?? 0) > 0 && (
            <div className="absolute bottom-2 right-2 flex items-center gap-1 px-2 py-1 text-xs font-medium text-white">
              <MessageCircle className="w-3 h-3" />
              <span>{location.images[0].commentCount}</span>
            </div>
          )}
        </div>
      )}

      {location.images.slice(1, 5).map((photo, index) => (
        <div
          key={photo.id ?? index}
          className="relative rounded-xl overflow-hidden sm:h-auto aspect-[263/157] group"
        >
          {index === 3 && location.images.length > 4 ? (
            <button
              type="button"
              onClick={handleSeeAll}
              className="absolute inset-0 flex flex-col items-center justify-center bg-black/40 text-white text-sm sm:text-base font-medium hover:bg-black/50 transition cursor-pointer"
            >
              <span className="font-semibold flex items-center gap-2">
                <Plus className="w-4 h-4" /> See all {location.images.length}{' '}
                images
              </span>
            </button>
          ) : (
            <>
              <Image
                src={photo.url}
                alt={`Photo ${index + 2}`}
                fill
                className="object-cover hover:scale-105 transition-transform duration-300"
              />
              {/* Tags */}
              {photo.tags && photo.tags.length > 0 && (
                <div className="absolute top-2 right-2 flex flex-wrap gap-1 justify-end">
                  {photo.tags.slice(0, 1).map((tag, tagIndex) => (
                    <Badge
                      key={tag.id ?? tagIndex}
                      variant="secondary"
                      className={`${tag.color ? `bg-${tag.color} text-white` : ''}`}
                    >
                      {tag.name}
                    </Badge>
                  ))}
                </div>
              )}
              {/* Comment count */}
              {(photo.commentCount ?? 0) > 0 && (
                <div className="absolute bottom-2 right-2 flex items-center gap-1 px-2 py-1 text-xs font-medium text-white">
                  <span>{photo.commentCount}</span>
                  <MessageCircle className="w-3 h-3" />
                </div>
              )}
            </>
          )}
        </div>
      ))}
    </div>
  );
}
