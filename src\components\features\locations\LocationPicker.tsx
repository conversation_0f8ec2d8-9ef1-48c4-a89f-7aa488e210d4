'use client';

import { DistanceIcon } from '@/lib/icons';
import LocationPickerModal from '@/components/features/locations/LocationPickerModal';
import { useState } from 'react';
import LocationAutocomplete from '@/components/features/locations/LocationAutoComplete';
import { APIProvider } from '@vis.gl/react-google-maps';

interface LocationPickerProps {
  defaultAddress?: {
    lat?: number;
    lng?: number;
    address?: string;
  } | null;
  onChange: (
    address?: string,
    latitude?: number,
    longitude?: number,
    placeId?: string,
    city?: string,
    state?: string,
    country?: string,
    postalCode?: string
  ) => void | null;
  icon?: React.ElementType;
  fullAddressMode?: boolean;
  placeholder?: string;
}

export default function LocationPicker({
  onChange,
  defaultAddress,
  icon: Icon = DistanceIcon,
  fullAddressMode,
  placeholder,
}: LocationPickerProps) {
  const [locationPickerOpen, setLocationPickerOpen] = useState<boolean>(false);
  const [address, setAddress] = useState(defaultAddress?.address ?? '');
  const handleChangeLocation = (
    data: {
      address?: string;
      lat?: number;
      lng?: number;
      placeId?: string;
      city?: string;
      state?: string;
      country?: string;
      postalCode?: string;
    } | null
  ) => {
    if (data) {
      const { address, lat, lng, placeId, city, state, country, postalCode } =
        data;
      onChange(address, lat, lng, placeId, city, state, country, postalCode);
      setAddress(address ?? '');
    } else {
      onChange(
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        undefined,
        undefined
      );
      setAddress('');
    }
  };

  return (
    <APIProvider
      apiKey={process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY!}
      libraries={['places']}
    >
      <LocationAutocomplete
        value={address}
        fullAddressMode={fullAddressMode}
        onSelect={place => {
          if (place) {
            console.log('place', place);
            const coords = {
              address: place.formatted_address,
              lat: place.geometry?.location?.lat(),
              lng: place.geometry?.location?.lng(),
              placeId: place.place_id,
              city: place.address_components?.find(component =>
                component.types.includes('locality')
              )?.long_name,
              state: place.address_components?.find(component =>
                component.types.includes('administrative_area_level_1')
              )?.long_name,
              country: place.address_components?.find(component =>
                component.types.includes('country')
              )?.long_name,
              postalCode: place.address_components?.find(component =>
                component.types.includes('postal_code')
              )?.long_name,
            };
            handleChangeLocation(coords);
          } else handleChangeLocation(null);
        }}
        placeholder={placeholder}
      />
      <Icon
        onClick={() => setLocationPickerOpen(true)}
        className="absolute right-3 top-1/2 -translate-y-1/2 h-4 w-4 cursor-pointer"
      />
      <LocationPickerModal
        defaultAddress={defaultAddress}
        open={locationPickerOpen}
        onOpenChange={setLocationPickerOpen}
        onChange={handleChangeLocation}
      />
    </APIProvider>
  );
}
