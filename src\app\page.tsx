'use client';

import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Routes } from '@/lib/routes';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { useAuth } from '@/contexts/auth-context';
import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Role } from '@/types/enum';

export default function HomePage() {
  const { user } = useAuth();
  const router = useRouter();

  useEffect(() => {
    switch (user?.role) {
      case Role.SuperAdmin:
        router.push(Routes.ADMIN_USERS);
        break;
      default:
        router.push(Routes.SIGN_IN);
    }
  }, [router, user]);

  return (
    <div className="">
      {/* Hero Section */}
      <div className="mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <div className="text-center">
          <h1 className="text-4xl font-bold tracking-tight sm:text-6xl">
            Welcome to <span className="text-blue-600">Scoutr</span>
          </h1>
          <p className="mt-6 text-lg leading-8 max-w-2xl mx-auto">
            A modern Next.js application with authentication, beautiful UI
            components, and everything you need to build amazing web
            applications.
          </p>
          <div className="mt-10 flex items-center justify-center gap-x-6">
            <Link href={Routes.SIGN_IN}>
              <Button size="lg">Get Started</Button>
            </Link>
            <Link href={Routes.DASHBOARD}>
              <Button variant="outline" size="lg">
                Go to Dashboard
              </Button>
            </Link>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <div className="text-center mb-16">
          <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
            Features
          </h2>
          <p className="mt-4 text-lg">
            Everything you need to build modern web applications
          </p>
        </div>

        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
          <Card>
            <CardHeader>
              <CardTitle>Authentication</CardTitle>
              <CardDescription>
                Secure user authentication with NextAuth.js
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm">
                Built-in authentication system with protected routes and user
                management.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Beautiful UI</CardTitle>
              <CardDescription>
                Modern components with shadcn/ui and Tailwind CSS
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm">
                Pre-built components that look great and are fully customizable.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>TypeScript</CardTitle>
              <CardDescription>
                Full TypeScript support for better development experience
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm">
                Type-safe code with excellent IDE support and error prevention.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Route Protection</CardTitle>
              <CardDescription>
                Public and private routes with middleware
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm">
                Automatic route protection with redirects and role-based access.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Code Quality</CardTitle>
              <CardDescription>
                ESLint and Prettier for consistent code
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm">
                Automated code formatting and linting for maintainable code.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Responsive Design</CardTitle>
              <CardDescription>Mobile-first responsive design</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm">
                Works perfectly on all devices with responsive components.
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
