'use client';

import { useSearchPara<PERSON>, useRouter } from 'next/navigation';
import { useState, useEffect } from 'react';
import { toastError, toastSuccess } from '@/lib/toast';
import { Heading } from '@/components/ui/typography';
import { Body } from '@/components/ui/typography';
import Image from 'next/image';
import PasswordForm from '@/components/PasswordForm';
import { authService } from '@/lib/services/auth-service';
import { Routes } from '@/lib/routes';
import { handleError } from '@/lib/error-handler';

export default function SetupPasswordPage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const token = searchParams.get('token');

  useEffect(() => {
    if (!token) {
      toastError('Invalid setup link. Please check your invitation email.', {
        id: 'setup-password-token',
      });
      router.push(Routes.SIGN_IN);
    }
  }, [token, router]);

  useEffect(() => {
    if (error) {
      toastError(error, {
        id: 'setup-password',
      });
    }
  }, [error]);

  const handleSubmit = async (password: string) => {
    if (!token) {
      setError('Invalid setup link. Please check your invitation email.');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      await authService.acceptInvitation({
        token,
        password,
      });

      // Show success toast
      toastSuccess('Account created successfully.');
      router.push(Routes.SIGN_IN);
    } catch (err) {
      const { message } = handleError(
        err,
        'An error occurred while setting password'
      );
      setError(message);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-4 max-w-[28.5rem] w-full">
      <div className="text-center md:text-left">
        <div className="flex items-center justify-center">
          <Image
            src="/assets/logo.svg"
            alt="Scoutr Logo"
            width={138}
            height={0}
          />
        </div>
        <div className="space-y-2 mt-2">
          <Heading level={3} className="text-header font-semibold text-center">
            Set up your password
          </Heading>
          <Body className="text-center max-w-[24.625rem] mx-auto">
            Choose a strong password to secure your account.
          </Body>
        </div>
      </div>

      <PasswordForm
        termEnable={true}
        buttonText="Create Account"
        onSubmit={handleSubmit}
        isLoading={isLoading}
        error={error}
      />
    </div>
  );
}
