import { Comment } from '@/types';
import { httpClient } from '../http-client';

export class CommentService {
  async getReferenceItemComments(
    referenceListId: string,
    referenceItemId: string
  ): Promise<Comment[]> {
    return httpClient.get<Comment[]>(
      `/reference-lists/${referenceListId}/items/${referenceItemId}/comments`
    );
  }

  async createReferenceItemComment(
    referenceListId: string,
    referenceItemId: string,
    content: string
  ): Promise<Comment> {
    return httpClient.post<Comment>(
      `/reference-lists/${referenceListId}/items/${referenceItemId}/comments`,
      { content }
    );
  }

  async deleteReferenceItemComment(
    referenceListId: string,
    referenceItemId: string,
    commentId: string
  ): Promise<void> {
    return httpClient.delete(
      `/reference-lists/${referenceListId}/items/${referenceItemId}/comments/${commentId}`
    );
  }

  async updateReferenceItemComment(
    referenceListId: string,
    referenceItemId: string,
    commentId: string,
    content: string
  ): Promise<Comment> {
    return httpClient.put<Comment>(
      `/reference-lists/${referenceListId}/items/${referenceItemId}/comments/${commentId}`,
      { content }
    );
  }
}

export const commentService = new CommentService();
