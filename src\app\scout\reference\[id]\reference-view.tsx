'use client';

import ConfirmDialog from '@/components/shared/ConfirmDialog';
import LocationCard from '@/components/features/locations/LocationCard';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Typography } from '@/components/ui/typography';
import { PlayArrowIcon, SearchIcon, ShareIcon } from '@/lib/icons';
import { getDisplayText } from '@/lib/utils';
import { useState } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Location, Reference } from '@/types';
import ShareReferenceListModal from '@/components/project/ShareReferenceListModal';
import Image from 'next/image';
import { ProductionHouse } from '@/lib/services/production-service';
import InlineEdit from '@/components/InlineEdit';
import { CommentModal } from '@/components/comment/CommentModal';

interface ReferenceListProps {
  items: Location[];
  productionHouse: ProductionHouse;
  dateRange: string;
  totalReferences: number;
  status: string;
  reference?: Reference;
  onDelete: (location?: Location) => void;
  onUpdate: (projectName: string) => void;
  searchChange: (search: string) => void;
}

export default function ReferenceListView({
  items,
  productionHouse,
  dateRange,
  totalReferences,
  status,
  reference,
  onDelete,
  onUpdate,
  searchChange,
}: ReferenceListProps) {
  const [search, setSearch] = useState<string>('');
  const [selectedLocation, setSelectedLocation] = useState<Location>();
  const [shareReferenceListOpen, setShareReferenceListOpen] =
    useState<boolean>(false);
  const [removeLocationModalOpen, setRemoveLocationModalOpen] =
    useState<boolean>(false);
  const [addCommentDialogOpen, setAddCommentDialogOpen] =
    useState<boolean>(false);
  const router = useRouter();
  const params = useParams();
  const id = params.id as string;

  return (
    <>
      <div className="w-full flex justify-center">
        <div className="flex flex-col space-y-6 xl:w-[70.5rem] p-6 xl:px-0">
          <div className="flex items-center gap-2">
            <InlineEdit
              value={reference?.projectName ?? ''}
              onSave={onUpdate}
            />
          </div>
          <Typography color="muted">
            {productionHouse.name} • {dateRange} • {totalReferences}{' '}
            {totalReferences > 1 ? 'references' : 'reference'}
            {
              <Badge variant="secondary" className="ml-2">
                {getDisplayText(status)}{' '}
              </Badge>
            }
          </Typography>
          <div className="flex-1 flex flex-col items-center w-full space-y-6">
            {!(!search && items.length === 0) && (
              <div className="flex flex-col w-full gap-4 lg:flex-row lg:items-center lg:justify-between">
                <div className="flex flex-col gap-4 lg:flex-row lg:items-center lg:flex-1 lg:justify-between">
                  <div className="relative w-full lg:w-[22.4375rem]">
                    <SearchIcon className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="Search location..."
                      value={search ?? ''}
                      onChange={e => {
                        setSearch(e.target.value);
                        searchChange(e.target.value);
                      }}
                      className="pl-9"
                    />
                  </div>
                  <div className="flex gap-3 lg:flex-row lg:items-center lg:justify-end">
                    <Button
                      variant="outline"
                      className="w-[9.9375rem] h-[2.5625rem]"
                      onClick={() => {
                        router.push(`/scout/reference/${id}/search-locations`);
                      }}
                    >
                      Search locations
                    </Button>
                    <Button
                      onClick={() => {
                        router.push(`/scout/reference/${id}/preview`);
                      }}
                      variant="outline"
                      className="w-[9.9375rem] h-[2.5625rem]"
                    >
                      <PlayArrowIcon /> Preview
                    </Button>
                    <Button
                      onClick={() => setShareReferenceListOpen(true)}
                      className="w-[12.5625rem] h-[2.5625rem]"
                    >
                      <ShareIcon />
                      Share List
                    </Button>
                  </div>
                </div>
              </div>
            )}
            {items.length > 0 && (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                {items.map((location, index) => {
                  return (
                    <LocationCard
                      key={index}
                      location={location}
                      onDelete={() => {
                        setSelectedLocation(location);
                        setRemoveLocationModalOpen(true);
                      }}
                      onEdit={location => {
                        router.push(
                          `/scout/reference/${params.id}/edit-images/${location.id}`
                        );
                      }}
                      onAddComment={location => {
                        setSelectedLocation(location);
                        setAddCommentDialogOpen(true);
                      }}
                    />
                  );
                })}
              </div>
            )}
            {!search && items.length === 0 && (
              <div className="grid place-items-center items-center justify-center my-auto">
                <div className="space-y-12 text-center">
                  <div className="w-[20rem] flex flex-col items-center space-y-12">
                    <Image
                      src="/assets/circle-placeholder.svg"
                      alt="Circle Placeholder"
                      width={168}
                      height={168}
                    />
                    <div>
                      <h1 className="text-2xl font-bold mb-">
                        Start building your reference list
                      </h1>
                      <span className="text-base">
                        Your selected locations will appear here.
                      </span>
                    </div>
                  </div>
                  <Button
                    variant="default"
                    className="w-[11.125rem] h-[2.5625rem]"
                    onClick={() => {
                      router.push(`/scout/reference/${id}/search-locations`);
                    }}
                  >
                    Search locations
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
      <ConfirmDialog
        open={removeLocationModalOpen}
        onOpenChange={setRemoveLocationModalOpen}
        title="Remove reference from list?"
        description="This location will be removed from the reference list. You can add it again later if needed."
        confirmText="Remove"
        cancelText="Keep location"
        onConfirm={() => onDelete(selectedLocation)}
      />
      <ShareReferenceListModal
        open={shareReferenceListOpen}
        onOpenChange={setShareReferenceListOpen}
        referenceId={id}
      />
      <CommentModal
        open={addCommentDialogOpen}
        onOpenChange={setAddCommentDialogOpen}
        reference={reference}
        referenceItem={reference?.items.find(
          item => item.id === selectedLocation?.id
        )}
      />
    </>
  );
}
