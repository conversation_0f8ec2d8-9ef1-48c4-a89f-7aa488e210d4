import { ProductionHouse } from '@/lib/services/production-service';
import { Location } from './location';
import { Tag } from './tag';
import { User } from './user';

export interface Reference {
  id: string;
  internalNotes?: string;
  items: ReferenceItem[];
  productionHouse: ProductionHouse;
  projectName: string;
  scout: User;
  shootDateStart: string;
  shootDateEnd: string;
  status: string;
  coverImageUrl?: string;
}

export interface ResponseImageItem {
  id: string;
  order: number;
  image: {
    id: string;
    key: string;
    locationId: string;
    order: number;
    url: string;
    tags?: Tag[];
  };
}
export interface ReferenceItem {
  commentCount: number;
  id: string;
  images: ResponseImageItem[];
  location: Location;
}

export interface ReferenceWithFeedback extends Reference {
  items: (ReferenceItem & {
    like: boolean;
    dislike: boolean;
  })[];
}
