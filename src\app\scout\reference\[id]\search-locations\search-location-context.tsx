'use client';

import React, {
  createContext,
  useContext,
  useState,
  useCallback,
  useMemo,
  ReactNode,
  useEffect,
} from 'react';
import { useSearchParams } from 'next/navigation';
import isEqual from 'lodash/isEqual';
import { Reference } from '@/types';

export interface Address {
  address?: string;
  placeId?: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
  latitude?: string;
  longitude?: string;
}

export interface LocationFilters {
  query: string;
  where: Address;
  category: string;
  subCategories: string[];
  style: string[];
  size: {
    small: boolean;
    medium: boolean;
    large: boolean;
  };
}

export const defaultFilters: LocationFilters = {
  query: '',
  where: {} as Address,
  category: '',
  subCategories: [],
  style: [],
  size: { small: false, medium: false, large: false },
};

// Utility functions for URL synchronization
function serializeFiltersToUrl(filters: LocationFilters): URLSearchParams {
  const params = new URLSearchParams();

  if (filters.query) params.set('query', filters.query);
  if (
    Object.keys(filters.where).length &&
    filters.where.latitude !== 'undefined' &&
    filters.where.longitude !== 'undefined'
  )
    params.set('where', JSON.stringify(filters.where));
  if (filters.category) params.set('category', filters.category);
  if (filters.subCategories.length > 0) {
    params.set('subCategories', filters.subCategories.join(','));
  }
  if (filters.style.length > 0) {
    params.set('style', filters.style.join(','));
  }

  // Handle size filters
  const sizeFilters = [];
  if (filters.size.small) sizeFilters.push('small');
  if (filters.size.medium) sizeFilters.push('medium');
  if (filters.size.large) sizeFilters.push('large');
  if (sizeFilters.length > 0) {
    params.set('size', sizeFilters.join(','));
  }

  return params;
}

function deserializeFiltersFromUrl(
  searchParams: URLSearchParams
): LocationFilters {
  const filters = { ...defaultFilters };

  const query = searchParams.get('query');
  if (query) filters.query = query;

  const where = searchParams.get('where');
  if (where && Object.keys(JSON.parse(where)).length)
    filters.where = JSON.parse(where);

  const category = searchParams.get('category');
  if (category) {
    filters.category = category;
  }

  const subCategories = searchParams.get('subCategories');
  if (subCategories) {
    filters.subCategories = subCategories.split(',').filter(Boolean);
  }

  const style = searchParams.get('style');
  if (style) {
    filters.style = style.split(',').filter(Boolean);
  }

  const size = searchParams.get('size');
  if (size) {
    const sizeArray = size.split(',').filter(Boolean);
    filters.size = {
      small: sizeArray.includes('small'),
      medium: sizeArray.includes('medium'),
      large: sizeArray.includes('large'),
    };
  }

  return filters;
}

interface SearchLocationContextType {
  // Panel state
  isPanelOpen: boolean;
  togglePanel: () => void;
  openPanel: () => void;
  closePanel: () => void;

  // Filters state
  filters: LocationFilters;
  setFilters: (
    updater: LocationFilters | ((prev: LocationFilters) => LocationFilters)
  ) => void;

  // Reference state
  reference?: Reference;
  setReference: (
    updater:
      | Reference
      | ((prev: Reference | undefined) => Reference | undefined)
  ) => void;
}

const SearchLocationContext = createContext<
  SearchLocationContextType | undefined
>(undefined);

interface SearchLocationProviderProps {
  children: ReactNode;
}

export function SearchLocationProvider({
  children,
}: SearchLocationProviderProps) {
  const searchParams = useSearchParams();

  // Panel state
  const [isPanelOpen, setIsPanelOpen] = useState(false);

  // Initialize filters from URL or use defaults
  const [filters, setFiltersState] = useState<LocationFilters>(() => {
    return deserializeFiltersFromUrl(searchParams);
  });

  // Reference state
  const [reference, setReferenceState] = useState<Reference | undefined>();

  // Panel actions
  const togglePanel = useCallback(() => {
    setIsPanelOpen(prev => !prev);
  }, []);

  const openPanel = useCallback(() => {
    setIsPanelOpen(true);
  }, []);

  const closePanel = useCallback(() => {
    setIsPanelOpen(false);
  }, []);

  // Filters actions
  const setFilters = useCallback(
    (
      updater: LocationFilters | ((prev: LocationFilters) => LocationFilters)
    ) => {
      setFiltersState(prev =>
        typeof updater === 'function' ? updater(prev) : updater
      );
    },
    []
  );

  // Reference actions
  const setReference = useCallback(
    (
      updater:
        | Reference
        | ((prev: Reference | undefined) => Reference | undefined)
    ) => {
      setReferenceState(prev =>
        typeof updater === 'function' ? updater(prev) : updater
      );
    },
    []
  );
  // Sync URL when filters change (but not on initial load)
  useEffect(() => {
    const urlParams = serializeFiltersToUrl(filters);
    const currentSearchParams = searchParams.toString();
    const newSearchParams = urlParams.toString();

    // Only update URL if it's different from current URL
    if (currentSearchParams !== newSearchParams) {
      // Use window.history.replaceState to update URL without triggering Next.js navigation
      // This prevents the page from refetching, allowing manual API data fetching
      const newUrl = `${window.location.pathname}${newSearchParams ? `?${newSearchParams}` : ''}`;
      window.history.replaceState(
        { ...window.history.state, as: newUrl, url: newUrl },
        '',
        newUrl
      );

      // Manually trigger a custom event to notify components that search params changed
      // This is necessary because useSearchParams might not update with history.replaceState
      window.dispatchEvent(new PopStateEvent('popstate'));
    }
  }, [filters, searchParams]);

  // Sync filters when URL changes (e.g., browser back/forward)
  useEffect(() => {
    const urlFilters = deserializeFiltersFromUrl(searchParams);
    setFiltersState(prev => (isEqual(prev, urlFilters) ? prev : urlFilters));
  }, [searchParams]);

  const value = useMemo<SearchLocationContextType>(
    () => ({
      // Panel state
      isPanelOpen,
      togglePanel,
      openPanel,
      closePanel,

      // Filters state
      filters,
      setFilters,

      // Reference state
      reference: reference || undefined,
      setReference,
    }),
    [
      isPanelOpen,
      togglePanel,
      openPanel,
      closePanel,
      filters,
      setFilters,
      reference,
      setReference,
    ]
  );

  return (
    <SearchLocationContext.Provider value={value}>
      {children}
    </SearchLocationContext.Provider>
  );
}

export function useSearchLocation() {
  const context = useContext(SearchLocationContext);
  if (context === undefined) {
    throw new Error(
      'useSearchLocation must be used within a SearchLocationProvider'
    );
  }
  return context;
}

// Convenience hooks for specific functionality
export function useSearchLocationFilters() {
  const { filters, setFilters } = useSearchLocation();
  return {
    filters,
    setFilters,
  };
}

export function useSearchLocationPanel() {
  const { isPanelOpen, togglePanel, openPanel, closePanel } =
    useSearchLocation();
  return { isPanelOpen, togglePanel, openPanel, closePanel };
}

export function useSearchLocationReference() {
  const { reference, setReference } = useSearchLocation();
  return { reference, setReference };
}
