import { useEffect } from 'react';
import { useNavbar, NavbarItem } from '@/contexts/navbar-context';

/**
 * Custom hook to easily manage page-specific navbar items
 * Automatically cleans up navbar items when the component unmounts
 */
export function usePageNavbar(items: NavbarItem[]) {
  const { setAdditionalItems, clearNavbarItems } = useNavbar();

  useEffect(() => {
    setAdditionalItems(items);

    // Cleanup navbar items when component unmounts
    return () => {
      clearNavbarItems();
    };
  }, [items, setAdditionalItems, clearNavbarItems]);
}
