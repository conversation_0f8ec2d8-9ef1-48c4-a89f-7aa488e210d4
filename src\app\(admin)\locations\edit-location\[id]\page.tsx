'use client';
import { But<PERSON> } from '@/components/ui/button';
import { Routes } from '@/lib/routes';
import { Loader2, ChevronRight } from 'lucide-react';
import { use, useCallback, useEffect, useState } from 'react';
import { isEmpty } from 'lodash';
import { useRouter } from 'next/navigation';
import ImagesForm from '@/components/features/locations/ImagesForm';
import { MINIMUM_IMAGES, PROPERTY_CATEGORY } from '@/types/constant';
import { TagType } from '@/types/enum';
import BasicDetailsForm from '@/components/BasicDetailsForm';
import TagsForm from '@/components/features/tags/TagsForm';
import { useLocationService, useTagService } from '@/hooks/use-services';
import { Tag, TagOption } from '@/types/tag';
import { Location, LocationRequest, Photo } from '@/types/location';
import { UpdateLocationData } from '@/lib/services/location-service';
import Loading from './loading';
import { toastError, toastSuccess, toastWarning } from '@/lib/toast';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import { Body, Heading } from '@/components/ui/typography';

const locationDefault: LocationRequest = {
  address: '',
  description: '',
  contactEmail: '',
  contactPhones: [],
  propertyType: '',
  tags: {},
  size: '',
  images: [],
};
export default function EditLocationPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = use(params);
  const router = useRouter();
  const tagService = useTagService();
  const locationService = useLocationService();
  const [formData, setFormData] = useState<LocationRequest>(locationDefault);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const [hasUploadingFiles, setHasUploadingFiles] = useState<boolean>(false);
  const [tagsByType, setTagsByType] = useState<
    Partial<Record<TagType, TagOption[]>>
  >({});

  const buildEditLocationPayload = (
    data: LocationRequest
  ): UpdateLocationData => {
    const {
      address,
      latitude,
      longitude,
      description,
      tags,
      size,
      images,
      placeId,
      city,
      state,
      country,
      postalCode,
    } = data;

    // Flatten tags object into array of tag IDs
    const tagIds = Object.values(tags || {}).flat();

    return {
      address,
      latitude,
      longitude,
      description,
      placeId,
      city,
      state,
      country,
      postalCode,
      tagIds,
      size,
      images: images.map(image => ({
        id: image.id,
        url: image.url,
        order: image.order,
        key: image.key || '',
        tagIds: (image.tags || []).map(tag => tag.id),
      })),
    };
  };

  // Preload all tags and organize by category
  useEffect(() => {
    const fetchAllTags = async () => {
      try {
        const allTags = await tagService.getAllTags();

        // Convert tags to TagOption format and organize by category
        const organizedTags: Partial<Record<TagType, TagOption[]>> = {};

        allTags.forEach((tag: Tag) => {
          const tagOption: TagOption = {
            value: tag.id,
            label: tag.name,
            color: tag.color,
            type: tag.type as TagType,
          };

          // Only include tags with valid TagCategory types
          if (Object.values(TagType).includes(tag.type as TagType)) {
            const type = tag.type as TagType;
            if (!organizedTags[type]) {
              organizedTags[type] = [];
            }
            organizedTags[type]!.push(tagOption);
          }
        });

        setTagsByType(organizedTags);
      } catch (error) {
        console.error('Error fetching tags:', error);
      }
    };

    fetchAllTags();
  }, [tagService]);

  const handleEditLocation = async () => {
    setIsSaving(true);
    try {
      if (formData.images.some((image: Photo) => isEmpty(image.tags))) {
        toastWarning('Not all images have a tag.');
      } else {
        const payload = buildEditLocationPayload(formData);
        await locationService.updateLocation(id, payload);
        toastSuccess('Location updated successfully.');
        router.push(Routes.ADMIN_LOCATIONS);
      }
    } catch {
      toastError('Failed to update location');
    } finally {
      setIsSaving(false);
    }
  };

  const validateForm = (data: Partial<LocationRequest>) => {
    if (!data) {
      return true;
    }
    if (!data.address || !data.description || !data.contactEmail) {
      return true;
    }
    // Check if at least one phone has 10 digits
    const hasValidPhone =
      data.contactPhones &&
      data.contactPhones.some(phone => phone.replace(/\D/g, '').length === 10);
    if (!hasValidPhone) return true;

    // Validate tags - check that at least one tag is selected for each required category
    if (data.propertyType) {
      const requiredCategories = PROPERTY_CATEGORY[data.propertyType] || [];
      const hasAllRequiredTags = requiredCategories.every(
        category => data.tags?.[category] && data.tags[category]!.length > 0
      );
      if (!hasAllRequiredTags || !data.size) return true;
    }

    return (
      !data.images ||
      (Array.isArray(data.images) && data.images.length < MINIMUM_IMAGES)
    );
  };

  const handleImagesChange = (newImages: Photo[]) => {
    setFormData(prev => ({
      ...prev,
      images: newImages,
    }));
  };

  const handleChange = (data: Partial<LocationRequest>) => {
    if (data) {
      setFormData((prev: LocationRequest) => ({
        ...prev,
        ...data,
      }));
    }
  };

  const fetchLocationData = useCallback(async () => {
    try {
      setIsLoading(true);
      const locationData = (await locationService.getLocationById(
        id
      )) as Location;
      const photosWithFiles = locationData.images;

      // Group tags by their type field (TagCategory)
      const tagsByCategory: Partial<Record<TagType, string[]>> = {};
      locationData.tags.forEach(tag => {
        // Map API tag.type to TagCategory enum
        const category = tag.type as TagType;
        if (Object.values(TagType).includes(category)) {
          if (!tagsByCategory[category]) {
            tagsByCategory[category] = [];
          }
          tagsByCategory[category]!.push(tag.id);
        }
      });

      setFormData((prev: LocationRequest) => ({
        ...prev,
        ...locationData,
        placeId: locationData.placeId,
        city: locationData.city,
        state: locationData.state,
        country: locationData.country,
        postalCode: locationData.postalCode,
        size: locationData.size || '',
        tags: tagsByCategory,
        latitude: Number(locationData.latitude),
        longitude: Number(locationData.longitude),
        images: photosWithFiles as unknown as Photo[],
      }));
      document.title = `${locationData.title} | Scoutr`;
    } finally {
      setIsLoading(false);
    }
  }, [id, locationService]);

  useEffect(() => {
    if (!id) return;
    fetchLocationData();
  }, [fetchLocationData, id]);

  return (
    <div className="flex flex-col h-full">
      {isLoading && <Loading />}
      {!isLoading && (
        <>
          <div className="flex flex-1 flex-col items-center pb-20 py-4 sm:py-6 overflow-y-scroll">
            <div className="max-w-282 w-full">
              {/* Breadcrumb */}
              <div className="mb-8 py-4">
                <Breadcrumb>
                  <BreadcrumbList>
                    <BreadcrumbItem>
                      <BreadcrumbLink
                        href={Routes.ADMIN_LOCATIONS}
                        className="text-primary-200 hover:text-primary-300"
                      >
                        Locations
                      </BreadcrumbLink>
                    </BreadcrumbItem>
                    <BreadcrumbSeparator>
                      <ChevronRight className="h-4 w-4 text-primary-400" />
                    </BreadcrumbSeparator>
                    <BreadcrumbItem>
                      <BreadcrumbPage className="text-primary-400">
                        Edit location
                      </BreadcrumbPage>
                    </BreadcrumbItem>
                  </BreadcrumbList>
                </Breadcrumb>
              </div>

              <div className="w-full mx-auto">
                <div className="space-y-8">
                  <div className="w-full max-w-152 mx-auto flex flex-col gap-8">
                    {/* Header */}
                    <div className="space-y-6">
                      <div>
                        <Heading level={3} className="leading-9">
                          Edit location
                        </Heading>
                        <Body className="mt-3 leading-7">
                          Update the details, tags, or images for this location.
                        </Body>
                      </div>

                      {/* Location details section */}
                      <div className="space-y-6">
                        <Heading level={4} className="leading-9">
                          Location details
                        </Heading>
                        <BasicDetailsForm
                          data={formData}
                          onChange={handleChange}
                        />
                      </div>

                      {/* Tags section */}
                      <div className="space-y-6">
                        <Heading level={4} className="leading-9">
                          Location tags
                        </Heading>
                        <Body className="leading-7">
                          Select the tags that capture the look and feel of the
                          location. These tags make it easier for scouts and
                          prod. admins to discover the right fit.
                        </Body>
                        <TagsForm
                          data={formData}
                          onChange={handleChange}
                          tagsByType={tagsByType}
                        />
                      </div>
                    </div>
                  </div>

                  {/* Images section */}
                  <div
                    className={`w-full mx-auto flex flex-col gap-8 max-w-152`}
                  >
                    <div className="w-full max-w-152 mx-auto">
                      <Heading level={4} className="leading-9">
                        Location images
                      </Heading>
                    </div>
                    <ImagesForm
                      data={formData.images}
                      onChange={handleImagesChange}
                      onUploadStatusChange={setHasUploadingFiles}
                      gridCol={2}
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="bg-white z-50 h-22 px-4 sm:px-0 mr-2 border-t flex justify-center items-center shadow-sm">
            <div
              className={`w-full flex items-center justify-between max-w-152`}
            >
              <Button
                variant="outline"
                onClick={() => router.push(Routes.ADMIN_LOCATIONS)}
                className="px-7 py-3"
              >
                Cancel
              </Button>
              <Button
                onClick={handleEditLocation}
                disabled={
                  validateForm(formData) || isSaving || hasUploadingFiles
                }
                className="px-7 py-3"
              >
                {isSaving ? (
                  <>
                    Saving ... <Loader2 className="w-4 h-4 animate-spin" />
                  </>
                ) : (
                  'Save changes'
                )}
              </Button>
            </div>
          </div>
        </>
      )}
    </div>
  );
}
