'use client';

import { Skeleton } from '@/components/ui/skeleton';

export default function Loading() {
  return (
    <div className="flex flex-col items-center pb-20 py-4 sm:py-6">
      <div className="w-full sm:w-[unset] sm:max-w-4xl space-y-8">
        {/* Back link */}
        <div className="inline-flex items-center gap-2 w-40">
          <Skeleton className="h-4 w-4 rounded" />
          <Skeleton className="h-4 w-36" />
        </div>

        {/* Step indicator */}
        <div className="flex items-center justify-center space-x-4">
          {Array.from({ length: 3 }).map((_, i) => (
            <div key={i} className="flex items-center">
              <Skeleton className="h-8 w-8 rounded-full" />
              {i < 2 && <Skeleton className="h-0.5 w-8 mx-2" />}
            </div>
          ))}
        </div>

        <div className="w-full sm:w-[42rem] flex flex-col gap-8">
          <div className="space-y-6">
            {/* Title & description */}
            <div className="space-y-2">
              <Skeleton className="h-8 w-56" />
              <Skeleton className="h-4 w-80" />
            </div>

            {/* Basic details form */}
            <div className="space-y-4">
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-24 w-full" />
            </div>
          </div>
        </div>

        {/* Sticky footer actions */}
        <div className="bg-white z-50 fixed left-0 py-5 px-4 sm:px-0 right-0 bottom-0 border-t flex justify-center items-center">
          <div className="w-[42rem] flex items-center justify-between">
            <Skeleton className="h-10 w-24" />
            <Skeleton className="h-10 w-32" />
          </div>
        </div>
      </div>
    </div>
  );
}
