/**
 * Common TypeScript types and utilities
 * Shared type definitions used across the application
 */

/**
 * Generic API response wrapper
 */
export interface ApiResponse<T> {
  data: T;
  meta?: {
    totalPages?: number;
    totalItems?: number;
    page?: number;
    limit?: number;
  };
}

/**
 * Pagination parameters
 */
export interface PaginationParams {
  page?: number;
  limit?: number;
}

/**
 * Filter parameters
 */
export interface FilterParams {
  search?: string;
  [key: string]: unknown;
}

/**
 * Standard error response structure
 */
export interface ApiErrorResponse {
  message: string;
  statusCode?: number;
  code?: string;
  [key: string]: unknown;
}

/**
 * Loading state
 */
export type LoadingState = 'idle' | 'loading' | 'success' | 'error';

/**
 * Async function result wrapper
 */
export type AsyncResult<T> = Promise<{
  data?: T;
  error?: Error;
}>;
