import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Routes } from '@/lib/routes';

export default async function AccountDeleted() {
  return (
    <div className="flex items-center justify-center min-h-[calc(100vh-4rem)] w-full">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl font-bold">Account deleted</CardTitle>
          <CardDescription className="text-muted-foreground">
            Your account has been deleted. Please contact the administrator to
            continue.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-col space-y-2">
            <Button asChild className="w-full">
              <Link
                href={Routes.SIGN_IN}
                className="flex items-center justify-center space-x-2"
              >
                <span>Go to Sign In</span>
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
