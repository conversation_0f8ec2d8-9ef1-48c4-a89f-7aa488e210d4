'use client';

import * as React from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import SimpleMultiSelect from '@/components/Select/SimpleMultiSelect';
import { FormEvent, useCallback, useEffect, useState } from 'react';
import { useTagService } from '@/hooks/use-services';
import { TagOption } from '@/types/tag';
import { toastError } from '@/lib/toast';

interface BulkAddTagsModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSave: (tags: TagOption[]) => void;
}

export default function BulkAddTagsModal({
  open,
  onOpenChange,
  onSave,
}: BulkAddTagsModalProps) {
  const tagService = useTagService();
  const [tagIds, setTagIds] = useState<string[]>([]);
  const [tagOption, setTagOption] = useState<
    {
      value: string;
      label: string;
      color?: string;
    }[]
  >([]);

  useEffect(() => {
    if (!open) {
      setTagIds([]);
    }
  }, [open]);

  const handleSubmit = (e: FormEvent) => {
    e.preventDefault();
    if (!tagIds.length) return;
    const result = tagIds.map(tagId => {
      const tag = tagOption.find(tag => tag.value === tagId);
      return tag
        ? { value: tag.value, label: tag.label, color: tag.color }
        : ({} as TagOption);
    });
    onSave(result);
    onOpenChange(false);
  };

  const fetchStyleTags = useCallback(async () => {
    try {
      const styles = await tagService.getAllTags({ type: 'style' });
      setTagOption(
        styles.map(style => ({
          value: style.id,
          label: style.name,
          color: style.color,
        }))
      );
    } catch {
      toastError('Failed to get tag styles');
    }
  }, [tagService]);

  useEffect(() => {
    fetchStyleTags();
  }, [fetchStyleTags]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className="sm:max-w-[20.5625rem]"
        onInteractOutside={e => e.preventDefault()}
        showCloseButton={false}
      >
        <DialogHeader>
          <DialogTitle>Bulk add tags</DialogTitle>
          <DialogDescription className="text-sm">
            Add tags to multiple locations.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label
              htmlFor="type"
              className="text-sm font-medium text-foreground"
            >
              Tags
            </Label>
            <SimpleMultiSelect
              options={tagOption}
              value={tagIds}
              onChange={setTagIds}
              placeholder="Select tags"
              groupLabel="Tags"
              className="w-full sm:w-[17.4375rem]"
            />
          </div>

          <DialogFooter className="mt-6">
            <Button
              type="button"
              variant="ghost"
              onClick={() => onOpenChange(false)}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={tagIds.length === 0}>
              Select tags
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
