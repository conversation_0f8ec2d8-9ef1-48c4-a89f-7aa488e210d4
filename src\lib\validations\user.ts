import { z } from 'zod';
import { Role, UserStatus } from '@/types/enum';

/**
 * Validation schema for creating a user (invite)
 */
export const createUserSchema = z.object({
  name: z
    .string()
    .min(1, 'The user name is required')
    .trim()
    .max(100, 'Name must be less than 100 characters'),
  email: z
    .string()
    .min(1, 'The email is required')
    .email('Please enter a valid email address')
    .trim()
    .toLowerCase(),
  role: z.nativeEnum(Role, {
    message: 'The role is required',
  }),
  productionHouseId: z.string().optional(),
});

/**
 * Validation schema for updating a user
 */
export const updateUserSchema = z.object({
  name: z
    .string()
    .min(1, 'The user name is required')
    .trim()
    .max(100, 'Name must be less than 100 characters'),
  role: z.nativeEnum(Role, {
    message: 'The role is required',
  }),
  status: z.nativeEnum(UserStatus, {
    message: 'The status is required',
  }),
  productionHouseId: z.string().optional(),
});

export type CreateUserFormData = z.infer<typeof createUserSchema>;
export type UpdateUserFormData = z.infer<typeof updateUserSchema>;
