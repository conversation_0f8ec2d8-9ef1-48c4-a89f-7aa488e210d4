'use client';

import { Skeleton } from '@/components/ui/skeleton';
import { TableCell, TableRow } from '@/components/ui/table';

export default function TagsTableRowLoading() {
  return (
    <>
      {Array.from({ length: 6 }).map((_, i) => (
        <TableRow className="h-20" key={`s-${i}`}>
          <TableCell className="px-6">
            <div className="flex items-center gap-4">
              <Skeleton className="h-4 w-4" />
              <Skeleton className="h-4 w-32" />
            </div>
          </TableCell>
          <TableCell className="px-6">
            <Skeleton className="h-4 w-48" />
          </TableCell>
          <TableCell className="px-6">
            <Skeleton className="h-4 w-48" />
          </TableCell>
          <TableCell>
            <div className="flex items-center justify-end gap-2">
              <Skeleton className="h-4 w-10" />
              <Skeleton className="h-4 w-10" />
            </div>
          </TableCell>
        </TableRow>
      ))}
    </>
  );
}
