import { DialogFooter } from '../ui/dialog';
import { User } from 'next-auth';
import { CommentInput } from './CommentInput';

interface CommentModalFooterProps {
  user: User | null;
  isCreating: boolean;
  onComment: (comment: string) => void;
  onCancel: () => void;
}

export const CommentModalFooter = ({
  user,
  isCreating,
  onComment,
  onCancel,
}: CommentModalFooterProps) => {
  return (
    <DialogFooter className="p-4 border-t">
      <CommentInput
        user={user}
        isCreating={isCreating}
        onComment={onComment}
        onCancel={onCancel}
        showCancelButton={true}
      />
    </DialogFooter>
  );
};
