import { getSession, signOut } from 'next-auth/react';
import { extractErrorMessage } from './error-handler';
import { Routes } from './routes';

// Global refresh promise to prevent concurrent token refreshes across all instances
let globalRefreshPromise: Promise<boolean> | null = null;

// HTTP Client with automatic access token injection and token refresh
export class HttpClient {
  private baseUrl: string;
  private defaultHeaders: HeadersInit;

  constructor(baseUrl?: string) {
    this.baseUrl =
      baseUrl ||
      process.env.NEXT_PUBLIC_NESTJS_API_URL ||
      'http://localhost:3001';
    this.defaultHeaders = {
      'Content-Type': 'application/json',
    };
  }

  /**
   * Get the current access token from the session
   */
  private async getAccessToken(): Promise<string | null> {
    try {
      const session = await getSession();
      return session?.accessToken || null;
    } catch (error) {
      console.error('Failed to get access token:', error);
      return null;
    }
  }

  /**
   * Refresh access token using refresh token from auth service
   * Uses global lock to prevent concurrent refreshes across all requests
   */
  private async refreshSession(): Promise<boolean> {
    // If a refresh is already in progress, wait for it
    if (globalRefreshPromise) {
      if (process.env.NODE_ENV === 'development') {
        console.log(
          '⏳ Token refresh already in progress, waiting for completion...'
        );
      }
      return globalRefreshPromise;
    }

    // Create a new refresh promise
    if (process.env.NODE_ENV === 'development') {
      console.log('🔄 Starting new token refresh from HTTP client...');
    }

    globalRefreshPromise = this.performRefresh();

    try {
      const result = await globalRefreshPromise;
      if (process.env.NODE_ENV === 'development') {
        console.log(
          result ? '✅ Token refresh successful' : '❌ Token refresh failed'
        );
      }
      return result;
    } finally {
      // Clear the global promise after completion
      globalRefreshPromise = null;
    }
  }

  /**
   * Perform the actual token refresh
   */
  private async performRefresh(): Promise<boolean> {
    try {
      // Get current session - this will trigger NextAuth's JWT callback
      // which handles the token refresh logic
      const session = await getSession();

      // Don't attempt refresh if session has error or no refresh token
      if (
        !session?.refreshToken ||
        session.error === 'RefreshAccessTokenError'
      ) {
        if (process.env.NODE_ENV === 'development') {
          console.log(
            '❌ Cannot refresh: no refresh token or session has error'
          );
        }
        return false;
      }

      // The getSession() call above will trigger the JWT callback in auth.ts
      // which has its own global lock and handles the actual token refresh
      // We just need to wait a bit and verify the session is still valid
      await new Promise(resolve => setTimeout(resolve, 200));

      // Verify the session is valid after refresh attempt
      const refreshedSession = await getSession();

      if (
        !refreshedSession ||
        refreshedSession.error === 'RefreshAccessTokenError'
      ) {
        if (process.env.NODE_ENV === 'development') {
          console.log('❌ Session refresh failed or has error');
        }
        return false;
      }

      return true;
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('❌ Failed to refresh session:', error);
      }
      return false;
    }
  }

  /**
   * Create headers with automatic access token injection
   */
  private async createHeaders(
    customHeaders?: HeadersInit
  ): Promise<HeadersInit> {
    const accessToken = await this.getAccessToken();

    const headers: HeadersInit = {
      ...this.defaultHeaders,
      ...customHeaders,
    };

    // Automatically add Authorization header if access token is available
    if (accessToken) {
      (headers as Record<string, string>).Authorization =
        `Bearer ${accessToken}`;
    }

    return headers;
  }

  /**
   * Make an HTTP request with automatic token injection and retry on 401
   */
  private async request<T>(
    endpoint: string,
    options: RequestInit = {},
    isRetry = false,
    skipTokenRefresh = false
  ): Promise<T> {
    // Define custom error types locally to satisfy the "rewrite selection" constraint.
    // In a real application, these should ideally be defined globally or in a separate file and imported.
    interface HttpResponseErrorResponse {
      status: number;
      statusText: string;
      data: unknown;
      headers: Record<string, string>;
      url: string;
    }

    class HttpClientError extends Error {
      status?: number;
      response?: HttpResponseErrorResponse | null;

      constructor(
        message: string,
        status?: number,
        response?: HttpResponseErrorResponse | null
      ) {
        // Extract message from error response if available
        const errorMessage =
          response?.data &&
          typeof response.data === 'object' &&
          'message' in response.data &&
          typeof response.data.message === 'string'
            ? response.data.message
            : message;

        super(errorMessage);
        this.name = 'HttpClientError';
        this.status = status;
        this.response = response;
        // Set the prototype explicitly to ensure `instanceof` works correctly
        Object.setPrototypeOf(this, HttpClientError.prototype);
      }

      /**
       * Get the error message from the API response
       * Falls back to the error message if not available
       */
      getMessage(): string {
        if (
          this.response?.data &&
          typeof this.response.data === 'object' &&
          'message' in this.response.data &&
          typeof this.response.data.message === 'string'
        ) {
          return this.response.data.message;
        }
        return this.message;
      }

      /**
       * Get the full error data from the API response
       */
      getErrorData(): unknown {
        return this.response?.data || null;
      }
    }

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

    try {
      const headers = await this.createHeaders(options.headers);

      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        ...options,
        headers,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        // Try to parse the error body (JSON or text)
        let errorBody = null;
        try {
          const contentType = response.headers.get('content-type');
          if (contentType && contentType.includes('application/json')) {
            errorBody = await response.json();
          } else {
            errorBody = await response.text();
          }
        } catch {
          errorBody = null;
        }

        const errorResponse: HttpResponseErrorResponse = {
          status: response.status,
          statusText: response.statusText,
          data: errorBody,
          headers: Object.fromEntries(response.headers.entries()),
          url: `${this.baseUrl}${endpoint}`,
        };

        // User had been deleted - redirect first, then don't throw error
        if (response.status === 401 && errorBody.message === 'User not found') {
          await signOut({ redirect: false });
          window.location.href = Routes.ACCOUNT_DELETED;
          // Return a pending promise to prevent further client-side execution as a redirect is imminent
          return new Promise<T>(() => {});
        }

        // Handle 401 Unauthorized - token might be expired
        // Skip token refresh for logout endpoint or if explicitly disabled
        if (response.status === 401 && !isRetry && !skipTokenRefresh) {
          // Check if we have a valid session before attempting refresh
          const currentSession = await getSession();

          // Don't attempt refresh if no session or session has error
          if (
            !currentSession ||
            currentSession.error === 'RefreshAccessTokenError'
          ) {
            throw new HttpClientError(
              `Unauthorized: ${response.statusText}`,
              response.status,
              errorResponse
            );
          }

          if (process.env.NODE_ENV === 'development') {
            console.warn(
              '🔒 Request failed with 401 - attempting token refresh'
            );
          }

          // Attempt to refresh the session (with global lock)
          const refreshed = await this.refreshSession();

          if (refreshed) {
            if (process.env.NODE_ENV === 'development') {
              console.log('🔄 Retrying request with refreshed token');
            }
            // Retry the request once with the new token
            return this.request<T>(endpoint, options, true, skipTokenRefresh);
          } else {
            if (process.env.NODE_ENV === 'development') {
              console.error('❌ Token refresh failed, request will fail');
            }

            throw new HttpClientError(
              'Token refresh failed',
              response.status,
              errorResponse
            );
          }
        }

        // No permission - check if role mismatch and redirect
        if (response.status === 403 && errorBody.role) {
          await signOut({ redirect: false });
          const session = await getSession();
          const userRole = session?.user?.role;
          if (errorBody.role !== userRole) {
            window.location.href = Routes.NO_PERMISSION;
            // Return a pending promise to prevent further client-side execution as a redirect is imminent
            return new Promise<T>(() => {});
          }
        }

        throw new HttpClientError(
          `HTTP ${response.status}: ${response.statusText}`,
          response.status,
          errorResponse
        );
      }

      // Handle empty responses (like 204 No Content)
      if (
        response.status === 204 ||
        response.headers.get('content-length') === '0'
      ) {
        return {} as T;
      }

      return response.json();
    } catch (error) {
      clearTimeout(timeoutId);

      // If the error is already an HttpClientError, rethrow it.
      // This covers cases where HttpClientError was thrown earlier in the try block.
      if (error instanceof HttpClientError) {
        throw error;
      }

      // Handle request timeout (AbortError)
      if (error instanceof Error && error.name === 'AbortError') {
        throw new HttpClientError(
          'Request timeout - please try again',
          undefined, // No specific HTTP status for timeout
          null // No HTTP response object for timeout
        );
      }

      // Handle other generic fetch/network errors that are not HttpClientError
      // and don't have a specific response object.
      if (error instanceof Error) {
        throw new HttpClientError(
          error.message || 'Network error',
          undefined, // No specific HTTP status for network error
          null // No HTTP response object for network error
        );
      }

      // Fallback for truly unknown errors (should be rare with proper typing)
      throw new HttpClientError('An unknown error occurred', undefined, null);
    }
  }

  /**
   * GET request
   */
  async get<T>(endpoint: string, headers?: HeadersInit): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'GET',
      headers,
    });
  }

  /**
   * POST request
   */
  async post<T>(
    endpoint: string,
    data?: unknown,
    headers?: HeadersInit
  ): Promise<T> {
    // Skip token refresh for logout endpoint
    const skipTokenRefresh = endpoint.includes('/auth/logout');

    return this.request<T>(
      endpoint,
      {
        method: 'POST',
        headers,
        body: data ? JSON.stringify(data) : undefined,
      },
      false,
      skipTokenRefresh
    );
  }

  /**
   * PUT request
   */
  async put<T>(
    endpoint: string,
    data?: unknown,
    headers?: HeadersInit
  ): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      headers,
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  /**
   * PATCH request
   */
  async patch<T>(
    endpoint: string,
    data?: unknown,
    headers?: HeadersInit
  ): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'PATCH',
      headers,
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  /**
   * DELETE request
   */
  async delete<T>(
    endpoint: string,
    data?: unknown,
    headers?: HeadersInit
  ): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'DELETE',
      headers,
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  /**
   * Upload file with automatic token injection and retry on 401
   */
  async uploadFile<T>(
    endpoint: string,
    file: File,
    additionalData?: Record<string, unknown>,
    headers?: HeadersInit,
    isRetry = false
  ): Promise<T> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout for file uploads

    try {
      const accessToken = await this.getAccessToken();

      const formData = new FormData();
      formData.append('file', file);

      // Add additional data to form
      if (additionalData) {
        Object.entries(additionalData).forEach(([key, value]) => {
          formData.append(key, String(value));
        });
      }

      const requestHeaders: HeadersInit = {
        ...headers,
      };

      // Add Authorization header if access token is available
      if (accessToken) {
        (requestHeaders as Record<string, string>).Authorization =
          `Bearer ${accessToken}`;
      }

      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        method: 'POST',
        headers: requestHeaders,
        body: formData,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        // Handle 401 Unauthorized - token might be expired
        if (response.status === 401 && !isRetry) {
          if (process.env.NODE_ENV === 'development') {
            console.warn(
              '🔒 Upload failed with 401 - attempting token refresh'
            );
          }

          // Attempt to refresh the session (with global lock)
          const refreshed = await this.refreshSession();

          if (refreshed) {
            if (process.env.NODE_ENV === 'development') {
              console.log('🔄 Retrying upload with refreshed token');
            }
            // Retry the upload once with the new token
            return this.uploadFile<T>(
              endpoint,
              file,
              additionalData,
              headers,
              true
            );
          } else {
            if (process.env.NODE_ENV === 'development') {
              console.error('❌ Token refresh failed, upload will fail');
            }
          }
        }

        const errorText = await response.text();
        const rawError = new Error(`HTTP ${response.status}: ${errorText}`);
        const userFriendlyMessage = extractErrorMessage(rawError);
        throw new Error(userFriendlyMessage);
      }

      return response.json();
    } catch (error) {
      clearTimeout(timeoutId);

      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('Upload timeout - please try again');
      }

      const userFriendlyMessage = extractErrorMessage(error);
      throw new Error(userFriendlyMessage);
    }
  }
}

// Create singleton instance
export const httpClient = new HttpClient();

// Export individual methods for convenience
export const get = <T>(endpoint: string, headers?: HeadersInit) =>
  httpClient.get<T>(endpoint, headers);

export const post = <T>(
  endpoint: string,
  data?: unknown,
  headers?: HeadersInit
) => httpClient.post<T>(endpoint, data, headers);

export const put = <T>(
  endpoint: string,
  data?: unknown,
  headers?: HeadersInit
) => httpClient.put<T>(endpoint, data, headers);

export const patch = <T>(
  endpoint: string,
  data?: unknown,
  headers?: HeadersInit
) => httpClient.patch<T>(endpoint, data, headers);

export const deleteRequest = <T>(endpoint: string, headers?: HeadersInit) =>
  httpClient.delete<T>(endpoint, headers);

export const uploadFile = <T>(
  endpoint: string,
  file: File,
  additionalData?: Record<string, unknown>,
  headers?: HeadersInit
) => httpClient.uploadFile<T>(endpoint, file, additionalData, headers);
