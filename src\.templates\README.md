# Component Templates

This directory contains templates for creating new components, hooks, and forms following the project's standards.

## Available Templates

### 1. `component.tsx.template`

Standard React component template with:

- TypeScript interfaces
- JSDoc comments
- Proper exports

**Usage:**

```bash
# Copy template and rename
cp src/.templates/component.tsx.template src/components/features/myfeature/MyComponent.tsx
```

### 2. `hook.ts.template`

Custom React hook template with:

- TypeScript return types
- Standard hook patterns
- Error handling structure

**Usage:**

```bash
cp src/.templates/hook.ts.template src/hooks/useMyHook.ts
```

### 3. `form-modal.tsx.template`

Form modal template with:

- React Hook Form + Zod integration
- Standardized form structure
- Loading states
- Error handling

**Usage:**

```bash
cp src/.templates/form-modal.tsx.template src/components/features/myfeature/MyFormModal.tsx
```

## Component Structure Standards

### File Organization

```
src/
├── components/
│   ├── features/          # Feature-specific components
│   │   ├── tags/
│   │   ├── locations/
│   │   └── users/
│   ├── shared/            # Shared/reusable components
│   └── ui/                # Base UI components (Shadcn)
├── hooks/
│   ├── api/               # React Query hooks
│   └── use*.ts            # Custom hooks
└── lib/
    ├── validations/       # Zod schemas
    └── query-keys.ts      # React Query keys
```

### Component Structure Order

1. **Imports** (grouped: React, Next.js, UI, Utils, Types)
2. **Types/Interfaces**
3. **Constants**
4. **Component** (default export)
5. **Sub-components** (if any)

### Naming Conventions

- **Components**: PascalCase (`AddTagModal.tsx`)
- **Hooks**: camelCase (`useTags.ts`)
- **Utils**: camelCase (`formatDate.ts`)
- **Types**: PascalCase interfaces/types
- **Constants**: UPPER_SNAKE_CASE (`LIMIT = 6`)

### Form Patterns

- Use React Hook Form + Zod for all forms
- Validation schemas in `src/lib/validations/`
- Form modals follow the `form-modal.tsx.template` structure
- Loading states use `InlineSpinner` from shared components

### Loading States

- Use `LoadingSpinner` for page-level loading
- Use `TableSkeleton` or `TableRowSkeleton` for table loading
- Use `InlineSpinner` for button loading states
- All loading components in `src/components/shared/`
