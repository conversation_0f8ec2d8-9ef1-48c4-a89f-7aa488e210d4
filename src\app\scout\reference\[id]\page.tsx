'use client';

import { use, useCallback, useEffect, useMemo, useRef } from 'react';
import ReferenceListView from './reference-view';
import Loading from './loading';
import { useNavbar } from '@/contexts/navbar-context';
import { SearchIcon, ShareIcon, EditIcon } from 'lucide-react';
import { Location } from '@/types';
import { toastError, toastSuccess } from '@/lib/toast';
import { ProductionHouse } from '@/lib/services/production-service';
import { formatDateRange } from '@/lib/utils';
import { useReferenceService } from '@/hooks/use-services';
import { useReference } from './reference-context';

export default function ReferenceListFullScreenPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = use(params);
  const referenceService = useReferenceService();
  const { setAdditionalItems, clearNavbarItems } = useNavbar();
  const { reference, isLoading, refreshReference } = useReference();
  const search = useRef<string>('');

  const fetchReferenceLists = useCallback(async () => {
    await refreshReference(search.current);
  }, [refreshReference]);

  const handleDeleteLocation = async (data?: Location) => {
    try {
      if (!data?.id || !id) return;
      await referenceService.deleteReferenceItem({
        listId: id,
        itemId: data.id,
      });
      toastSuccess('Location removed successfully.');
      fetchReferenceLists();
    } catch {
      toastError('Failed to delete location');
    }
  };

  const handleUpdateReference = async (projectName: string) => {
    try {
      await referenceService.updateReference({ id, projectName });
      toastSuccess('The reference has been updated successfully');
      // Refresh reference data to get updated name
      await refreshReference(search.current);
    } catch {
      toastError('Failed to update reference');
    }
  };

  const handleSearchChange = (input: string) => {
    search.current = input;
    fetchReferenceLists();
  };

  const locationItems = useMemo(() => {
    return reference
      ? reference?.items.map(item => ({
          ...item.location,
          commentCount: item.commentCount,
          id: item.id, // use item id instead location id to delete item
          images: item.images.map(img => ({
            ...img.image,
            tags: [],
          })),
        }))
      : [];
  }, [reference]);

  // Set navbar items for this page
  useEffect(() => {
    setAdditionalItems([
      {
        id: 'search-locations',
        label: 'Search Locations',
        icon: <SearchIcon className="h-4 w-4" />,
        href: `/scout/reference/${id}/search-locations`,
        variant: 'outline' as const,
      },
      {
        id: 'edit-reference',
        label: 'Edit Reference',
        icon: <EditIcon className="h-4 w-4" />,
        onClick: () => {
          // Handle edit reference
          console.log('Edit reference clicked');
        },
        variant: 'ghost' as const,
      },
      {
        id: 'share-reference',
        label: 'Share',
        icon: <ShareIcon className="h-4 w-4" />,
        onClick: () => {
          // Handle share reference
          console.log('Share reference clicked');
        },
        variant: 'ghost' as const,
      },
    ]);

    // Cleanup navbar items when component unmounts
    return () => {
      clearNavbarItems();
    };
  }, [id, setAdditionalItems, clearNavbarItems]);

  const dateRangeString = useMemo(() => {
    return reference
      ? formatDateRange(
          new Date(reference?.shootDateStart),
          new Date(reference?.shootDateEnd)
        )
      : '';
  }, [reference]);

  return isLoading ? (
    <Loading />
  ) : (
    <ReferenceListView
      reference={reference}
      productionHouse={reference?.productionHouse ?? ({} as ProductionHouse)}
      dateRange={dateRangeString}
      totalReferences={reference?.items?.length ?? 0}
      status={reference?.status ?? ''}
      items={locationItems}
      onDelete={handleDeleteLocation}
      onUpdate={handleUpdateReference}
      searchChange={handleSearchChange}
    />
  );
}
