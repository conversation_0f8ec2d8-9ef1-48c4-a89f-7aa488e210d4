'use client';

import type { Location } from '@/types';

import LocationFeatures from '@/components/features/locations/LocationFeatures';
import { NaturalLightIcon, ParkingIcon, RooftopAccessIcon } from '@/lib/icons';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { getLocationSizeText } from '@/types/constant';

const mockLocationOffers = [
  { icon: ParkingIcon, label: 'Free parking' },
  { icon: NaturalLightIcon, label: 'Abundant natural light' },
  { icon: RooftopAccessIcon, label: 'Rooftop access' },
];

interface LocationDetailCardProps {
  location: Location;
  onSubmit?: () => void;
}

export function LocationDetailCard({
  location,
  onSubmit,
}: LocationDetailCardProps) {
  const handleApproveLocation = () => {
    if (onSubmit) {
      onSubmit();
    }
  };

  return (
    <Card className="w-full py-4">
      <CardContent className="space-y-5 text-sm text-gray-700">
        {/* Top primary action */}
        <div className="pt-1">
          <Button onClick={handleApproveLocation} className="w-full">
            Approve Location
          </Button>
        </div>

        <div className="space-y-5">
          <div className="space-y-1">
            <p className="font-bold">Location details</p>
            <div className="mt-2 grid grid-cols-2 gap-y-4 text-xs sm:text-sm">
              <span>Building type</span>
              <span className="text-right">Residential</span>
              <span>Structure type</span>
              <span className="text-right">Apartment</span>
              <span>Style</span>
              <span className="text-right">Modern</span>
              <span>Size</span>
              <span className="text-right">
                {getLocationSizeText(location.size)}
              </span>
            </div>
          </div>

          <div className="border-t pt-4">
            <p className="mb-7 font-bold">Location features</p>
            <LocationFeatures data={mockLocationOffers} />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
