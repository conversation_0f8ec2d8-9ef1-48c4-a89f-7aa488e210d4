'use client';
import React, { useState, useEffect, useRef } from 'react';
import { Map, Marker, useMap, MapMouseEvent } from '@vis.gl/react-google-maps';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import LocationAutocomplete from '@/components/features/locations/LocationAutoComplete';
import { Button } from '@/components/ui/button';

interface LocationPickerModalProps {
  open: boolean;
  defaultAddress?: {
    lat?: number;
    lng?: number;
    address?: string;
  } | null;
  onOpenChange: (open: boolean) => void;
  onChange: (
    data: { lat: number; lng: number; address?: string } | null
  ) => void;
}

const defaultCenter = { lat: 34.0522, lng: -118.2437 };
const defaultZoom = 12;

// Component to handle map panning without resetting zoom
function MapController({ center }: { center: { lat: number; lng: number } }) {
  const map = useMap();
  const previousCenter = useRef(center);

  useEffect(() => {
    if (!map) return;

    // Only pan if the center actually changed
    if (
      previousCenter.current.lat !== center.lat ||
      previousCenter.current.lng !== center.lng
    ) {
      map.panTo(center);
      previousCenter.current = center;
    }
  }, [map, center]);

  return null;
}

export default function LocationPickerModal({
  open,
  defaultAddress,
  onOpenChange,
  onChange,
}: LocationPickerModalProps) {
  const [selected, setSelected] = useState<{
    lat?: number;
    lng?: number;
    address?: string;
  } | null>(defaultAddress ?? null);

  const [mapCenter, setMapCenter] = useState(
    defaultAddress?.lat && defaultAddress?.lng
      ? { lat: defaultAddress.lat, lng: defaultAddress.lng }
      : defaultCenter
  );

  const handleMapClick = (e: MapMouseEvent) => {
    if (!e.detail?.latLng) return;
    const lat = e.detail.latLng.lat;
    const lng = e.detail.latLng.lng;

    // Immediately set coordinates and show marker
    const coordsWithoutAddress = { lat, lng, address: undefined };
    setSelected(coordsWithoutAddress);
    // Don't change map center - stay at current zoom and position

    // Then fetch address asynchronously
    (async () => {
      try {
        const geocoder = new google.maps.Geocoder();
        const res = await geocoder.geocode({ location: { lat, lng } });
        const address = res.results[0]?.formatted_address;
        const coords = { lat, lng, address };
        setSelected(coords);
        onChange(coords);
      } catch (err) {
        console.error('Geocoder failed:', err);
        // Even if geocoding fails, still call onChange with coordinates
        onChange(coordsWithoutAddress);
      }
    })();
  };

  useEffect(() => {
    if (open) {
      setSelected(defaultAddress ?? null);
      if (defaultAddress?.lat && defaultAddress?.lng) {
        setMapCenter({ lat: defaultAddress.lat, lng: defaultAddress.lng });
      }
    }
  }, [defaultAddress, open]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className="md:max-w-3xl lg:max-w-4xl xl:max-w-5xl"
        onInteractOutside={e => {
          // Prevent closing modal when clicking on Google autocomplete dropdown
          const target = e.target as HTMLElement;
          if (target.closest('.pac-container')) {
            e.preventDefault();
          }
        }}
      >
        <DialogHeader>
          <DialogTitle>Choose address</DialogTitle>
        </DialogHeader>
        <div className="flex flex-col gap-2">
          <div>
            <LocationAutocomplete
              value={selected?.address || ''}
              onSelect={place => {
                console.log('place', place);
                if (place) {
                  const lat = place.geometry?.location?.lat();
                  const lng = place.geometry?.location?.lng();
                  const address = place.formatted_address || place.name;
                  if (typeof lat === 'number' && typeof lng === 'number') {
                    const coords = { lat, lng, address };
                    setSelected(coords);
                    onChange(coords);
                    // Pan to the new location without resetting zoom
                    setMapCenter({ lat, lng });
                  }
                } else {
                  setSelected(null);
                }
              }}
              placeholder="Search address"
            />
          </div>

          <div className="h-[65vh] w-full z-0">
            <Map
              style={{ width: '100%', height: '100%' }}
              defaultCenter={mapCenter}
              defaultZoom={defaultZoom}
              onClick={handleMapClick}
              gestureHandling="greedy"
              disableDefaultUI={false}
              clickableIcons={true}
            >
              <MapController center={mapCenter} />
              {selected &&
                typeof selected.lat === 'number' &&
                typeof selected.lng === 'number' && (
                  <Marker position={{ lat: selected.lat, lng: selected.lng }} />
                )}
            </Map>
          </div>
        </div>
        <DialogFooter className="sm:justify-end">
          <Button
            type="button"
            variant="outline"
            onClick={() => onOpenChange(false)}
          >
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
