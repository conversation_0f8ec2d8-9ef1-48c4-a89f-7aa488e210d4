'use client';

import { useEffect, useState, useCallback } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Location } from '@/types';
import { toastError } from '@/lib/toast';
import { useLocationService } from '@/hooks/use-services';
import LocationDetail from '@/components/features/locations/LocationDetail';

export default function LocationPublicPage() {
  const params = useParams<{ id: string }>();
  const router = useRouter();
  const locationService = useLocationService();
  const locationId = params?.id;

  const [location, setLocation] = useState<Location | undefined>();
  const [loading, setLoading] = useState<boolean>(true);

  const fetchLocation = useCallback(async () => {
    if (!locationId) return;
    try {
      setLoading(true);
      const response = await locationService.getLocationById(locationId);
      setLocation(response);
    } catch {
      toastError('Failed to get location');
    } finally {
      setLoading(false);
    }
  }, [locationId, locationService]);

  useEffect(() => {
    fetchLocation();
  }, [fetchLocation]);

  const handleSeeAllImages = () => {
    router.push(`/location/${locationId}/all-images`);
  };

  const handleSubmit = () => {
    router.back();
  };

  return (
    <LocationDetail
      location={location}
      loading={loading}
      onSeeAllPhotos={handleSeeAllImages}
      onSubmit={handleSubmit}
    />
  );
}
