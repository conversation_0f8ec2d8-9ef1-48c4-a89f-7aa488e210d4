'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Typography } from '@/components/ui/typography';
import { formatDateRange, getDisplayText } from '@/lib/utils';
import { Reference } from '@/types';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { Eye } from 'lucide-react';
import { MessageIcon } from '@/lib/icons';
import { CommentModal } from '@/components/comment/CommentModal';

interface ProjectCardProps {
  project: Reference;
  onComment?: (projectId: string) => void;
}

export default function ProjectCard({ project, onComment }: ProjectCardProps) {
  const router = useRouter();
  const [isHovered, setIsHovered] = useState(false);
  const [isCommentModalOpen, setIsCommentModalOpen] = useState(false);

  // Both Scout and ProductionAdmin use scout reference routes for now
  // TODO: Update to shared /project/[projectId] route when implemented
  const handleView = () => {
    // Don't navigate if comment modal is open
    if (isCommentModalOpen) return;
    router.push(`/scout/reference/${project.id}`);
  };

  const handleComment = () => {
    if (onComment) {
      onComment(project.id);
    } else {
      setIsCommentModalOpen(true);
    }
  };

  return (
    <>
      <div
        className={`flex flex-col items-start cursor-pointer rounded-tl-[12px] rounded-tr-[12px] rounded-bl-[12px] rounded-br-[12px] overflow-hidden border border-neutral-200 bg-white ${
          isCommentModalOpen ? 'pointer-events-none' : ''
        }`}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        onClick={handleView}
      >
        {/* Image */}
        <div className="relative w-full h-[192px] bg-neutral-300">
          {project.coverImageUrl ? (
            <Image
              src={project.coverImageUrl}
              alt={project.projectName}
              fill
              className="object-cover"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center bg-gray-200">
              <span className="text-gray-500 text-sm">No Image</span>
            </div>
          )}
          {/* Status Badge */}
          {project.status && (
            <div className="absolute top-3 right-3">
              <Badge variant="secondary" className=" text-neutral-900">
                {getDisplayText(project.status)}
              </Badge>
            </div>
          )}
        </div>

        {/* Content */}
        <div className="border border-neutral-200 border-solid box-border flex flex-col gap-4 items-start p-4 w-full rounded-bl-[12px] rounded-br-[12px]">
          <div className="flex flex-col gap-1 items-start w-full">
            <Typography
              variant="h3"
              className="font-semibold text-base leading-6 text-black"
            >
              {project.projectName}
            </Typography>
            <p className="font-normal text-base leading-6 text-neutral-600">
              {project.productionHouse?.name || 'Unknown Production House'}
            </p>
            <p className="font-normal text-sm leading-5 text-neutral-500 h-5">
              {formatDateRange(
                new Date(project.shootDateStart),
                new Date(project.shootDateEnd)
              )}
            </p>
          </div>

          {/* Action Buttons - Show on hover, but always reserve space */}
          <div
            className={`flex gap-2 items-center w-full transition-opacity duration-200 ${
              isHovered ? 'opacity-100' : 'opacity-0 pointer-events-none'
            }`}
            style={{ minHeight: '36px' }}
          >
            <Button
              variant="outline"
              className="flex-1 bg-[rgba(229,231,235,0.8)] hover:bg-[rgba(229,231,235,0.9)] border-0 h-9 px-4 py-2 rounded-md"
              onClick={e => {
                e.stopPropagation();
                handleComment();
              }}
            >
              <MessageIcon className="h-4 w-4 mr-1 text-primary" />
              <span className="text-sm font-medium text-gray-900">Comment</span>
            </Button>
            <Button
              variant="outline"
              className="flex-1 bg-[rgba(229,231,235,0.8)] hover:bg-[rgba(229,231,235,0.9)] border-0 h-9 px-4 py-2 rounded-md"
              onClick={e => {
                e.stopPropagation();
                handleView();
              }}
            >
              <Eye className="h-4 w-4 mr-1 text-primary" />
              <span className="text-sm font-medium text-gray-900">View</span>
            </Button>
          </div>
        </div>
      </div>
      <CommentModal
        open={isCommentModalOpen}
        onOpenChange={setIsCommentModalOpen}
        reference={project}
      />
    </>
  );
}
