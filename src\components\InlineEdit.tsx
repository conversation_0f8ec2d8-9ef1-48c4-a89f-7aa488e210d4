import { useState } from 'react';
import { PencilIcon } from '@/lib/icons';
import { Input } from './ui/input';

interface InlineEditProps {
  value: string;
  onSave: (text: string) => void;
}

export default function InlineEdit({ onSave, value }: InlineEditProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [title, setTitle] = useState(value);

  // Handle saving on blur
  const handleBlur = () => {
    onSave(title);
    setIsEditing(false);
  };
  // Handle saving on "Enter" key press
  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === 'Enter') {
      onSave(title);
      setIsEditing(false);
    }
  };

  return (
    <div className="flex items-center space-x-2">
      {isEditing ? (
        <Input
          type="text"
          value={title}
          onChange={e => setTitle(e.target.value)}
          onBlur={handleBlur}
          onKeyDown={handleKeyDown}
          autoFocus // Automatically focuses the input when it appears
          className="border rounded px-2 py-1 !text-2xl font-bold"
        />
      ) : (
        <div className="flex items-center space-x-2 group">
          <h1 className="text-2xl font-bold">{title}</h1>
          <button
            onClick={() => setIsEditing(true)}
            className="p-1 rounded opacity-0 group-hover:opacity-100 transition-opacity"
            aria-label="Edit title"
          >
            <PencilIcon className="cursor-pointer" />
          </button>
        </div>
      )}
    </div>
  );
}
