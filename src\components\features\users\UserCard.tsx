import { cn } from '@/lib/utils';
import { User } from '@/types/user';
import { getRoleDisplayName } from '@/lib/utils';
import { Card } from '@/components/ui/card';
import { Heading } from '@/components/ui/typography';

interface UserCardProps {
  user: User;
  className?: string;
  onClick?: () => void;
}

export default function UserCard({ user, className, onClick }: UserCardProps) {
  const fullName = `${user.firstName} ${user.lastName}`.trim();
  const roleDisplayName = getRoleDisplayName(user.role);

  return (
    <Card
      className={cn(
        'bg-popover border border-slate-200 flex flex-col gap-1 items-start p-4 rounded-[calc(var(--radius)-2px)]',
        'hover:shadow-lg transition-shadow',
        onClick && 'cursor-pointer',
        className
      )}
      onClick={onClick}
    >
      <Heading level={4} className="text-sm leading-5 line-clamp-2">
        {fullName}
      </Heading>
      <p className="font-normal leading-5 text-sm text-neutral-500">
        {roleDisplayName}
      </p>
    </Card>
  );
}
