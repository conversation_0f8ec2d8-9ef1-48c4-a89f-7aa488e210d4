'use client';

import { ScrollArea } from '@/components/ui/scroll-area';
import SidebarFilter from './sidebar-filter';
import {
  SearchLocationProvider,
  useSearchLocationPanel,
} from './search-location-context';
import ReferenceListPanel from './ReferenceListPanel';

interface PageLayoutProps {
  children: React.ReactNode;
}

function LayoutContent({ children }: PageLayoutProps) {
  const { isPanelOpen } = useSearchLocationPanel();

  return (
    <div className="flex h-full w-full">
      <SidebarFilter />

      <div className="flex flex-1">
        <ScrollArea
          className={`flex-1 px-4 sm:pl-12 sm:pr-20 md:pl-20 py-10 h-[calc(100dvh-var(--navbar-height))] transition-all duration-300`}
        >
          {children}
        </ScrollArea>

        <ScrollArea
          className={`h-[calc(100dvh-var(--navbar-height))] transition-all duration-300 max-w-[29rem] ${isPanelOpen ? 'w-full' : 'w-20'}`}
        >
          <ReferenceListPanel isPanelOpen={isPanelOpen} />
        </ScrollArea>
      </div>
    </div>
  );
}

export default function ScoutSearchLocationsLayout({
  children,
}: PageLayoutProps) {
  return (
    <SearchLocationProvider>
      <LayoutContent>{children}</LayoutContent>
    </SearchLocationProvider>
  );
}
