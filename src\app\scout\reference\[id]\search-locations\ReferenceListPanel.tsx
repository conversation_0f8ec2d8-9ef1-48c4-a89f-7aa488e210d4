'use client';

import ConfirmDialog from '@/components/shared/ConfirmDialog';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Body, Heading } from '@/components/ui/typography';
import { PencilIcon, ShareIcon, TrashIcon } from '@/lib/icons';
import { toastError, toastSuccess } from '@/lib/toast';
import { MessageCircleIcon } from 'lucide-react';
import { ChevronsRight, FullscreenIcon } from 'lucide-react';
import { useState } from 'react';
import { useReference } from '../reference-context';
import { formatDateRange } from '@/lib/utils';
import Image from 'next/image';
import { useReferenceService } from '@/hooks/use-services';
import { ReferenceItem } from '@/types';
import { Loader2 } from 'lucide-react';

interface ReferenceListPanelProps {
  isPanelOpen?: boolean;
}

export default function ReferenceListPanel({
  isPanelOpen = false,
}: ReferenceListPanelProps) {
  const { reference, refreshReference } = useReference();
  const referenceService = useReferenceService();
  const [isRemoveLocationDialogOpen, setIsRemoveLocationDialogOpen] =
    useState(false);
  const [selectedItem, setSelectedItem] = useState<ReferenceItem>();
  const [isDeletingLocation, setIsDeletingLocation] = useState(false);

  const handleDeleteLocation = async (item?: ReferenceItem) => {
    setIsDeletingLocation(true);
    setIsRemoveLocationDialogOpen(false);
    try {
      if (!item?.id || !reference?.id) return;
      await referenceService.deleteReferenceItem({
        listId: reference.id,
        itemId: item.id,
      });
      await refreshReference();
      toastSuccess('Location removed successfully.');
    } catch {
      toastError('Failed to delete location');
    } finally {
      setIsDeletingLocation(false);
    }
  };

  if (!reference) {
    return null;
  }

  return (
    <div
      className={`border-l border-border p-6 pr-16 flex flex-col h-full transition-all duration-300 ${isPanelOpen ? 'opacity-100 bg-card-background' : 'opacity-0 overflow-hidden'}`}
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Heading level={5}>{reference?.projectName}</Heading>
          <PencilIcon className="cursor-pointer" />
        </div>
        <ChevronsRight className="cursor-pointer" />
      </div>
      <div className="flex items-center gap-2 mt-2">
        <Body color="default">
          {reference?.productionHouse?.name} •{' '}
          {formatDateRange(
            new Date(reference?.shootDateStart ?? ''),
            new Date(reference?.shootDateEnd ?? '')
          )}{' '}
          • {reference?.items?.length}{' '}
          {reference?.items?.length === 1 ? 'reference' : 'references'}
        </Body>
      </div>
      <div className="flex items-center gap-4 mt-6">
        <Button variant="outline" className="flex-1">
          <FullscreenIcon />
          Full Screen
        </Button>
        <Button className="flex-1">
          <ShareIcon />
          Share List
        </Button>
      </div>
      <div className="mt-5 flex flex-col gap-4">
        {reference?.items?.map(item => (
          <Card
            key={item.id}
            className="px-3 py-4 flex flex-row items-center gap-4 border-[#E5E5E5] shadow-none group"
          >
            <div className="flex-shrink-0">
              <div className="bg-[#A3A3A3] relative flex items-center justify-center rounded-lg h-12 w-12">
                {item.images[0]?.image?.url && (
                  <Image
                    src={item.images[0]?.image?.url}
                    alt={item.location.title ?? ''}
                    fill
                    className="object-cover rounded-lg z-0"
                  />
                )}
                <PencilIcon className="w-4 h-4 cursor-pointer opacity-0 group-hover:opacity-100 transition-opacity fill-[#1C1B1F] z-10 absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2" />
              </div>
            </div>
            <div className="flex flex-col flex-1">
              <Heading level={6} className="text-sm">
                {item.location.title}
              </Heading>
              <Body className="text-xs text-muted-foreground">
                {item.location.address}
              </Body>
              {item.commentCount && item.commentCount > 0 ? (
                <>
                  <Body className="text-sm text-sub-header block group-hover:hidden cursor-pointer">
                    {item.commentCount > 1
                      ? `${item.commentCount} Comments`
                      : `${item.commentCount} Comment`}
                  </Body>
                  <Body className="text-sm text-sub-header hidden group-hover:flex items-center gap-1 cursor-pointer">
                    <MessageCircleIcon className="w-4 h-4 cursor-pointer fill-[#404040]" />
                    View Comment
                  </Body>
                </>
              ) : (
                <Body className="text-sm text-sub-header hidden group-hover:flex items-center gap-1 cursor-pointer">
                  <MessageCircleIcon className="w-4 h-4 cursor-pointer fill-[#404040]" />
                  Add Comment
                </Body>
              )}
            </div>
            <div className="flex items-center gap-2">
              {isDeletingLocation ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <TrashIcon
                  className="w-4 h-4 cursor-pointer opacity-0 group-hover:opacity-100 transition-opacity"
                  onClick={() => {
                    setSelectedItem(item);
                    setIsRemoveLocationDialogOpen(true);
                  }}
                />
              )}
            </div>
          </Card>
        ))}
      </div>
      <ConfirmDialog
        open={isRemoveLocationDialogOpen}
        onOpenChange={setIsRemoveLocationDialogOpen}
        title="Remove reference from list?"
        description="This location will be removed from the reference list. You can add it again later if needed."
        confirmText="Remove"
        cancelText="Keep location"
        onConfirm={() => selectedItem && handleDeleteLocation(selectedItem)}
        onCancel={() => setIsRemoveLocationDialogOpen(false)}
      />
    </div>
  );
}
