import { z } from 'zod';
import { PropertyType } from '@/types/enum';

/**
 * Validation schema for creating/updating a tag
 */
export const createTagSchema = z.object({
  name: z
    .string()
    .min(1, 'The tag name is required')
    .trim()
    .max(100, 'Tag name must be less than 100 characters'),
  propertyType: z.nativeEnum(PropertyType, {
    message: 'The tag type is required',
  }),
  type: z.string(),
});

export const updateTagSchema = z.object({
  name: z
    .string()
    .min(1, 'The tag name is required')
    .trim()
    .max(100, 'Tag name must be less than 100 characters'),
  type: z.string({
    message: 'The tag type is required',
  }),
  color: z.string().optional(),
});

export type CreateTagFormData = z.infer<typeof createTagSchema>;
export type UpdateTagFormData = z.infer<typeof updateTagSchema>;
