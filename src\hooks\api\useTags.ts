import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useTagService } from '../use-services';
import { CreateTagData, Tag, UpdateTagData } from '@/types/tag';
import { queryKeys } from '@/lib/query-keys';
import { toastError, toastSuccess } from '@/lib/toast';
import { ApiError } from '@/lib/api-client';

interface UseTagsFilters {
  page?: number;
  search?: string;
  propertyType?: string;
  limit?: number;
}

interface UseTagsOptions {
  enabled?: boolean;
}

/**
 * Hook for fetching tags with filters
 */
export function useTags(filters?: UseTagsFilters, options?: UseTagsOptions) {
  const tagService = useTagService();

  return useQuery({
    queryKey: queryKeys.tags.list(filters),
    queryFn: async () => {
      return tagService.getTags(filters);
    },
    enabled: options?.enabled !== false,
    staleTime: 1000 * 30, // 30 seconds
    gcTime: 1000 * 60 * 5, // 5 minutes
  });
}

/**
 * Hook for creating a tag
 */
export function useCreateTag() {
  const tagService = useTagService();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (tagData: Partial<CreateTagData>) => {
      return tagService.createTag(tagData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.tags.all });
      toastSuccess('Tag created successfully.');
    },
    onError: (error: unknown) => {
      const message =
        (error as ApiError).response?.data?.message ?? 'Failed to create tag';
      toastError(message);
    },
  });
}

/**
 * Hook for updating a tag
 */
export function useUpdateTag() {
  const tagService = useTagService();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      id,
      data,
    }: {
      id: string;
      data: Partial<UpdateTagData>;
    }) => {
      return tagService.updateTag(id, data);
    },
    onSuccess: (updatedTag: Tag) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.tags.all });
      queryClient.setQueryData(
        queryKeys.tags.detail(updatedTag.id),
        updatedTag
      );
      toastSuccess('Tag updated successfully.');
    },
    onError: (error: unknown) => {
      const message =
        (error as ApiError).response?.data?.message ?? 'Failed to update tag';
      toastError(message);
    },
  });
}

/**
 * Hook for deleting a tag
 */
export function useDeleteTag() {
  const tagService = useTagService();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      return tagService.deleteTag(id);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.tags.all });
      toastSuccess('Tag deleted successfully.');
    },
    onError: (error: unknown) => {
      const message =
        (error as ApiError).response?.data?.message ?? 'Failed to delete tag';
      toastError(message);
    },
  });
}

/**
 * Hook for bulk deleting tags
 */
export function useBulkDeleteTags() {
  const tagService = useTagService();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (tagIds: string[]) => {
      return tagService.bulkDeleteTags(tagIds);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.tags.all });
      toastSuccess('Tags deleted successfully.');
    },
    onError: (error: unknown) => {
      // Handle special error case for tags in use
      interface TagsInUseErrorData {
        code: 'TAGS_IN_USE';
        usedTags: {
          id: string;
          name: string;
          color?: string;
        }[];
      }
      interface ErrorWithResponse {
        response?: {
          data?: TagsInUseErrorData;
        };
      }

      if (
        typeof error === 'object' &&
        error !== null &&
        (error as ErrorWithResponse).response?.data?.code === 'TAGS_IN_USE'
      ) {
        // Return the error so the component can handle it specially
        throw error;
      } else {
        const message =
          (error as ApiError).response?.data?.message ??
          'Failed to bulk delete tags';
        toastError(message);
      }
    },
  });
}
