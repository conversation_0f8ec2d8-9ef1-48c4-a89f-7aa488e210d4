import { User } from './user';

// Types for authentication
export interface LoginCredentials {
  email: string;
  password: string;
}

export interface LoginResponse {
  accessToken: string;
  refreshToken: string;
  accessTokenExpireTime: number;
  refreshTokenExpireTime: number;
}

export interface RefreshTokenResponse {
  accessToken: string;
  refreshToken: string;
  accessTokenExpireTime: number;
  refreshTokenExpireTime: number;
}

export interface AuthenticatedUser extends User {
  accessToken: string;
  refreshToken: string;
  accessTokenExpires: number;
  refreshTokenExpires: number;
}

export interface ChangePasswordData {
  currentPassword: string;
  newPassword: string;
}

export interface ForgotPasswordData {
  email: string;
}

export interface ResetPasswordData {
  token: string;
  newPassword: string;
}

export interface ResetPasswordConfirmData {
  token: string;
  password: string;
}

export interface InvitationUserInfoResponse {
  user: User;
}

export interface AcceptInvitationData {
  token: string;
  password: string;
}

export interface AcceptInvitationResponse {
  message: string;
  user: User;
}

export interface RegisterData {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  username: string;
}
