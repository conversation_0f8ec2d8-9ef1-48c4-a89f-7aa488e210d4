'use client';

import { useEffect, useState } from 'react';
import { usePathname } from 'next/navigation';
import { Routes, getPageTitle, getPageDescription } from '@/lib/routes';
import { Navbar } from '../Navbar';
import { SidebarInset } from '../ui/sidebar';
import { ScrollArea } from '../ui/scroll-area';
import { Skeleton } from '../ui/skeleton';
import { NavbarProvider } from '@/contexts/navbar-context';

interface PageLayoutProps {
  children: React.ReactNode;
}

export function AppLayout({ children }: PageLayoutProps) {
  const pathname = usePathname();
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    // Show loading state during navigation
    setIsLoading(true);

    // Find the matching route
    const currentRoute =
      Object.values(Routes).find(route => route === pathname) || Routes.HOME;

    // Update document title
    document.title = getPageTitle(currentRoute);

    // Update meta description
    const metaDescription = document.querySelector('meta[name="description"]');
    if (metaDescription) {
      metaDescription.setAttribute('content', getPageDescription(currentRoute));
    }

    // Remove artificial delay - hide loading state immediately
    setIsLoading(false);
  }, [pathname]);

  return (
    <NavbarProvider>
      <div className="flex h-screen w-full">
        {/* Main Content Area */}
        <SidebarInset>
          {/* Top Navbar */}
          <Navbar />

          {/* Page Content */}
          <ScrollArea className="h-[calc(100dvh-var(--navbar-height))]">
            <div className="flex flex-1 h-full">
              {isLoading ? (
                <div className="w-full space-y-4">
                  <Skeleton className="h-8 w-48" />
                  <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                    {Array.from({ length: 4 }).map((_, index) => (
                      <Skeleton key={index} className="h-32" />
                    ))}
                  </div>
                  <div className="grid gap-4 lg:grid-cols-2">
                    {Array.from({ length: 2 }).map((_, index) => (
                      <Skeleton key={index} className="h-64" />
                    ))}
                  </div>
                </div>
              ) : (
                children
              )}
            </div>
          </ScrollArea>
        </SidebarInset>
      </div>
    </NavbarProvider>
  );
}
