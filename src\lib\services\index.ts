// Export all domain services
export { authService, AuthService } from './auth-service';
export { userService, UserService } from './user-service';
export { dashboardService, DashboardService } from './dashboard-service';
export { locationService, LocationService } from './location-service';
export { tagService, TagService } from './tag-service';
export { assetService, AssetService } from './asset-service';
export {
  notificationService,
  NotificationService,
} from './notification-service';
export { referenceService, ReferenceService } from './reference-service';
export { fileService, FileService } from './file-service';
export {
  productionHouseService,
  ProductionHouseService,
} from './production-service';
export { commentService, CommentService } from './comment-service';

// Export types
export type {
  LoginCredentials,
  LoginResponse,
  RefreshTokenResponse,
  AuthenticatedUser,
  ChangePasswordData,
  ForgotPasswordData,
  ResetPasswordData,
  ResetPasswordConfirmData,
  AcceptInvitationData,
  AcceptInvitationResponse,
  InvitationUserInfoResponse,
} from '@/types/auth';
export type {
  CreateLocationData,
  UpdateLocationData,
} from './location-service';
export type {
  CreateAssetData,
  UpdateAssetData,
  AssetCategory,
} from './asset-service';
export type {
  Notification,
  CreateNotificationData,
  NotificationPreferences,
} from './notification-service';
export type {
  CreateReferenceData,
  UpdateReferenceData,
  ReferenceFilters,
  ReferenceListResponse,
} from './reference-service';
