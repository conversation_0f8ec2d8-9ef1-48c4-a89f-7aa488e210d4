import { httpClient } from '../http-client';
export interface ProductionHouse {
  id: string;
  name: string;
  createdAt: string;
  updatedAt: string;
}

export class ProductionHouseService {
  async getProductionHouses(): Promise<ProductionHouse[]> {
    return httpClient.get<ProductionHouse[]>('/production-houses');
  }

  async createProductionHouse(productionHouseData: {
    name: string;
  }): Promise<ProductionHouse> {
    return httpClient.post<ProductionHouse>(
      '/production-houses',
      productionHouseData
    );
  }
}

export const productionHouseService = new ProductionHouseService();
