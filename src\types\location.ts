import { Tag } from './tag';
import { PropertyType, TagType } from './enum';

export interface Photo {
  id?: string;
  key?: string;
  url: string;
  order: number;
  tags?: Tag[];
  commentCount?: number;
}

export interface Contact {
  id: string;
  name?: string;
  email?: string;
  phone?: string;
}

export interface Location {
  id: string;
  title: string;
  name: string;
  address: string;
  placeId?: string;
  city: string;
  state: string;
  zipCode: string;
  postalCode?: string;
  country: string;
  latitude?: number;
  longitude?: number;
  description?: string;
  coverImageUrl: string | null;
  status: string;
  createdAt: string;
  updatedAt: string;
  avatar?: string;
  tags: Tag[];
  commentCount?: number;
  images: Photo[];
  size?: string;
  contact: Contact;
}

export interface LocationRequest {
  address: string;
  placeId?: string;
  city?: string;
  state?: string;
  country?: string;
  postalCode?: string;
  description: string;
  contactEmail: string;
  contactPhones: string[];
  propertyType?: PropertyType | '';
  // Tags organized by category type - maps TagCategory to array of tag IDs
  tags: Partial<Record<TagType, string[]>>;
  size: string;
  images: Photo[];
  latitude?: number;
  longitude?: number;
}

export interface LocationContact {
  id: string;
  name?: string;
  email: string;
  phone: string;
  label?: string;
  isThirdPartyRep?: boolean;
  isPrimary?: boolean;
}
