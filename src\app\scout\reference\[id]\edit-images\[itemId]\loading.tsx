import { Skeleton } from '@/components/ui/skeleton';
export default function Loading() {
  return (
    <div className="w-full flex justify-center">
      <div className="space-y-11 w-full xl:w-[70.5rem] p-11 xl:px-0">
        {/* Header Section */}
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-4">
          {/* Left side: Back button and text */}
          <div className="inline-flex items-center gap-3">
            <Skeleton className="h-5 w-5 rounded-full" />{' '}
            {/* For ArrowLeft icon */}
            <div className="flex flex-col justify-start gap-1">
              <Skeleton className="h-7 w-48" />{' '}
              {/* For "Edit Images" Heading */}
              <Skeleton className="h-4 w-64" />{' '}
              {/* For "Downtown Coffee Shop Scene" paragraph */}
            </div>
          </div>
          {/* Right side: Buttons */}
          <div className="flex items-center gap-2">
            <Skeleton className="h-10 w-24" /> {/* For "Cancel" button */}
            <Skeleton className="h-10 w-24" /> {/* For "Save" button */}
          </div>
        </div>

        {/* Photo Grid Section */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2">
          {Array.from({ length: 6 }).map(
            (
              _,
              index // Display a few image skeletons
            ) => (
              <Skeleton
                key={index}
                className="w-full aspect-square rounded-xl"
              />
            )
          )}
        </div>
      </div>
    </div>
  );
}
