'use client';

import { useSearchParams, useRouter } from 'next/navigation';
import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Loader2 } from 'lucide-react';
import Image from 'next/image';
import { Heading } from '@/components/ui/typography';
import { Body } from '@/components/ui/typography';
import { authService } from '@/lib/services/auth-service';
import { handleError } from '@/lib/error-handler';
import { toastSuccess } from '@/lib/toast';
import { Routes } from '@/lib/routes';
import { User } from '@/types/user';

export default function InvitationPage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [error, setError] = useState<string | null>(null);
  const invitationToken = searchParams.get('token');
  const [user, setUser] = useState<User | null>(null);
  const [inputEmail, setInputEmail] = useState<string>('');
  const [invitationResent, setInvitationResent] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [resendError, setResendError] = useState<boolean>(false);

  useEffect(() => {
    if (!invitationToken) {
      setError(
        'Invalid invitation link. Please check your email for the correct link.'
      );
      setResendError(false);
      return;
    }

    // Fetch user info from invitation token using authService
    const fetchUserInfo = async () => {
      setError(null);

      try {
        const { user: userData } =
          await authService.getInvitationUserInfo(invitationToken);
        setUser(userData);

        // Redirect to setup-password page on success
        router.push(`/setup-password?token=${invitationToken}`);
      } catch (err) {
        const { message } = handleError(
          err,
          'An error occurred while fetching user information'
        );
        setError(message);
        setResendError(false);
        // Try to extract user info from error if available
        // This might not always be available, so we'll handle null case
      }
    };

    fetchUserInfo();
  }, [invitationToken, router]);

  const handleRequestNewLink = async (emailToUse: string) => {
    if (!emailToUse.trim()) {
      setError('Please enter an email address');
      setResendError(false);
      return;
    }

    setLoading(true);
    setError(null);
    setResendError(false);
    try {
      await authService.resendInvitation(emailToUse);
      toastSuccess('Invitation link resent successfully');
      setInvitationResent(true);
      setResendError(false);
    } catch (err) {
      setResendError(true);
      const { message } = handleError(
        err,
        'An error occurred while resending the invitation link'
      );
      setError(message);
    } finally {
      setLoading(false);
    }
  };

  const handleGoToLogin = () => {
    router.push(Routes.SIGN_IN);
  };

  if (error) {
    const showEmailInput = !user || !user?.email;

    return (
      <div className="flex flex-col items-center justify-center max-w-[28.5rem] gap-10 w-full">
        <div className="relative w-[124px] h-[40px]">
          <Image
            src="/assets/logo.svg"
            alt="Scoutr Logo"
            fill
            priority
            className="object-contain"
          />
        </div>
        <div className="w-full">
          <Heading
            level={3}
            className="text-header font-semibold mb-2 text-center"
          >
            Oh no! Link not working
          </Heading>
          <Body className="text-center">
            Looks like this invitation has expired or isn&apos;t valid anymore.
            Don&apos;t worry, you can easily request a new one.
          </Body>
        </div>
        {showEmailInput && (
          <div className="w-full">
            <Input
              type="email"
              placeholder="Enter your email"
              value={inputEmail}
              onChange={e => setInputEmail(e.target.value)}
              disabled={loading || invitationResent}
              className="w-full"
            />
          </div>
        )}
        {resendError && (
          <div className="w-full p-4 bg-muted rounded-lg border border-border">
            <Body className="text-center text-sm text-muted-foreground">
              Unable to resend the invitation link. Please contact your
              administrator to request a new invitation.
            </Body>
          </div>
        )}
        {invitationResent ? (
          <Button className="w-full" onClick={handleGoToLogin}>
            Go to log in
          </Button>
        ) : (
          <Button
            className="w-full"
            onClick={() => handleRequestNewLink(user?.email || inputEmail)}
            disabled={(!user?.email && !inputEmail.trim()) || loading}
          >
            {loading ? 'Sending...' : 'Request a new link'}
          </Button>
        )}
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center justify-center">
      <Loader2 className="w-10 h-10 animate-spin" />
    </div>
  );
}
