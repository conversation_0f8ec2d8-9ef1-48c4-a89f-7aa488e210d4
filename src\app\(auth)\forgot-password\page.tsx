'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Routes } from '@/lib/routes';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Heading, Body } from '@/components/ui/typography';
import Image from 'next/image';
import { toastSuccess, toastError } from '@/lib/toast';
import { authService } from '@/lib/services/auth-service';
import { handleError } from '@/lib/error-handler';

const forgotPasswordSchema = z.object({
  email: z
    .string()
    .min(1, 'Email is required')
    .email('Please enter a valid email address'),
});

type ForgotPasswordFormData = z.infer<typeof forgotPasswordSchema>;

export default function ForgotPasswordPage() {
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const router = useRouter();

  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
  } = useForm<ForgotPasswordFormData>({
    resolver: zodResolver(forgotPasswordSchema),
    mode: 'onChange',
  });

  const onSubmit = async (data: ForgotPasswordFormData) => {
    setLoading(true);

    try {
      await authService.forgotPassword({ email: data.email });
      setSuccess(true);
      toastSuccess('Reset link sent if email exists.');
    } catch (error) {
      const { message } = handleError(
        error,
        'An error occurred while sending reset link'
      );
      toastError(message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-[28.5rem] w-full flex flex-col items-center justify-center gap-12">
      <div className="flex flex-col items-center justify-center gap-12">
        <div className="flex items-center justify-center">
          <Image
            src="/assets/logo.svg"
            alt="Scoutr Logo"
            width={138}
            height={0}
          />
        </div>
        <div className="space-y-2">
          {success ? (
            <>
              <Heading
                level={3}
                className="text-header font-semibold text-center"
              >
                Check Your Email
              </Heading>
              <Body className="text-center max-w-[25rem] mx-auto mt-2">
                We&apos;ve sent you a password reset link. Please check your
                email and click the link to reset your password. If you
                don&apos;t see it, check your spam folder.
              </Body>
            </>
          ) : (
            <>
              <Heading
                level={3}
                className="text-header font-semibold text-center"
              >
                Forgot your password?
              </Heading>
              <Body className="text-center max-w-[25rem] mx-auto mt-2">
                No worries! It happens to the best of us. Enter your email
                address and we&apos;ll send you a link to reset your password.
              </Body>
            </>
          )}
        </div>
      </div>

      {success ? (
        <Button onClick={() => router.push(Routes.SIGN_IN)} className="w-full">
          Back to Sign In
        </Button>
      ) : (
        <>
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-12 w-full">
            <div className="space-y-2">
              <Label htmlFor="email" className="text-sub-header">
                Email
              </Label>
              <Input
                id="email"
                type="email"
                placeholder="Enter your email"
                {...register('email')}
                className={
                  errors.email
                    ? 'border-red-500 focus-visible:border-red-500 focus-visible:ring-red-500/20'
                    : ''
                }
              />
            </div>

            <div className="space-y-6">
              <Button
                type="submit"
                className="w-full"
                disabled={loading || !isValid}
              >
                {loading ? 'Sending...' : 'Send reset link'}
              </Button>
              <Body className="text-sm text-foreground text-center">
                Remember your password?{' '}
                <Link
                  href={Routes.SIGN_IN}
                  className="underline cursor-pointer text-primary"
                >
                  Back to sign in
                </Link>
              </Body>
            </div>
          </form>
        </>
      )}
    </div>
  );
}
