import { Comment } from '@/types';
import { CommentLoading, NoCommentsYet } from './CommentLoading';
import { User } from 'next-auth';
import { CommentItem } from './CommentItem';

interface CommentModalBodyProps {
  comments: Comment[];
  user: User | null;
  loading: boolean;
  onDelete: (commentId: string) => void;
  onEdit: (commentId: string, content: string) => void;
}

export const CommentModalBody = ({
  comments,
  user,
  loading,
  onDelete,
  onEdit,
}: CommentModalBodyProps) => {
  return (
    <div className="p-4 min-h-[400px] overflow-y-auto h-full">
      <div className="flex flex-col gap-4">
        {comments.map(comment => (
          <CommentItem
            key={comment.id}
            comment={comment}
            isOwnComment={user?.id === comment.author.id}
            onDelete={() => onDelete(comment.id)}
            onEdit={() => onEdit(comment.id, comment.content)}
          />
        ))}
        {loading && <CommentLoading />}
      </div>
      {!loading && comments.length === 0 && (
        <div className="flex items-center justify-center h-full">
          <NoCommentsYet />
        </div>
      )}
    </div>
  );
};
