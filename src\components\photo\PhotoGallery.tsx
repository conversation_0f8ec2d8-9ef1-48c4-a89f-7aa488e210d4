import { FileError, FileErrorCode, FileUpload } from '@/types';
import { Plus } from 'lucide-react';
import { useRef, useCallback, useMemo, useState } from 'react';
import { CheckedState } from '@radix-ui/react-checkbox';
import {
  closestCenter,
  DndContext,
  DragOverlay,
  MouseSensor,
  TouchSensor,
  UniqueIdentifier,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import { GalleryItem } from './GalleryItem';
import {
  rectSortingStrategy,
  arrayMove,
  SortableContext,
} from '@dnd-kit/sortable';
import { GridContainer } from './GridContainer';

interface PhotoGalleryProps {
  data: FileUpload[];
  className?: string;
  onAdd?: (files: FileList | null) => void;
  onChange?: (uuid: string, file: FileUpload) => void;
  onCheckedChange?: (uuid: string, checked: CheckedState) => void;
  onRemove?: (uuid: string) => void;
  onEdit?: (uuid: string) => void;
  onReorder?: (reorderedData: FileUpload[]) => void;
}

export default function PhotoGallery({
  data,
  className,
  onCheckedChange,
  onAdd,
  onRemove,
  onEdit,
  onChange,
  onReorder,
}: PhotoGalleryProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const activeUploadsRef = useRef<Set<string>>(new Set());
  const [uploadSlotVersion, setUploadSlotVersion] = useState(0);
  const MAX_CONCURRENT_UPLOADS = 3;
  const sensors = useSensors(useSensor(MouseSensor), useSensor(TouchSensor));
  const [activeId, setActiveId] = useState<UniqueIdentifier | null>(null);
  const [draggedItemIndex, setDraggedItemIndex] = useState<number | null>(null);

  // Add the "Add more" button as a special item in the sortable list
  const allItems = useMemo(
    () => [...data.map(photo => photo.uuid), 'add-more-button'],
    [data]
  );

  // Check if upload can start and reserve a slot atomically
  const canStartUpload = useCallback((uuid: string): boolean => {
    if (activeUploadsRef.current.has(uuid)) {
      return true; // Already registered
    }
    if (activeUploadsRef.current.size < MAX_CONCURRENT_UPLOADS) {
      activeUploadsRef.current.add(uuid);
      return true;
    }
    return false;
  }, []);

  // Track upload state changes
  const handleUploadStart = useCallback((uuid: string) => {
    if (!activeUploadsRef.current.has(uuid)) {
      activeUploadsRef.current.add(uuid);
    }
  }, []);

  const handleUploadEnd = useCallback((uuid: string) => {
    if (activeUploadsRef.current.has(uuid)) {
      activeUploadsRef.current.delete(uuid);
      // Trigger re-render to allow queued items to check again
      setUploadSlotVersion(prev => prev + 1);
    }
  }, []);

  const handleUploadChange = useCallback(
    (uuid: string) => (file: FileUpload) => {
      // Track upload state
      if (file.isUploading && !activeUploadsRef.current.has(uuid)) {
        handleUploadStart(uuid);
      } else if (
        (!file.isUploading && activeUploadsRef.current.has(uuid)) ||
        file.isUploaded ||
        file.isUploadError
      ) {
        handleUploadEnd(uuid);
      }
      onChange?.(uuid, file);
    },
    [onChange, handleUploadStart, handleUploadEnd]
  );

  const handleUploadError = () => (error: FileError) => {
    if (error.code === FileErrorCode.UPLOAD_ERROR) {
      console.log('uploadError', error);
    }
  };

  const handleBrowseClick = useCallback(() => {
    fileInputRef.current?.click();
  }, []);

  // Memoize the callback functions to prevent unnecessary re-renders
  const createEditHandler = useCallback(
    (uuid: string) => {
      return () => onEdit?.(uuid);
    },
    [onEdit]
  );

  const createRemoveHandler = useCallback(
    (uuid: string) => {
      return () => onRemove?.(uuid);
    },
    [onRemove]
  );

  const createCheckedChangeHandler = useCallback(
    (uuid: string) => {
      return (checked: CheckedState) => onCheckedChange?.(uuid, checked);
    },
    [onCheckedChange]
  );

  const handleAdd = (e: React.ChangeEvent<HTMLInputElement>) => {
    onAdd?.(e.target.files);
  };

  function handleDragStart(event: { active: { id: UniqueIdentifier } }) {
    const id = event.active.id as string;
    setActiveId(id);
    // Store the original index of the dragged item to maintain its size in overlay
    const originalIndex = allItems.indexOf(id);
    setDraggedItemIndex(originalIndex);
  }

  const handleDragEnd = useCallback(
    (event: {
      active: { id: UniqueIdentifier };
      over: { id: UniqueIdentifier } | null;
    }) => {
      const { active, over } = event;

      // Prevent any drag operations involving the add button
      if (active.id === 'add-more-button' || over?.id === 'add-more-button') {
        return;
      }

      // Check if any files are currently uploading
      const hasUploadingFiles = data.some(photo => photo.isUploading);
      if (hasUploadingFiles) {
        return;
      }

      // Only allow reordering between actual photo items
      if (over && active.id !== over.id) {
        const oldIndex = data.findIndex(p => p.uuid === active.id);
        const newIndex = data.findIndex(p => p.uuid === over.id);

        // Ensure both indices are valid photo indices (not the add button)
        if (oldIndex !== -1 && newIndex !== -1) {
          const reorderedData = arrayMove(data, oldIndex, newIndex);
          const reorderedWithOrder = reorderedData.map((item, index) => ({
            ...item,
            order: index,
          }));

          // Notify parent component
          onReorder?.(reorderedWithOrder as FileUpload[]);
        }
      }
      setActiveId(null);
      setDraggedItemIndex(null);
    },
    [data, onReorder]
  );

  function handleDragCancel() {
    setActiveId(null);
    setDraggedItemIndex(null);
  }

  return (
    <div className={className}>
      {/* <Sortable
        {...props}
        onDragEnd={handleDragEnd}
        wrapperStyle={({ index }) => {
          if (index === 0) {
            return {
              height: '21.0625rem',
              gridRowStart: 'span 2',
              gridColumnStart: 'span 2',
            };
          }

          return {
            width: '100%',
            height: '10.5625rem',
          };
        }}
        items={[...allItems]}
        isDisabled={id => {
          return id === 'add-more-button' || data.some(p => p.isUploading);
        }}
        useDragOverlay={true}
      >
        {(id, index) => {
          // Handle the "Add more" button
          if (id === 'add-more-button') {
            return (
              <div
                key="add-more-button"
                onClick={handleBrowseClick}
                className={`${index === 0 ? 'h-full' : 'h-[10.5625rem]'} w-full rounded-lg bg-[#E5E5E5] flex items-center justify-center gap-2 cursor-pointer hover:bg-gray-100 transition-colors`}
                style={{ pointerEvents: 'auto' }}
              >
                <Plus className="w-6 h-6 text-gray-500" />
                <span className="text-gray-600 font-medium">
                  Add more photos
                </span>
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/jpeg,image/jpg,image/png"
                  multiple
                  onChange={handleAdd}
                  className="hidden"
                />
              </div>
            );
          }

          // Handle regular photo items
          const key = id as string;
          const photo = data.find(p => p.uuid === key);
          if (!photo) {
            return <></>;
          }

          return (
            <GalleryItem
              key={photo.uuid}
              photo={photo}
              uploadSlotVersion={uploadSlotVersion}
              activeUploadCount={activeUploadsRef.current.size}
              onCanUpload={canStartUpload}
              onEdit={createEditHandler(photo.uuid)}
              onRemove={createRemoveHandler(photo.uuid)}
              onCheckedChange={createCheckedChangeHandler(photo.uuid)}
              onUploadComplete={handleUploadChange(photo.uuid)}
              onUploadError={handleUploadError()}
              onUploading={handleUploadChange(photo.uuid)}
            />
          );
        }}
      </Sortable> */}

      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
        onDragCancel={handleDragCancel}
      >
        <SortableContext items={allItems} strategy={rectSortingStrategy}>
          <GridContainer columns={2}>
            {allItems.map((item, index) => {
              const id = item as string;
              if (id === 'add-more-button') {
                return (
                  <div
                    key="add-more-button"
                    onClick={handleBrowseClick}
                    className={`${index === 0 ? 'h-full' : 'h-[10.5625rem]'} w-full rounded-lg bg-[#E5E5E5] flex items-center justify-center gap-2 cursor-pointer hover:bg-gray-100 transition-colors`}
                    style={{ pointerEvents: 'auto' }}
                  >
                    <Plus className="w-6 h-6 text-gray-500" />
                    <span className="text-gray-600 font-medium">
                      Add more photos
                    </span>
                    <input
                      ref={fileInputRef}
                      type="file"
                      accept="image/jpeg,image/jpg,image/png"
                      multiple
                      onChange={handleAdd}
                      className="hidden"
                    />
                  </div>
                );
              }

              // Handle regular photo items
              const key = id as string;
              const photo = data.find(p => p.uuid === key);
              if (!photo) {
                return <></>;
              }

              return (
                <GalleryItem
                  index={index}
                  key={photo.uuid}
                  photo={photo}
                  uploadSlotVersion={uploadSlotVersion}
                  activeUploadCount={activeUploadsRef.current.size}
                  onCanUpload={canStartUpload}
                  onEdit={createEditHandler(photo.uuid)}
                  onRemove={createRemoveHandler(photo.uuid)}
                  onCheckedChange={createCheckedChangeHandler(photo.uuid)}
                  onUploadComplete={handleUploadChange(photo.uuid)}
                  onUploadError={handleUploadError()}
                  onUploading={handleUploadChange(photo.uuid)}
                />
              );
            })}
          </GridContainer>
        </SortableContext>
        <DragOverlay adjustScale={true}>
          {activeId && draggedItemIndex !== null ? (
            <GalleryItem
              index={draggedItemIndex}
              photo={data.find(p => p.uuid === activeId) as FileUpload}
            />
          ) : null}
        </DragOverlay>
      </DndContext>
    </div>
  );
}
