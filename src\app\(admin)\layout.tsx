'use client';

import { useAuth } from '@/contexts/auth-context';
import { Role } from '@/types';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { AdminLayoutWithSidebar } from '@/components/layout/admin-layout-with-sidebar';
import { Routes } from '@/lib/routes';

interface AdminRouteGroupLayoutProps {
  children: React.ReactNode;
}

export default function AdminRouteGroupLayout({
  children,
}: AdminRouteGroupLayoutProps) {
  const { user } = useAuth();
  const router = useRouter();

  useEffect(() => {
    // Only SuperAdmin can access these routes
    if (user && user.role !== Role.SuperAdmin) {
      router.push(Routes.NO_PERMISSION);
    }
  }, [user, router]);

  // Show nothing while checking or if not SuperAdmin
  if (!user || user.role !== Role.SuperAdmin) {
    return null;
  }

  return <AdminLayoutWithSidebar>{children}</AdminLayoutWithSidebar>;
}
