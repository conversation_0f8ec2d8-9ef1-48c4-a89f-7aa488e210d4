import {
  startTransition,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { InputFileIcon } from '@/lib/icons';
import { Button } from '@/components/ui/button';
import { MINIMUM_IMAGES } from '@/types/constant';
import { Photo } from '@/types/location';
import { TagOption } from '@/types/tag';
import { FileUpload } from '@/types';
import PhotoGallery from './PhotoGallery';
import { CheckedState } from '@radix-ui/react-checkbox';
import { toastInfo, toastSuccess } from '@/lib/toast';
import { filesValidator } from '@/lib/utils/fileValidator';
import { generateUUID } from '@/lib/utils';
import EditImageTagsModal from '../EditImageTags';
import BulkAddTagsModal from '../features/tags/BulkAddTags';

interface PhotosFormProps {
  data: Photo[];
  onChange: (data: Partial<Photo[]>) => void;
  onUploadStatusChange?: (hasUploading: boolean) => void;
}

export default function PhotosForm({
  data,
  onChange,
  onUploadStatusChange,
}: PhotosFormProps) {
  const [isDragging, setIsDragging] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [editIndex, setEditIndex] = useState<number>();
  const [editTagOpen, setEditTagOpen] = useState<boolean>(false);
  const [bulkAddTagsOpen, setBulkAddTagsOpen] = useState<boolean>(false);
  const [photos, setPhotos] = useState<FileUpload[]>([]);

  useEffect(() => {
    if (data.length > 0 && photos.length === 0) {
      setPhotos(
        data
          .sort((a, b) => a.order - b.order)
          .map(p => ({
            uuid: generateUUID(),
            id: p.id,
            url: p.url,
            imageSrc: p.url,
            key: p.key,
            order: p.order,
            tags: p.tags,
          }))
      );
    }
  }, [data, photos]);

  const handleChange = useCallback(
    (uuid: string, file: FileUpload) => {
      const index = photos.findIndex(photo => photo.uuid === uuid);
      if (index !== -1) {
        const newPhotos = [...photos];
        newPhotos[index] = {
          ...newPhotos[index],
          isUploadError: file.isUploadError,
          isUploadSuccess: file.isUploadSuccess,
          isUploading: file.isUploading,
          isUploaded: file.isUploaded,
          url: file.url,
          key: file.key,
        };
        setPhotos(newPhotos);
        onChange(newPhotos as Photo[]);
      }
    },
    [onChange, photos]
  );

  // Check if any uploads are in progress and notify parent
  useEffect(() => {
    const hasUploading = photos.some(photo => photo.isUploading === true);
    onUploadStatusChange?.(hasUploading);
  }, [photos, onUploadStatusChange]);

  const handleReorder = useCallback(
    (reorderedData: FileUpload[]) => {
      startTransition(() => {
        onChange(
          reorderedData.filter(photo => photo.url !== undefined) as Photo[]
        );
      });
      setPhotos(reorderedData);
    },
    [onChange]
  );

  //#region Handle Files

  const handleFiles = (files: FileList | null, isAddMore = false) => {
    if (!files || files.length === 0) return;
    const acceptedFiles = filesValidator(Array.from(files));
    if (isAddMore) {
      setPhotos(prev => [
        ...prev,
        ...acceptedFiles.map((file, index) => ({
          uuid: generateUUID(),
          file,
          imageSrc: URL.createObjectURL(file),
          order: prev.length + index,
          tags: [],
        })),
      ]);
    } else {
      setPhotos(
        acceptedFiles.map((file, index) => ({
          uuid: generateUUID(),
          file,
          imageSrc: URL.createObjectURL(file),
          order: index,
          tags: [],
        }))
      );
    }
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
    handleFiles(e.dataTransfer.files);
  };

  const handleBrowseClick = () => fileInputRef.current?.click();

  const handlePaste = useCallback((e: ClipboardEvent) => {
    const items = e.clipboardData?.items;
    if (!items) return;

    const files: File[] = Array.from(items)
      .map(item => item.getAsFile())
      .filter(file => file !== null);
    const acceptedFiles = filesValidator(files);
    setPhotos(
      acceptedFiles.map((file, index) => ({
        uuid: generateUUID(),
        file,
        imageSrc: URL.createObjectURL(file),
        order: index,
        tags: [],
      }))
    );
    if (acceptedFiles.length > 0) {
      toastSuccess(
        `${acceptedFiles.length} image${acceptedFiles.length > 1 ? 's' : ''} pasted successfully`
      );
    }
  }, []);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.ctrlKey || e.metaKey) && e.key === 'v') {
        setTimeout(() => {
          document.addEventListener('paste', handlePaste, { once: true });
        }, 0);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [handlePaste]);

  //#endregion Handle Files

  const handleCheckedChange = (uuid: string, checked: CheckedState) => {
    const index = photos.findIndex(photo => photo.uuid === uuid);
    if (index === -1) return;
    const newPhotos = [...photos];
    newPhotos[index] = {
      ...newPhotos[index],
      isChecked: checked === 'indeterminate' ? undefined : checked,
    };
    setPhotos(newPhotos);
    onChange(newPhotos as Photo[]);
  };

  const handleRemove = useCallback(
    (uuid: string) => {
      const index = photos.findIndex(photo => photo.uuid === uuid);
      if (index !== -1) {
        const newPhotos = [...photos];
        newPhotos.splice(index, 1);
        setPhotos(newPhotos);
        onChange(newPhotos as Photo[]);
      }
    },
    [onChange, photos]
  );

  const handleEdit = (uuid: string) => {
    const index = photos.findIndex(photo => photo.uuid === uuid);
    if (index !== -1) {
      setEditIndex(index);
      setEditTagOpen(true);
    }
  };

  const handleEditImageTags = (tags: TagOption[]) => {
    const result = photos.map((photo, index) => {
      return index !== editIndex
        ? photo
        : {
            ...photo,
            tags: tags.map(tag => ({
              id: tag.value,
              name: tag.label,
              type: 'style',
              color: tag.color,
            })),
          };
    });
    setPhotos(result as FileUpload[]);
    onChange(result as Photo[]);
    toastSuccess('Tags updated successfully.', { position: 'bottom-right' });
  };

  const handleBulkAddTags = (tags: TagOption[]) => {
    const updatedData = photos.map(photo => {
      if (photo.isChecked) {
        return {
          ...photo,
          tags: tags.map(tag => ({
            id: tag.value,
            name: tag.label,
            type: 'style',
            color: tag.color,
          })),
        };
      } else {
        return photo;
      }
    });
    setPhotos(updatedData as FileUpload[]);
    onChange(updatedData as Photo[]);
    toastSuccess('Tags updated successfully.', { position: 'bottom-right' });
  };

  const handleOpenBulkAddTags = () => {
    if (photos.some(photo => photo.isChecked)) {
      setBulkAddTagsOpen(true);
      return;
    }
    toastInfo('Please select one or more photos', {
      position: 'bottom-right',
    });
  };

  const tagsSelected = useMemo(() => {
    return editIndex !== undefined ? (photos[editIndex]?.tags ?? []) : [];
  }, [photos, editIndex]);

  return (
    <>
      <div className="w-full sm:w-[42rem] flex flex-col gap-6">
        <div className="space-y-2">
          <h1 className="text-2xl font-bold text-gray-900">
            {photos.length
              ? `${photos.length} ${photos.length > 1 ? 'photos' : 'photo'} selected`
              : 'Add photos for the location'}
          </h1>
          <p className="text-gray-600">
            {photos.length
              ? 'Drag to reorder. Add tags for context. The first photo will be used as the cover.'
              : `Choose at least ${MINIMUM_IMAGES} photos for your location`}
          </p>
          {photos.length > 0 && (
            <Button
              onClick={handleOpenBulkAddTags}
              variant="outline"
              className="w-[11.4375rem] h-[2.625rem]"
            >
              Bulk add tags
            </Button>
          )}
        </div>
        <div
          className={`flex flex-col items-center ${photos.length ? 'hidden' : ''}`}
        >
          <div
            onDragOver={e => {
              e.preventDefault();
              setIsDragging(true);
            }}
            onDragLeave={() => setIsDragging(false)}
            onDrop={handleDrop}
            className={`w-full sm:w-[42rem] bg-card-background border-2 rounded-xl py-[3.125rem] flex flex-col items-center justify-center text-center transition-colors ${
              isDragging ? 'border-primary' : 'border-dashed border-border'
            }`}
          >
            <div className="text-gray-700">
              <button
                className="flex flex-col items-center cursor-pointer gap-4 text-xl text-sub-header leading-7"
                type="button"
                onClick={handleBrowseClick}
              >
                <div className="flex items-center justify-center w-16 h-16 rounded-full bg-[#E5E5E5]">
                  <InputFileIcon />
                </div>
                Drag and drop photos here
              </button>
              <div className="text-[#737373] mt-2">
                or click to browse your files
              </div>
            </div>
            <input
              ref={fileInputRef}
              type="file"
              accept="image/jpeg,image/jpg,image/png"
              multiple
              onChange={e => handleFiles(e.target.files)}
              className="hidden"
            />
          </div>
          <p className="text-sm text-[#737373] leading-5 mt-3">
            Supported formats: JPG, PNG. Max size: 10MB per file.
          </p>
        </div>
        {photos.length > 0 && (
          <PhotoGallery
            data={photos}
            className="max-w-[34.5rem]"
            onAdd={(files: FileList | null) => handleFiles(files, true)}
            onChange={handleChange}
            onCheckedChange={handleCheckedChange}
            onRemove={handleRemove}
            onEdit={handleEdit}
            onReorder={handleReorder}
          />
        )}
      </div>
      <EditImageTagsModal
        open={editTagOpen}
        onOpenChange={setEditTagOpen}
        onSave={handleEditImageTags}
        tags={
          tagsSelected?.map(tag => ({
            value: tag.id,
            label: tag.name,
            color: tag.color,
          })) ?? []
        }
      />
      <BulkAddTagsModal
        open={bulkAddTagsOpen}
        onOpenChange={setBulkAddTagsOpen}
        onSave={handleBulkAddTags}
      />
    </>
  );
}
