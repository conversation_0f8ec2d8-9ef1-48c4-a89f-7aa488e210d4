'use client';

import { createContext, useContext, useEffect, useState } from 'react';
import { useSession, signIn, signOut } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { User } from 'next-auth';
import { authService, userService } from '@/lib/services';
import { Routes } from '@/lib/routes';

interface AuthContextType {
  user: User | null;
  loading: boolean;
  signIn: (
    email: string,
    password: string,
    callbackUrl?: string
  ) => Promise<void>;
  signOut: () => Promise<void>;
  isAuthenticated: boolean;
  refreshUserInfo: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const { data: session, status, update } = useSession();
  const [loading, setLoading] = useState(true);
  const [hasRefreshed, setHasRefreshed] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isLoggingOut, setIsLoggingOut] = useState(false);
  const router = useRouter();

  useEffect(() => {
    if (status !== 'loading') {
      setLoading(false);
    }
  }, [status]);

  // Handle session errors by logging out
  useEffect(() => {
    if (session?.error === 'RefreshAccessTokenError' && !isLoggingOut) {
      handleSignOut();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [session?.error]);

  const refreshUserInfo = async () => {
    if (!session?.user || isRefreshing) return;

    setIsRefreshing(true);
    try {
      const freshUserProfile = await userService.getProfile();
      await update({
        user: {
          ...session.user,
          ...freshUserProfile,
        },
      });
    } catch (error) {
      console.error('Failed to refresh user profile:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  // Refresh user info only once on initial authentication
  useEffect(() => {
    if (
      status === 'authenticated' &&
      session?.user &&
      !hasRefreshed &&
      !isRefreshing
    ) {
      setHasRefreshed(true);
      refreshUserInfo();
    }

    // Reset refresh flag when user logs out
    if (status === 'unauthenticated') {
      setHasRefreshed(false);
      setIsRefreshing(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [status, session?.user, hasRefreshed, isRefreshing]);

  const handleSignIn = async (
    email: string,
    password: string,
    callbackUrl?: string
  ) => {
    try {
      const result = await signIn('credentials', {
        email,
        password,
        redirect: false,
        callbackUrl: callbackUrl || '/',
      });

      if (result?.error) {
        throw new Error('Invalid credentials');
      }

      router.replace(callbackUrl || '/');
      router.refresh();
    } catch (error) {
      throw error;
    }
  };

  const handleSignOut = async () => {
    // Prevent multiple logout attempts
    if (isLoggingOut) return;

    setIsLoggingOut(true);

    // Always clear local session first, regardless of backend response
    try {
      // Try to logout from backend, but don't block if it fails
      await authService.logout();
    } catch (error) {
      // This is expected if the token is expired/invalid - don't alarm the user
      if (process.env.NODE_ENV === 'development') {
        console.warn(
          'Backend logout failed (continuing with local logout):',
          error
        );
      }
    } finally {
      // Always clear the local session, even if backend logout fails
      try {
        await signOut({ redirect: false });
      } catch (error) {
        // This should rarely happen, but log it for debugging
        if (process.env.NODE_ENV === 'development') {
          console.warn('Failed to clear NextAuth session:', error);
        }
      }

      setIsLoggingOut(false);
      // Always redirect to sign-in page
      router.push(Routes.SIGN_IN);
    }
  };

  const value = {
    user: session?.user as User | null,
    loading,
    signIn: handleSignIn,
    signOut: handleSignOut,
    isAuthenticated: !!session,
    refreshUserInfo,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
