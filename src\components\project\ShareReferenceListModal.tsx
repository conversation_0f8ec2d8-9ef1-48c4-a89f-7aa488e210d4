'use client';

import { CopyIcon, PlayArrowIcon } from '@/lib/icons';
import { Button } from '../ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '../ui/dialog';
import { Input } from '../ui/input';
import { Separator } from '../ui/separator';
import { toastError, toastSuccess } from '@/lib/toast';
import { useEffect, useState } from 'react';
import { useReferenceService } from '@/hooks/use-services';
import { Loader2 } from 'lucide-react';

interface ShareReferenceListModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  referenceId: string;
}
export default function ShareReferenceListModal({
  open,
  onOpenChange,
  referenceId,
}: ShareReferenceListModalProps) {
  const [emailInvite, setEmailInvite] = useState<string>('');
  const [emailError, setEmailError] = useState<string | null>(null);
  const referenceService = useReferenceService();
  const [loading, setLoading] = useState<boolean>(false);
  const [link, setLink] = useState<string>('');

  useEffect(() => {
    const baseUrl =
      process.env.NEXT_PUBLIC_APP_URL ||
      (typeof window !== 'undefined' ? window.location.origin : '');
    setLink(`${baseUrl}/share/${referenceId}`);
  }, [referenceId]);

  const handleCopyLink = () => {
    navigator.clipboard.writeText(link);
    toastSuccess('Link copied.');
  };
  const handleSend = async () => {
    try {
      setLoading(true);
      await referenceService.shareReferenceList(referenceId, [emailInvite]);
      toastSuccess('Email invite sent.');
    } catch {
      toastError('Failed to send email invite');
    } finally {
      setLoading(false);
    }
  };
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[28rem] px-0">
        <DialogHeader className="px-6">
          <DialogTitle>Share reference list</DialogTitle>
          <DialogDescription>
            Invite viewers or share with a link.
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-6 border-t border-b p-6">
          <div className="relative flex items-center gap-5">
            <Input
              id="email-invite"
              type="email"
              placeholder="Invite viewers by email"
              value={emailInvite}
              onChange={e => {
                setEmailInvite(e.target.value);
                if (emailError) {
                  setEmailError(null);
                }
              }}
              onBlur={() => {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailInvite) {
                  setEmailError('Email is required');
                } else if (!emailRegex.test(emailInvite)) {
                  setEmailError('Please enter a valid email address');
                } else {
                  setEmailError(null);
                }
              }}
              className={
                emailError ? 'border-red-500 focus:border-red-500' : ''
              }
            />
            {emailError && (
              <p className="absolute -bottom-5.5 left-0 text-sm text-red-500 mt-1">
                {emailError}
              </p>
            )}
            <Button
              onClick={handleSend}
              disabled={!emailInvite.length || !!emailError || loading}
              size="lg"
              type="button"
            >
              {loading ? <Loader2 className="w-4 h-4 animate-spin" /> : 'Send'}
            </Button>
          </div>
          <div className="flex items-center gap-2">
            <Separator className="flex-1" />
            <span className="text-xs">Or share via link</span>
            <Separator className="flex-1" />
          </div>

          <div className="flex gap-2 items-center">
            <Input value={link} readOnly className="text-sm truncate" />
            <Button
              variant="outline"
              onClick={handleCopyLink}
              className="whitespace-nowrap flex items-center gap-1"
              size="lg"
            >
              <CopyIcon className="w-4 h-4" /> Copy Link
            </Button>
          </div>
        </div>
        <DialogFooter className="justify-between px-6 flex-col">
          <Button
            onClick={() => {
              window.open(link, '_blank', 'noopener,noreferrer');
            }}
            size="lg"
            type="button"
            variant="outline"
          >
            <PlayArrowIcon /> Preview
          </Button>
          <div className="flex items-center gap-2">
            <Button
              onClick={() => onOpenChange(false)}
              type="button"
              variant="outline"
              size="lg"
              className="flex-1"
            >
              Cancel
            </Button>
            <Button
              className="flex-1"
              size="lg"
              type="button"
              onClick={() => onOpenChange(false)}
            >
              Done
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
