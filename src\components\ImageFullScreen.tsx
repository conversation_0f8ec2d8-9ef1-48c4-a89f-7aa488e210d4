'use client';

import * as React from 'react';
import Image from 'next/image';
import { X } from 'lucide-react';

import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPagination,
  CarouselPrevious,
} from './ui/carousel';
import { Button } from './ui/button';
import { Skeleton } from './ui/skeleton';
import type { Comment, Photo } from '@/types';
import { CommentOnImage } from '@/components/comment/CommentOnImage';
import { useState } from 'react';
import { Role } from '@/types/enum';
import { DislikeIcon, LikeIcon, ZoomInMapIcon } from '@/lib/icons';
import { Badge } from './ui/badge';

interface ImageFullScreenProps {
  images: Photo[];
  open: boolean;
  onOpenChange: (open: boolean) => void;
  startIndex?: number;
  loading?: boolean;
  isExpanded?: boolean;
  onBack?: () => void;
  onToggleZoom?: () => void;
}

const mockComments: Comment[] = [
  {
    id: '1',
    content: 'Great shot!',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    author: {
      id: 'user1',
      firstName: 'Alice',
      lastName: 'Smith',
      email: '<EMAIL>',
      avatar: 'https://github.com/shadcn.png',
      role: Role.SuperAdmin,
    },
  },
  {
    id: '2',
    content: 'Love this place.',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    author: {
      id: 'user2',
      firstName: 'Bob',
      lastName: 'Johnson',
      email: '<EMAIL>',
      avatar: 'https://github.com/shadcn.png',
      role: Role.Scout,
    },
  },
];

export default function ImageFullScreen({
  open,
  images,
  onOpenChange,
  startIndex = 0,
  loading = false,
  isExpanded = true,
  onBack,
  onToggleZoom,
}: ImageFullScreenProps) {
  const [activeIndex, setActiveIndex] = React.useState<number>(0);
  const [comments] = useState<Comment[]>(mockComments);
  const [isCreating] = useState<boolean>(false);
  const [isLiked, setIsLiked] = useState(false);
  const [isDisliked, setIsDisliked] = useState(false);

  if (!open) return null;

  if (loading) {
    return <ImageFullScreenSkeleton />;
  }

  return (
    <div className="fixed inset-0 z-50 flex bg-neutral-800">
      {/* Left: image viewer */}
      <div className="flex-1 flex flex-col items-center py-11">
        {/* Carousel */}
        <div
          className={`flex-1 flex flex-col items-center px-4 pb-8 gap-6 w-full h-full ${isExpanded ? '' : 'space-y-4'}`}
        >
          {isExpanded && (
            <Button
              onClick={onBack}
              className="rounded-full absolute right-12 top-12 bg-black opacity-50 hover:bg-black/80"
            >
              <X />
            </Button>
          )}
          <div className="rounded-full bg-neutral-800 px-4 py-2 text-white">
            {activeIndex + 1} of {images.length}
          </div>
          <Carousel
            onSelectedIndex={setActiveIndex}
            startIndex={startIndex}
            className="relative w-[55%]"
          >
            <CarouselContent>
              {images.map((image, index) => (
                <CarouselItem key={index}>
                  <div className="relative w-full h-full aspect-[900/600]">
                    <Image
                      src={image.url}
                      alt="location view"
                      fill
                      className="w-full h-full object-cover rounded-lg"
                    />
                    {image.tags && image.tags.length > 0 && (
                      <div className="absolute top-4 right-4 flex flex-wrap gap-2 justify-end z-10">
                        {image.tags.map((tag, tagIndex) => (
                          <Badge
                            key={tag.id ?? tagIndex}
                            variant="secondary"
                            className={`${tag.color ? `bg-${tag.color} text-white` : ''}`}
                          >
                            {tag.name}
                          </Badge>
                        ))}
                      </div>
                    )}
                  </div>
                </CarouselItem>
              ))}
            </CarouselContent>
            {onToggleZoom && (
              <ZoomInMapIcon
                onClick={() => onToggleZoom?.()}
                className="absolute right-6 bottom-44 cursor-pointer"
              />
            )}
            <CarouselPrevious className="absolute top-1/2 -translate-y-26 -left-24 z-10 w-8 md:w-12 md:h-12 h-8 bg-black opacity-50 hover:bg-black/80 border-none" />
            <CarouselNext className="absolute top-1/2 -translate-y-26 -right-24 z-10 w-8 md:w-12 md:h-12 h-8 bg-black opacity-50 hover:bg-black/80 border-none" />
            {/* Like / Dislike buttons (Figma style) */}
            <div className="flex items-center justify-center my-6 gap-4">
              <Button
                variant="outline"
                className="flex h-8 w-8 items-center justify-center rounded-full border border-[#A3A3A3] bg-transparent text-[#525252] hover:bg-[#E5E5E5]"
                onClick={() => {
                  setIsLiked(prev => !prev);
                  if (!isDisliked) setIsDisliked(false);
                }}
              >
                <LikeIcon
                  color={isLiked ? 'white' : '#525252'}
                  className="h-4 w-4"
                />
              </Button>
              <Button
                variant="outline"
                className="flex h-8 w-8 items-center justify-center rounded-full border border-[#A3A3A3] bg-transparent text-[#525252] hover:bg-[#E5E5E5]"
                onClick={() => {
                  setIsDisliked(prev => !prev);
                  if (!isLiked) setIsLiked(false);
                }}
              >
                <DislikeIcon
                  color={isDisliked ? 'white' : '#525252'}
                  className="h-4 w-4"
                />
              </Button>
            </div>
            <div className="w-full flex justify-center">
              <CarouselPagination className="w-fit" />
            </div>
          </Carousel>
        </div>
      </div>

      {/* Right: comment sidebar (optional) */}
      {!isExpanded && (
        <CommentOnImage
          onBack={onBack ?? (() => {})}
          imageIndex={activeIndex}
          onCloseSidebar={() => onOpenChange(true)}
          comments={comments}
          loading={loading}
          isCreating={isCreating}
          onComment={() => {}}
          onDelete={() => {}}
          onEdit={() => {}}
        />
      )}
    </div>
  );
}
export function ImageFullScreenSkeleton() {
  return (
    <div className="fixed inset-0 z-50 flex bg-neutral-800">
      {/* Left: viewer skeleton */}
      <div className="flex-1 flex flex-col items-center py-11">
        {/* Carousel container skeleton */}
        <div className="flex-1 flex flex-col items-center px-4 pb-8 gap-6 w-full h-full">
          {/* Counter badge skeleton */}
          <Skeleton className="h-8 w-20 rounded-full bg-neutral-700" />

          {/* Carousel skeleton */}
          <div className="relative w-[55%] aspect-[900/600]">
            <Skeleton className="w-full h-full rounded-lg" />
            {/* Tag badges skeleton */}
            <div className="absolute top-4 right-4 flex flex-wrap gap-2 justify-end z-10">
              <Skeleton className="h-6 w-20 rounded-md" />
            </div>
            {/* Navigation arrows skeleton */}
            <Skeleton className="absolute top-1/2 -left-24 h-8 w-8 md:h-12 md:w-12 rounded-full bg-black/50" />
            <Skeleton className="absolute top-1/2 -right-24 h-8 w-8 md:h-12 md:w-12 rounded-full bg-black/50" />
            {/* Zoom icon skeleton */}
            <Skeleton className="absolute right-6 bottom-6 h-6 w-6 rounded" />
          </div>

          {/* Like/Dislike buttons skeleton */}
          <div className="flex items-center justify-center my-6 gap-4">
            <Skeleton className="h-8 w-8 rounded-full border border-[#A3A3A3]" />
            <Skeleton className="h-8 w-8 rounded-full border border-[#A3A3A3]" />
          </div>
        </div>
      </div>

      {/* Right: comment sidebar skeleton */}
      <div className="hidden md:flex w-[22rem] flex-col bg-white">
        <div className="flex items-center justify-between px-4 py-4 border-b">
          <Skeleton className="h-4 w-32" />
          <Skeleton className="h-4 w-4 rounded-full" />
        </div>

        <div className="flex-1 p-4 space-y-4 overflow-hidden">
          <Skeleton className="h-16 w-full rounded-md" />
          <Skeleton className="h-16 w-full rounded-md" />
          <Skeleton className="h-16 w-full rounded-md" />
        </div>

        <div className="px-4 py-4 border-t space-y-3">
          <Skeleton className="h-20 w-full rounded-md" />
          <Skeleton className="h-10 w-full rounded-md" />
        </div>
      </div>
    </div>
  );
}
