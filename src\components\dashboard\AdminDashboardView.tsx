'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { useAuth } from '@/contexts/auth-context';
import { ArrowRightIcon, LocationIcon, TagIcon, UsersIcon } from '@/lib/icons';
import { Plus } from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';
import { Routes } from '@/lib/routes';
import Link from 'next/link';
import AddUserModal from '@/components/features/users/AddUserModal';
import UserCard from '@/components/features/users/UserCard';
import { useRouter } from 'next/navigation';
import { AdminDashboardData } from '@/types/dashboard';
import { useDashboardService } from '@/hooks/use-services';
import { toastError } from '@/lib/toast';
import { Body, Heading } from '@/components/ui/typography';
import LocationCardInline from '@/components/features/locations/LocationCardInline';
import { AdminDashboardLoading } from './AdminDashboardLoading';

export function AdminDashboardView() {
  const { user } = useAuth();
  const router = useRouter();
  const dashboardService = useDashboardService();
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [dashboardData, setDashboardData] = useState<AdminDashboardData>();
  const [isAddUserModalOpen, setIsAddUserModalOpen] = useState(false);

  const fetchData = useCallback(async () => {
    try {
      setIsLoading(true);
      const response = await dashboardService.getDashboard();
      setDashboardData(response);
    } catch {
      toastError('Failed to get dashboard data');
    } finally {
      setIsLoading(false);
    }
  }, [dashboardService]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return (
    <>
      <div className="flex justify-center">
        <div className="space-y-6 py-6 px-4 sm:px-8 w-full xl:max-w-[70.5rem] 2xl:mx-auto 2xl:px-0">
          <div className="flex flex-col sm:flex-row items-end justify-between gap-2">
            <div>
              <Heading level={3} className="leading-9 text-black mb-2">
                Good to see you, {user?.firstName ?? ''}
              </Heading>
              <Body>Manage users, locations, and tags all in one place.</Body>
            </div>
            <Button
              onClick={() => router.push(Routes.ADMIN_TAGS)}
              className="w-full sm:w-[12.5rem] h-9"
            >
              <TagIcon color="#fff" /> Manage tags
            </Button>
          </div>
          {isLoading ? (
            <AdminDashboardLoading />
          ) : (
            <div className="flex flex-1">
              <div className="w-full">
                <div className="grid gap-8 mb-8 grid-cols-1 md:grid-cols-2">
                  <Card className="shadow-none py-0 col-span-1 border-card-border">
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm text-foreground leading-5">
                            Total Users
                          </p>
                          <p className="text-2xl text-primary-500 leading-8 mt-1">
                            {dashboardData?.totalUsers.toLocaleString() ?? 0}
                          </p>
                        </div>
                        <div className="w-10 h-10 bg-primary-50 flex items-center justify-center rounded-lg">
                          <UsersIcon color="#525252" className="w-5 h-4" />
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                  <Card className="shadow-none py-0 border-card-border">
                    <CardContent className="p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm text-foreground leading-5">
                            Active Locations
                          </p>
                          <p className="text-2xl text-primary-500 leading-8 mt-1">
                            {dashboardData?.totalLocations?.toLocaleString() ??
                              0}
                          </p>
                        </div>
                        <div className="w-10 h-10 bg-primary-50 flex items-center justify-center rounded-lg">
                          <LocationIcon color="#525252" className="w-3 h-4" />
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                  <Card className="shadow-none py-0 col-span-1 border-card-border">
                    <CardContent className="p-6 flex flex-col justify-between h-full">
                      <div>
                        <div className="flex items-center justify-between mb-6">
                          <div className="flex items-center">
                            <div className="w-10 h-10 bg-primary-50 flex items-center justify-center rounded-lg">
                              <UsersIcon color="#525252" className="w-5 h-4" />
                            </div>
                            <p className="text-xl text-header leading-7 ml-3">
                              Users
                            </p>
                          </div>
                          <Badge className="bg-primary-50" variant="secondary">
                            {(() => {
                              const requestCount =
                                dashboardData?.pendingUserRequests ?? 0;
                              return `${requestCount} ${requestCount > 1 ? 'requests' : 'request'}`;
                            })()}
                          </Badge>
                        </div>
                        {!dashboardData?.recentUsers.length ? (
                          <div className="h-[15.5rem] flex flex-col items-center justify-center gap-4">
                            <div className="w-29 h-29 bg-[#E5E7EB] flex items-center justify-center rounded-full">
                              <UsersIcon
                                color="#D1D5DB"
                                className="w-15 h-13"
                              />
                            </div>
                            No users yet.
                          </div>
                        ) : (
                          <div className="flex flex-col gap-4">
                            {dashboardData.recentUsers.map((user, index) => (
                              <UserCard key={user.id || index} user={user} />
                            ))}
                          </div>
                        )}
                      </div>

                      <div className="flex items-center justify-between mt-7">
                        <Button variant="ghost">
                          <Link
                            href={Routes.ADMIN_USERS}
                            className="flex items-center justify-center space-x-2"
                          >
                            <p className="text-primary-300">View All Users</p>
                            <ArrowRightIcon className="w-4 h-4" />
                          </Link>
                        </Button>
                        <Button
                          onClick={() => setIsAddUserModalOpen(true)}
                          variant="default"
                          className="w-[11.6875rem] h-9"
                        >
                          <Plus /> Create New User
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                  <Card className="shadow-none py-0 border-card-border">
                    <CardContent className="p-6 flex flex-col justify-between h-full">
                      <div>
                        <div className="flex items-center mb-6">
                          <div className="w-10 h-10 bg-[#f6edf7] flex items-center justify-center rounded-lg">
                            <LocationIcon color="#525252" className="w-3 h-4" />
                          </div>
                          <p className="text-xl text-header leading-7 ml-3">
                            Locations
                          </p>
                        </div>
                        {!dashboardData?.recentLocations.length ? (
                          <div className="h-[15.5rem] flex flex-col items-center justify-center gap-4">
                            <div className="w-29 h-29 bg-[#E5E7EB] flex items-center justify-center rounded-full">
                              <LocationIcon
                                color="#D1D5DB"
                                className="w-15 h-13"
                              />
                            </div>
                            No locations yet.
                          </div>
                        ) : (
                          dashboardData.recentLocations.map(
                            (location, index) => (
                              <LocationCardInline
                                key={index}
                                location={location}
                              />
                            )
                          )
                        )}
                      </div>
                      <div className="flex items-center justify-between mt-7">
                        <Button variant="ghost">
                          <Link
                            href={Routes.ADMIN_LOCATIONS}
                            className="flex items-center justify-center space-x-2"
                          >
                            <p className="text-primary-300">
                              View All Locations
                            </p>
                            <ArrowRightIcon className="w-4 h-4" />
                          </Link>
                        </Button>

                        <Button
                          variant="default"
                          className="w-[11.6875rem] h-9"
                        >
                          <Plus />
                          <Link
                            href={Routes.ADMIN_CREATE_LOCATION}
                            className="flex items-center justify-center space-x-2"
                          >
                            <p>Submit Location</p>
                          </Link>
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
      <AddUserModal
        open={isAddUserModalOpen}
        onOpenChange={setIsAddUserModalOpen}
      />
    </>
  );
}
