'use client';

import { cn } from '@/lib/utils';

interface ProcessBarProps {
  currentStep: number;
  steps: { label: string; value: string; flex?: string }[];
  onStepClick?: (stepValue: string) => void;
}

export function ProcessBar({
  currentStep,
  steps,
  onStepClick,
}: ProcessBarProps) {
  const handleStepClick = (stepValue: string, stepNumber: number) => {
    // Only allow clicking on completed or active steps (not future steps)
    if (stepNumber <= currentStep && onStepClick) {
      onStepClick(stepValue);
    }
  };

  return (
    <div className="flex h-8 w-full items-center">
      {steps.map((step, index) => {
        const stepNumber = index + 1;
        const isActive = stepNumber === currentStep;
        const isCompleted = stepNumber < currentStep;
        const isClickable = stepNumber <= currentStep && !!onStepClick;

        return (
          <div
            key={step.value}
            className={cn('flex items-center', step.flex ?? '')}
          >
            {/* Step Circle */}
            <button
              type="button"
              onClick={() => handleStepClick(step.value, stepNumber)}
              disabled={!isClickable}
              className={`h-8 w-8 shrink-0 rounded-full flex items-center justify-center transition-colors ${
                isActive
                  ? 'bg-primary'
                  : isCompleted
                    ? 'bg-gray-300'
                    : 'bg-neutral-200'
              } ${
                isClickable
                  ? 'cursor-pointer hover:opacity-80'
                  : 'cursor-not-allowed'
              }`}
            >
              <p
                className={`text-sm font-normal leading-normal ${
                  isActive ? 'text-gray-50' : 'text-gray-500'
                }`}
              >
                {stepNumber}
              </p>
            </button>

            {/* Step Label */}
            <button
              type="button"
              onClick={() => handleStepClick(step.value, stepNumber)}
              disabled={!isClickable}
              className={`ml-2 shrink-0 text-sm font-normal leading-5 whitespace-nowrap text-left transition-colors ${
                isActive ? 'text-[#5b0677]' : 'text-gray-500'
              } ${
                isClickable
                  ? 'cursor-pointer hover:text-primary'
                  : 'cursor-not-allowed'
              }`}
            >
              {step.label}
            </button>

            {/* Connector Line - positioned after the text */}
            {index < steps.length - 1 && (
              <div className="mx-2 h-[1px] flex-1 min-w-[16px] bg-neutral-300" />
            )}
          </div>
        );
      })}
    </div>
  );
}
