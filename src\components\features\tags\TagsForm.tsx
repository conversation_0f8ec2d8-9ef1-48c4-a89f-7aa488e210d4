import { useMemo, useState } from 'react';
import { Label } from '@/components/ui/label';
import {
  TAG_SIZE_OPTIONS,
  PROPERTY_CATEGORY,
  TAG_TYPE_OPTIONS,
} from '@/types/constant';
import { PropertyType, TagType } from '@/types/enum';
import { Home, Building2, TreePineIcon } from 'lucide-react';
import { cn } from '@/lib/utils';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { TagOption } from '@/types/tag';
import { Button } from '@/components/ui/button';
import { SearchableSelect } from '@/components/ui/searchable-select';
import { useCreateTag, useDeleteTag, useUpdateTag } from '@/hooks/api/useTags';

interface TagsData {
  propertyType?: PropertyType | '';
  tags: Partial<Record<TagType, string[]>>;
  size: string;
}

interface TagsProps {
  data: TagsData;
  onChange: (data: Partial<TagsData>) => void;
  tagsByType: Partial<Record<TagType, TagOption[]>>;
}

const PROPERTY_TYPE_OPTIONS = [
  {
    value: PropertyType.RESIDENTIAL,
    label: TAG_TYPE_OPTIONS[PropertyType.RESIDENTIAL],
    icon: Home,
  },
  {
    value: PropertyType.COMMERCIAL,
    label: TAG_TYPE_OPTIONS[PropertyType.COMMERCIAL],
    icon: Building2,
  },
  {
    value: PropertyType.PUBLIC,
    label: TAG_TYPE_OPTIONS[PropertyType.PUBLIC],
    icon: TreePineIcon,
  },
] as const;

const SIZE_LABELS: Record<string, string> = {
  small: 'Small (< 1,000 sq ft)',
  medium: 'Medium (1,000-5,000 sq ft)',
  large: 'Large (> 5,000 sq ft)',
};

// Helper function to format category enum to display label
export const formatCategoryLabel = (type: TagType): string => {
  const labelMap: Record<TagType, string> = {
    [TagType.BUILDING_TYPE]: 'Building type',
    [TagType.STRUCTURE_TYPE]: 'Structure type',
    [TagType.STYLE]: 'Style',
    [TagType.INTERIOR_FEATURE]: 'Interior feature',
    [TagType.EXTERIOR_FEATURE]: 'Exterior feature',
    [TagType.FUNCTION_TYPE]: 'Function type',
    [TagType.SPACE_TYPE]: 'Space type',
  };
  return labelMap[type] || type;
};

// Helper function to get placeholder text for category dropdowns
const getCategoryPlaceholder = (type: TagType): string => {
  const placeholderMap: Record<TagType, string> = {
    [TagType.BUILDING_TYPE]:
      "Select building type (e.g. 'Apartment', 'Townhouse')",
    [TagType.STRUCTURE_TYPE]:
      "Select structure type (e.g. 'Single story', 'Duplex')",
    [TagType.STYLE]: "Select style (e.g. 'Modern', 'Rustic')",
    [TagType.INTERIOR_FEATURE]: 'Select interior feature',
    [TagType.EXTERIOR_FEATURE]: 'Select exterior feature',
    [TagType.FUNCTION_TYPE]: 'Select function type',
    [TagType.SPACE_TYPE]: 'Select space type',
  };
  return placeholderMap[type] || 'Select option';
};

// Helper function to get helper text for category dropdowns
const getCategoryHelperText = (type: TagType): string | null => {
  const helperTextMap: Record<TagType, string | null> = {
    [TagType.BUILDING_TYPE]: null,
    [TagType.STRUCTURE_TYPE]: "Indicate the building's layout.",
    [TagType.STYLE]: 'Select multiple styles.',
    [TagType.INTERIOR_FEATURE]: null,
    [TagType.EXTERIOR_FEATURE]: null,
    [TagType.FUNCTION_TYPE]: 'Select how this space is used or operated.',
    [TagType.SPACE_TYPE]: null,
  };
  return helperTextMap[type] || null;
};

interface CategorySelectProps {
  type: TagType;
  options: TagOption[];
  value: string | string[];
  onChange: (value: string | string[]) => void;
  propertyType?: PropertyType | '';
  onOptionAdded?: (newOption: TagOption) => void;
  onOptionUpdated?: (updatedOption: TagOption) => void;
  onOptionDeleted?: (deletedOption: TagOption) => void;
}

function CategorySelect({
  type,
  options,
  value,
  onChange,
  propertyType,
  onOptionAdded,
  onOptionUpdated,
  onOptionDeleted,
}: CategorySelectProps) {
  const createTagMutation = useCreateTag();
  const updateTagMutation = useUpdateTag();
  const deleteTagMutation = useDeleteTag();
  const [isCreating, setIsCreating] = useState(false);

  const handleCreateNew = async (name: string) => {
    if (!propertyType) return;

    try {
      setIsCreating(true);
      const newTag = await createTagMutation.mutateAsync({
        name: name.trim(),
        type: type as TagType,
        propertyType: propertyType,
      });

      // Convert created tag to TagOption format
      const newOption: TagOption = {
        value: newTag.id,
        label: newTag.name,
        color: newTag.color,
        type: newTag.type as TagType,
      };

      // Call onChange with the new tag ID
      onChange([newTag.id]);

      // Notify parent component about the new option
      if (onOptionAdded) {
        onOptionAdded(newOption);
      }
    } catch (error) {
      // Error handling is done in the mutation
      console.error('Failed to create tag:', error);
    } finally {
      setIsCreating(false);
    }
  };

  const handleUpdate = async (value: string, newLabel: string) => {
    try {
      await updateTagMutation.mutateAsync({
        id: value,
        data: { name: newLabel.trim() },
      });
      // Update the option in the options array
      const updatedOptions = options.map(option =>
        option.value === value ? { ...option, label: newLabel.trim() } : option
      );
      // Update the option in the options array
      const updatedOption = updatedOptions.find(
        option => option.value === value
      );
      if (updatedOption) {
        onOptionUpdated?.(updatedOption);
      }
    } catch (error) {
      console.error('Failed to update tag:', error);
    }
  };

  const handleDelete = async (value: string) => {
    try {
      await deleteTagMutation.mutateAsync(value);
      onChange(value);
      // Delete the option from the options array
      const deletedOption = options.find(option => option.value === value);
      if (deletedOption) {
        onOptionDeleted?.(deletedOption);
      }
    } catch (error) {
      console.error('Failed to delete tag:', error);
    }
  };

  return (
    <div className="space-y-2">
      <Label className="text-sm font-medium leading-5 text-header">
        {formatCategoryLabel(type)}
      </Label>
      <SearchableSelect
        options={options}
        value={Array.isArray(value) ? value : value ? [value] : []}
        onValueChange={onChange}
        placeholder={getCategoryPlaceholder(type)}
        searchPlaceholder={`Search ${formatCategoryLabel(type)}...`}
        emptyText={`No ${formatCategoryLabel(type)} found.`}
        onCreateNew={handleCreateNew}
        disabled={isCreating || !propertyType}
        multiple={type === TagType.STYLE}
        editable={true}
        onEdit={handleUpdate}
        onDelete={handleDelete}
      />
      {getCategoryHelperText(type) && (
        <p className="text-sm text-[#adaebc] leading-6">
          {getCategoryHelperText(type)}
        </p>
      )}
    </div>
  );
}

export default function TagsForm({ data, onChange, tagsByType }: TagsProps) {
  const [localTagsByType, setLocalTagsByType] =
    useState<Partial<Record<TagType, TagOption[]>>>(tagsByType);

  // Update options when props change
  useMemo(() => {
    setLocalTagsByType(tagsByType);
  }, [tagsByType]);

  const handleOptionAdded = (type: TagType, newOption: TagOption) => {
    setLocalTagsByType(prev => ({
      ...prev,
      [type]: [...(prev[type] || []), newOption],
    }));
  };

  // Get relevant categories based on selected property type
  const relevantTypes = useMemo(() => {
    if (!data.propertyType) return [];
    return PROPERTY_CATEGORY[data.propertyType] || [];
  }, [data.propertyType]);

  // Get selected value(s) for a category
  const getSelectedValue = (type: TagType): string | string[] => {
    const tagIds = data.tags?.[type] || [];
    return tagIds;
  };

  const handlePropertyTypeChange = (propertyType: PropertyType) => {
    const newPropertyType =
      data.propertyType === propertyType ? '' : propertyType;
    onChange({
      propertyType: newPropertyType,
      // Reset tags when property type changes
      tags: {},
    });
  };

  const handleTypeChange = (type: TagType, value: string | string[]) => {
    const newTags = {
      ...data.tags,
      [type]: Array.isArray(value) ? value : value ? [value] : [],
    };
    onChange({ tags: newTags });
  };

  const handleStyleChange = (value: string | string[]) => {
    // Style supports multiple selection
    const styleValues = Array.isArray(value) ? value : value ? [value] : [];
    const newTags = {
      ...data.tags,
      [TagType.STYLE]: styleValues,
    };
    onChange({ tags: newTags });
  };

  const handleSizeChange = (size: string) => {
    onChange({ size });
  };

  const handleOptionUpdated = (updatedOption: TagOption) => {
    setLocalTagsByType(prev => ({
      ...prev,
      [updatedOption.type as TagType]: prev[updatedOption.type as TagType]?.map(
        option =>
          option.value === updatedOption.value ? updatedOption : option
      ),
    }));
  };

  const handleOptionDeleted = (deletedOption: TagOption) => {
    setLocalTagsByType(prev => ({
      ...prev,
      [deletedOption.type as TagType]: prev[
        deletedOption.type as TagType
      ]?.filter(option => option.value !== deletedOption.value),
    }));

    // If deleted option is in the data.tags, remove it from the data.tags
    if (
      data.tags?.[deletedOption.type as TagType]?.includes(deletedOption.value)
    ) {
      onChange({
        tags: {
          ...data.tags,
          [deletedOption.type as TagType]: data.tags[
            deletedOption.type as TagType
          ]?.filter(id => id !== deletedOption.value),
        },
      });
    }
  };

  return (
    <div className="space-y-4">
      {/* Property Type Selection */}
      <div className="space-y-4">
        <Label className="text-sm font-medium leading-5 text-header">
          Property type
        </Label>
        <div className="flex flex-wrap gap-2">
          {PROPERTY_TYPE_OPTIONS.map(option => {
            const Icon = option.icon;
            const isSelected = data.propertyType === option.value;
            return (
              <Button
                key={option.value}
                variant="outline"
                onClick={() => handlePropertyTypeChange(option.value)}
                className={cn(
                  'flex h-[55px] items-center gap-2 rounded-lg border px-4 py-[13px] transition-colors',
                  isSelected
                    ? 'border-2'
                    : 'border border-primary-100 hover:bg-primary-50 hover:border-primary-300'
                )}
              >
                <Icon className="h-[17px] w-[17px] text-primary" />
                <span className="text-sm font-medium leading-5 text-primary">
                  {option.label}
                </span>
              </Button>
            );
          })}
        </div>
      </div>

      {/* Show categories only after property type is selected */}
      {data.propertyType && relevantTypes.length > 0 && (
        <div className="space-y-4">
          {/* Render all relevant categories in the order defined in PROPERTY_CATEGORY */}
          {relevantTypes.map(type => {
            // Get options for this type from tagsByType
            const options = localTagsByType[type] || [];

            const selectedValue = getSelectedValue(type);
            // For all types including Style, use array format for multiple selection
            const value = Array.isArray(selectedValue)
              ? selectedValue
              : selectedValue
                ? [selectedValue]
                : [];

            const onChange =
              type === TagType.STYLE
                ? handleStyleChange
                : (val: string | string[]) => handleTypeChange(type, val);

            const onOptionAdded = (newOption: TagOption) =>
              handleOptionAdded(type, newOption);

            return (
              <CategorySelect
                key={type}
                type={type}
                options={options}
                value={value}
                onChange={onChange}
                propertyType={data.propertyType}
                onOptionAdded={onOptionAdded}
                onOptionUpdated={handleOptionUpdated}
                onOptionDeleted={handleOptionDeleted}
              />
            );
          })}

          {/* Size */}
          <div className="space-y-3">
            <Label className="text-sm font-medium leading-5 text-header">
              Size
            </Label>
            <RadioGroup
              value={data.size || ''}
              onValueChange={handleSizeChange}
            >
              <div className="flex flex-col gap-2">
                {TAG_SIZE_OPTIONS.map(size => (
                  <div key={size} className="flex items-center gap-2">
                    <RadioGroupItem value={size} id={size} />
                    <Label
                      htmlFor={size}
                      className="text-sm font-medium leading-[14px] text-header cursor-pointer"
                    >
                      {SIZE_LABELS[size] || size}
                    </Label>
                  </div>
                ))}
              </div>
            </RadioGroup>
          </div>
        </div>
      )}
    </div>
  );
}
