'use client';

import { useState, useMemo } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Routes } from '@/lib/routes';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { toastDismiss, toastError, toastLoading } from '@/lib/toast';
import { Heading, Body } from '@/components/ui/typography';
import { EyeOff, Eye } from 'lucide-react';
import { signUpSchema, type SignUpFormData } from '@/lib/validations';
import { CircleCheckOutlineIcon, CircleXOutlineIcon } from '@/lib/icons';
import { authService } from '@/lib/services/auth-service';
import { handleError } from '@/lib/error-handler';

export default function SignUpPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [signUpSuccess, setSignUpSuccess] = useState(false);
  const [userEmail, setUserEmail] = useState('');

  const form = useForm<SignUpFormData>({
    resolver: zodResolver(signUpSchema),
    mode: 'onChange',
    defaultValues: {
      username: '',
      email: '',
      firstName: '',
      lastName: '',
      password: '',
      confirmPassword: '',
      acceptTerms: false,
    },
  });

  const password = form.watch('password');

  const getPasswordRequirements = (passwordValue: string) => {
    const minLength = 8;
    const hasUpperCase = /[A-Z]/.test(passwordValue);
    const hasLowerCase = /[a-z]/.test(passwordValue);
    const hasNumbers = /\d/.test(passwordValue);
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(passwordValue);

    return [
      {
        text: 'At least 8 characters',
        met: passwordValue.length >= minLength,
      },
      {
        text: 'One uppercase letter',
        met: hasUpperCase,
      },
      {
        text: 'One lowercase letter',
        met: hasLowerCase,
      },
      {
        text: 'One number',
        met: hasNumbers,
      },
      {
        text: 'One special character',
        met: hasSpecialChar,
      },
    ];
  };

  const passwordRequirements = useMemo(
    () => getPasswordRequirements(password),
    [password]
  );

  const onSubmit = async (data: SignUpFormData) => {
    setLoading(true);
    const toastId = toastLoading('Creating your account...');

    try {
      await authService.register({
        email: data.email,
        password: data.password,
        firstName: data.firstName,
        lastName: data.lastName,
        username: data.username,
      });

      setUserEmail(data.email);
      setSignUpSuccess(true);
    } catch (error) {
      console.error('Error signing up:', error);

      // Check if it's a username conflict error (409 status)
      if (
        error instanceof Error &&
        'response' in error &&
        error.response &&
        typeof error.response === 'object' &&
        'data' in error.response &&
        error.response.data &&
        typeof error.response.data === 'object' &&
        'statusCode' in error.response.data &&
        error.response.data.statusCode === 409 &&
        'message' in error.response.data &&
        typeof error.response.data.message === 'string' &&
        (error.response.data.message.toLowerCase().includes('user_name') ||
          error.response.data.message.toLowerCase().includes('username'))
      ) {
        // Set error on username field
        const usernameErrorMsg =
          'This username is already taken. Please choose another one.';
        form.setError('username', {
          type: 'manual',
          message: usernameErrorMsg,
        });
        toastError(usernameErrorMsg);
      } else {
        // Handle other errors
        const { message } = handleError(
          error,
          'Failed to create account. Please try again.'
        );
        toastError(message);
      }
    } finally {
      toastDismiss(toastId);
      setLoading(false);
    }
  };

  if (signUpSuccess) {
    return (
      <div className="w-full h-screen overflow-auto flex items-center justify-center">
        <div className="space-y-12 max-w-[28.5rem] w-full h-full flex flex-col items-center justify-center">
          <div className="text-center space-y-2">
            <Heading
              level={3}
              className="text-header font-semibold text-center"
            >
              Almost there!
            </Heading>
            <Body className="text-center max-w-[25rem] mx-auto">
              Check your inbox of{' '}
              <span className="font-medium text-header">{userEmail}</span> and
              click the link we sent to confirm your email and activate your
              account.
            </Body>
          </div>

          <Button
            type="button"
            className="w-full"
            color="primary"
            onClick={() => router.push(Routes.SIGN_IN)}
          >
            Back to Log in
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full h-screen overflow-auto">
      <div className="flex items-center justify-center min-h-full py-6">
        <div className="space-y-6 max-w-[28.5rem] w-full">
          <div className="text-center">
            <div className="space-y-2">
              <Heading
                level={4}
                className="text-header font-semibold text-center"
              >
                Set up your account
              </Heading>
              <Body className="text-center max-w-[25rem] mx-auto">
                Let&apos;s get your account ready! Add your basic details and
                choose a strong password to keep it secure.
              </Body>
            </div>
          </div>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="username"
                render={({ field }) => (
                  <FormItem className="gap-1.5">
                    <FormLabel>Username</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <span className="absolute left-3 top-1/2 -translate-y-1/2 text-foreground">
                          @
                        </span>
                        <Input
                          type="text"
                          placeholder="Enter your username"
                          className="pl-8"
                          {...field}
                          disabled={loading}
                        />
                      </div>
                    </FormControl>
                    <FormMessage className="text-red-500" />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem className="gap-1.5">
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input
                        type="email"
                        placeholder="Enter your email"
                        {...field}
                        disabled={loading}
                      />
                    </FormControl>
                    <FormMessage className="text-red-500" />
                  </FormItem>
                )}
              />

              <div className="flex gap-4">
                <FormField
                  control={form.control}
                  name="firstName"
                  render={({ field }) => (
                    <FormItem className="flex-1 gap-1.5">
                      <FormLabel>First name</FormLabel>
                      <FormControl>
                        <Input
                          type="text"
                          placeholder="Enter your first name"
                          {...field}
                          disabled={loading}
                        />
                      </FormControl>
                      <FormMessage className="text-red-500" />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="lastName"
                  render={({ field }) => (
                    <FormItem className="flex-1 gap-1.5">
                      <FormLabel>Last name</FormLabel>
                      <FormControl>
                        <Input
                          type="text"
                          placeholder="Enter your last name"
                          {...field}
                          disabled={loading}
                        />
                      </FormControl>
                      <FormMessage className="text-red-500" />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem className="gap-1.5">
                    <FormLabel>Password*</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input
                          type={showPassword ? 'text' : 'password'}
                          placeholder="Create a password"
                          {...field}
                          disabled={loading}
                          className="pr-10"
                        />
                        <button
                          type="button"
                          onClick={() => setShowPassword(!showPassword)}
                          className="cursor-pointer absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700 focus:outline-none"
                          disabled={loading}
                        >
                          {showPassword ? (
                            <EyeOff className="h-4 w-4" />
                          ) : (
                            <Eye className="h-4 w-4" />
                          )}
                        </button>
                      </div>
                    </FormControl>
                    <FormMessage className="text-red-500" />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="confirmPassword"
                render={({ field }) => (
                  <FormItem className="gap-1.5">
                    <FormLabel>Confirm Password*</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input
                          type={showConfirmPassword ? 'text' : 'password'}
                          placeholder="Confirm your password"
                          {...field}
                          disabled={loading}
                          className="pr-10"
                        />
                        <button
                          type="button"
                          onClick={() =>
                            setShowConfirmPassword(!showConfirmPassword)
                          }
                          className="cursor-pointer absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700 focus:outline-none"
                          disabled={loading}
                        >
                          {showConfirmPassword ? (
                            <EyeOff className="h-4 w-4" />
                          ) : (
                            <Eye className="h-4 w-4" />
                          )}
                        </button>
                      </div>
                    </FormControl>
                    <FormMessage className="text-red-500" />
                  </FormItem>
                )}
              />

              {/* Password Requirements */}
              <Card className="shadow-none border-none bg-destructive-50 gap-3 py-4">
                <CardHeader className="px-4 gap-0">
                  <CardTitle className="text-sub-header text-sm font-normal">
                    Password Requirements
                  </CardTitle>
                </CardHeader>
                <CardContent className="px-4">
                  <div className="space-y-2">
                    {passwordRequirements.map((requirement, index) => (
                      <div key={index} className="flex items-center gap-2">
                        {requirement.met ? (
                          <CircleCheckOutlineIcon className="h-4 w-4 text-success-600 fill-success-100" />
                        ) : (
                          <CircleXOutlineIcon className="h-4 w-4 text-destructive-400" />
                        )}
                        <span className="text-sm">{requirement.text}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Terms and Conditions */}
              <FormField
                control={form.control}
                name="acceptTerms"
                render={({ field }) => (
                  <FormItem className="gap-1.5">
                    <div className="flex gap-2 items-start">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          className="mt-1 data-[state=checked]:bg-primary"
                        />
                      </FormControl>
                      <FormLabel className="text-sm text-foreground leading-relaxed cursor-pointer">
                        Accept{' '}
                        <span className="underline cursor-pointer text-primary">
                          terms and conditions
                        </span>
                      </FormLabel>
                    </div>
                    <FormMessage className="text-red-500" />
                  </FormItem>
                )}
              />

              <Button
                type="submit"
                className="w-full"
                color="primary"
                disabled={loading || !form.formState.isValid}
              >
                {loading ? (
                  <div className="flex items-center space-x-2">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    <span>Creating account...</span>
                  </div>
                ) : (
                  'Create Account'
                )}
              </Button>
            </form>
          </Form>

          <div className="">
            <Body className="text-sm text-foreground text-center">
              Already have an account?{' '}
              <Link
                href={Routes.SIGN_IN}
                className="underline cursor-pointer text-primary"
              >
                Sign in
              </Link>
            </Body>
          </div>
        </div>
      </div>
    </div>
  );
}
