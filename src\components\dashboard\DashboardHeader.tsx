import { Heading, Body } from '@/components/ui/typography';

interface DashboardHeaderProps {
  userName?: string | null;
  totalCount: number;
}

export function DashboardHeader({
  userName,
  totalCount,
}: DashboardHeaderProps) {
  return (
    <div>
      <Heading level={3} className="font-extrabold mb-2">
        Welcome back{userName ? `, ${userName}` : ''}
      </Heading>
      <Body className="text-base text-neutral-600">
        {totalCount > 0
          ? 'Review your projects or start a new one.'
          : 'Ready to create your first project?'}
      </Body>
    </div>
  );
}
