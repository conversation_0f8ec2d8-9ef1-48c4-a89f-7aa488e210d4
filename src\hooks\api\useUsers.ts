import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useUserService } from '../use-services';
import { User } from '@/types/user';
import { queryKeys } from '@/lib/query-keys';
import { toastError, toastSuccess } from '@/lib/toast';
import { ApiError } from '@/lib/api-client';

interface UseUsersFilters {
  page?: number;
  search?: string;
  role?: string;
  status?: string;
  limit?: number;
}

interface UseUsersOptions {
  enabled?: boolean;
}

const LIMIT = 8;

/**
 * Hook for fetching users with filters
 */
export function useUsers(filters?: UseUsersFilters, options?: UseUsersOptions) {
  const userService = useUserService();

  return useQuery({
    queryKey: queryKeys.users.list(filters),
    queryFn: async () => {
      return userService.getUsers(
        filters?.page,
        filters?.search,
        filters?.role,
        filters?.status,
        filters?.limit || LIMIT
      );
    },
    enabled: options?.enabled !== false,
    staleTime: 1000 * 30, // 30 seconds
    gcTime: 1000 * 60 * 5, // 5 minutes
  });
}

/**
 * Hook for fetching user requests (pending users)
 */
export function useUserRequests(
  filters?: Omit<UseUsersFilters, 'status'>,
  options?: UseUsersOptions
) {
  const userService = useUserService();

  return useQuery({
    queryKey: queryKeys.users.requests(filters),
    queryFn: async () => {
      return userService.getUsersRequests(
        filters?.page,
        filters?.search,
        filters?.role,
        filters?.limit || LIMIT
      );
    },
    enabled: options?.enabled !== false,
    staleTime: 1000 * 30, // 30 seconds
    gcTime: 1000 * 60 * 5, // 5 minutes
  });
}

/**
 * Hook for creating a user (invite)
 */
export function useCreateUser() {
  const userService = useUserService();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (userData: Partial<User>) => {
      const { name, email, role, productionHouse } = userData;
      return userService.createUser({
        name,
        email,
        role: role!,
        productionHouse,
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.users.all });
      toastSuccess('Invite sent successfully.');
    },
    onError: (error: unknown) => {
      const apiError = error as ApiError;
      const message = apiError.response?.data?.message ?? 'An error occurred';
      const statusCode = apiError.response?.data?.statusCode;
      if (statusCode === 409) {
        toastError(
          'This email address is already registered. Please invite a different address.'
        );
      } else {
        toastError(message);
      }
    },
  });
}

/**
 * Hook for updating a user
 */
export function useUpdateUser() {
  const userService = useUserService();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, data }: { id: string; data: Partial<User> }) => {
      return userService.updateUser(id, {
        name: data.name,
        role: data.role,
        productionHouse: data.productionHouse,
        status: data.status,
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.users.all });
      toastSuccess('User updated successfully.');
    },
    onError: (error: unknown) => {
      const message =
        (error as ApiError).response?.data?.message ?? 'An error occurred';
      toastError(message);
    },
  });
}

/**
 * Hook for deleting a user
 */
export function useDeleteUser() {
  const userService = useUserService();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      return userService.deleteUser(id);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.users.all });
      toastSuccess('User removed successfully.');
    },
    onError: (error: unknown) => {
      const message =
        (error as ApiError).response?.data?.message ?? 'An error occurred';
      toastError(message);
    },
  });
}

/**
 * Hook for updating user status (accept/reject)
 */
export function useUpdateUserStatus() {
  const userService = useUserService();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      id,
      type,
    }: {
      id: string;
      type: 'accept' | 'reject';
    }) => {
      if (type === 'accept') {
        return userService.acceptUser(id);
      }
      return userService.rejectUser(id);
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.users.all });
      const message =
        variables.type === 'accept'
          ? 'User invite accepted successfully.'
          : 'User invite rejected successfully.';
      toastSuccess(message);
    },
    onError: (error: unknown) => {
      const apiError = error as ApiError;
      const message = apiError.response?.data?.message ?? 'An error occurred';
      const statusCode = apiError.response?.data?.statusCode;
      if (statusCode === 409) {
        toastError(
          'This email address is already registered. Please invite a different address.'
        );
      } else {
        toastError(message);
      }
    },
  });
}
