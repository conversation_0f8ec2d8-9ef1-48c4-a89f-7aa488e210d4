import { Skeleton } from '../ui/skeleton';
import { Heading, Body } from '../ui/typography';
import { Avatar, AvatarFallback } from '../ui/avatar';
import { UndrawEnterPassword } from '../illustrations/Illustrations';

export const CommentLoading = () => {
  return (
    <div className="flex items-start gap-3">
      <Avatar>
        <AvatarFallback>
          <Skeleton className="h-8 w-8 rounded-full" />
        </AvatarFallback>
      </Avatar>
      <div className="flex flex-col items-start gap-2 w-full">
        <Skeleton className="h-4 w-48" />
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-full" />
      </div>
    </div>
  );
};

export const NoCommentsYet = () => {
  return (
    <div className="flex flex-col items-center justify-center w-full h-full gap-12">
      <UndrawEnterPassword width={159} height={134} />
      <div className="flex flex-col items-center gap-2">
        <Heading level={3}>No comments yet</Heading>
        <Body>Be the first to comment.</Body>
      </div>
    </div>
  );
};
