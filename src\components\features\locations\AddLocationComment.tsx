'use client';

import { Comment } from '@/types';
import { CommentItem } from '@/components/comment/CommentItem';
import { CommentLoading } from '@/components/comment/CommentLoading';
import { useAuth } from '@/contexts/auth-context';
import { CommentInput } from '@/components/comment/CommentInput';

interface AddLocationCommentProps {
  comments?: Comment[];
  loading?: boolean;
  isCreating?: boolean;
  onComment?: (content: string) => void;
  onDelete?: (commentId: string) => void;
  onEdit?: (commentId: string, content: string) => void;
  onClose?: () => void;
}

export function AddLocationComment({
  comments = [],
  loading = false,
  isCreating = false,
  onComment,
  onDelete,
  onEdit,
  onClose,
}: AddLocationCommentProps) {
  const { user } = useAuth();

  const handleComment = (content: string) => {
    onComment?.(content);
  };

  const handleDelete = (commentId: string) => {
    onDelete?.(commentId);
  };

  const handleEdit = (commentId: string, content: string) => {
    onEdit?.(commentId, content);
  };

  const handleClose = () => {
    onClose?.();
  };

  return (
    <div className="w-full space-y-6">
      <h2 className="font-semibold">Comments</h2>
      {/* Comments list */}
      <div className="space-y-4">
        {loading && <CommentLoading />}
        {!loading && comments.length === 0 && <p>There are no comments yet.</p>}
        {!loading &&
          comments.length > 0 &&
          comments.map(comment => (
            <CommentItem
              key={comment.id}
              comment={comment}
              isOwnComment={user?.id === comment.author.id}
              onDelete={() => handleDelete(comment.id)}
              onEdit={(commentId, content) => handleEdit(commentId, content)}
            />
          ))}
      </div>

      {/* Add comment input */}
      {user && (
        <div className="pb-6">
          <CommentInput
            user={user}
            isCreating={isCreating}
            onComment={handleComment}
            onCancel={() => {}}
            showCloseButton={true}
            onClose={handleClose}
          />
        </div>
      )}
    </div>
  );
}
