import { httpClient } from '../http-client';
import { User } from '@/types/user';
import {
  AcceptInvitationData,
  AcceptInvitationResponse,
  LoginCredentials,
  LoginResponse,
  RefreshTokenResponse,
  AuthenticatedUser,
  ChangePasswordData,
  ForgotPasswordData,
  ResetPasswordData,
  ResetPasswordConfirmData,
  InvitationUserInfoResponse,
  RegisterData,
} from '@/types/auth';

/**
 * Authentication Service - Handles all authentication-related operations
 */
export class AuthService {
  /**
   * Register a new user account
   */
  async register(data: RegisterData): Promise<void> {
    return httpClient.post<void>('/auth/register', {
      email: data.email,
      password: data.password,
      firstName: data.firstName,
      lastName: data.lastName,
      userName: data.username,
    });
  }

  /**
   * Authenticate user with email and password
   */
  async login(credentials: LoginCredentials): Promise<LoginResponse> {
    const response = await httpClient.post<LoginResponse>('/auth/login', {
      email: credentials.email,
      password: credentials.password,
    });

    return response;
  }

  /**
   * Refresh access token using refresh token
   */
  async refreshToken(refreshToken: string): Promise<RefreshTokenResponse> {
    // Make direct fetch call instead of using httpClient to avoid circular token refresh
    const baseUrl =
      process.env.NEXT_PUBLIC_NESTJS_API_URL || 'http://localhost:3001';

    const response = await fetch(`${baseUrl}/auth/refresh`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ refreshToken }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(
        errorData.message ||
          `Failed to refresh token: ${response.status} ${response.statusText}`
      );
    }

    return response.json();
  }

  /**
   * Complete authentication flow: login + fetch user profile
   */
  async authenticate(
    credentials: LoginCredentials
  ): Promise<AuthenticatedUser> {
    // Step 1: Login to get tokens
    const loginResponse = await this.login(credentials);

    // Step 2: Fetch user profile using the access token
    const userProfile = await this.getUserProfile(loginResponse.accessToken);

    // Step 3: Return complete authenticated user data
    return {
      ...userProfile,
      accessToken: loginResponse.accessToken,
      refreshToken: loginResponse.refreshToken,
      accessTokenExpires: loginResponse.accessTokenExpireTime * 1000,
      refreshTokenExpires: loginResponse.refreshTokenExpireTime * 1000,
    };
  }

  /**
   * Get user profile using access token
   */
  async getUserProfile(accessToken: string): Promise<User> {
    const baseUrl =
      process.env.NEXT_PUBLIC_NESTJS_API_URL || 'http://localhost:3001';

    const response = await fetch(`${baseUrl}/users/me`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${accessToken}`,
      },
    });

    if (!response.ok) {
      throw new Error(
        `Failed to fetch user profile: ${response.status} ${response.statusText}`
      );
    }

    return response.json();
  }

  /**
   * Change user password
   */
  async changePassword(data: ChangePasswordData): Promise<void> {
    return httpClient.patch<void>('/auth/change-password', data);
  }

  /**
   * Request password reset
   */
  async forgotPassword(data: ForgotPasswordData): Promise<void> {
    return httpClient.post<void>('/auth/reset-password', data);
  }

  /**
   * Reset password with token
   */
  async resetPassword(data: ResetPasswordData): Promise<void> {
    return httpClient.post<void>('/auth/reset-password', data);
  }

  /**
   * Confirm password reset with token
   */
  async resetPasswordConfirm(data: ResetPasswordConfirmData): Promise<void> {
    return httpClient.post<void>('/auth/reset-password/confirm', data);
  }

  /**
   * Logout user (invalidate tokens on server)
   */
  async logout(): Promise<void> {
    return httpClient.post<void>('/auth/logout');
  }

  /**
   * Verify email address
   */
  async verifyEmail(token: string): Promise<void> {
    return httpClient.post<void>('/auth/verify-email', { token });
  }

  /**
   * Confirm email address with token
   */
  async confirmEmail(token: string): Promise<void> {
    return httpClient.post<void>('/auth/confirm-email', { token });
  }

  /**
   * Resend email verification
   */
  async resendVerificationEmail(): Promise<void> {
    return httpClient.post<void>('/auth/resend-verification');
  }

  /**
   * Get user information from invitation token
   */
  async getInvitationUserInfo(
    token: string
  ): Promise<InvitationUserInfoResponse> {
    const baseUrl =
      process.env.NEXT_PUBLIC_NESTJS_API_URL || 'http://localhost:3001';

    const response = await fetch(`${baseUrl}/auth/invitation?token=${token}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(
        errorData.message ||
          `Failed to fetch user information: ${response.status} ${response.statusText}`
      );
    }

    return response.json();
  }

  /**
   * Accept invitation and set password
   */
  async acceptInvitation(
    data: AcceptInvitationData
  ): Promise<AcceptInvitationResponse> {
    const baseUrl =
      process.env.NEXT_PUBLIC_NESTJS_API_URL || 'http://localhost:3001';

    const response = await fetch(`${baseUrl}/auth/accept-invitation`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        token: data.token,
        password: data.password,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(
        errorData.message ||
          `Failed to accept invitation: ${response.status} ${response.statusText}`
      );
    }

    return response.json();
  }

  /**
   * Resend invitation link
   */
  async resendInvitation(email: string): Promise<void> {
    const baseUrl =
      process.env.NEXT_PUBLIC_NESTJS_API_URL || 'http://localhost:3001';

    const response = await fetch(`${baseUrl}/auth/resend-invitation`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(
        errorData.message ||
          `Failed to resend invitation: ${response.status} ${response.statusText}`
      );
    }

    return response.json();
  }
}

// Export singleton instance
export const authService = new AuthService();
