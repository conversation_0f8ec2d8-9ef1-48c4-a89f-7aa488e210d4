import { NextRequest, NextResponse } from 'next/server';
import { getServerSession, User } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { makeServerRequest } from '@/lib/api-client';

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user || !session?.accessToken) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { currentPassword, newPassword } = await request.json();

    // Validate input
    if (!currentPassword || !newPassword) {
      return NextResponse.json(
        { error: 'Current password and new password are required' },
        { status: 400 }
      );
    }

    if (newPassword.length < 6) {
      return NextResponse.json(
        { error: 'New password must be at least 6 characters' },
        { status: 400 }
      );
    }

    // Call backend API to change password
    // Get the access token from session
    const accessToken = session.accessToken;
    if (!accessToken) {
      return NextResponse.json(
        { error: 'Access token not found' },
        { status: 401 }
      );
    }

    // Call NestJS profile update endpoint using the utility function
    const data = await makeServerRequest('/auth/profile', session.accessToken, {
      method: 'PUT',
      body: JSON.stringify({
        currentPassword,
        newPassword,
      }),
    });

    return NextResponse.json(
      {
        message: 'Password changed successfully',
        user: (data as { user: User }).user,
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('Failed to change password:', error);
    return NextResponse.json(
      {
        error:
          error instanceof Error ? error.message : 'Failed to change password',
      },
      { status: 500 }
    );
  }
}
