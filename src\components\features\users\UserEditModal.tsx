'use client';

import * as React from 'react';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { User } from '@/types/user';
import { Loader2 } from 'lucide-react';
import { updateUserSchema, UpdateUserFormData } from '@/lib/validations';
import { Role, UserStatus } from '@/types/enum';
import { ROLE_DISPLAY_NAMES } from '@/lib/utils';
import { useProductionHouseService } from '@/hooks/use-services';
import { AutocompleteInput } from '@/components/ui/autocomplete';
import type { ProductionHouse } from '@/lib/services/production-service';
import { useUpdateUser } from '@/hooks/api/useUsers';

interface UserEditModalProps {
  user: User | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export default function UserEditModal({
  user,
  open,
  onOpenChange,
}: UserEditModalProps) {
  const updateUserMutation = useUpdateUser();
  const isUpdating = updateUserMutation.isPending;
  const productionHouseService = useProductionHouseService();
  const [productionHouses, setProductionHouses] = useState<ProductionHouse[]>(
    []
  );
  const [isLoadingProductionHouses, setIsLoadingProductionHouses] =
    useState(false);
  const form = useForm<UpdateUserFormData>({
    resolver: zodResolver(updateUserSchema),
    defaultValues: {
      name: '',
      role: undefined,
      status: undefined,
      productionHouseId: '',
    },
  });

  const [currentRole, setCurrentRole] = useState<Role | undefined>(user?.role);
  const watchedProductionHouseId = form.watch('productionHouseId');

  useEffect(() => {
    if (currentRole !== Role.ProductionAdmin) return;

    const fetchProductionHouses = async () => {
      try {
        setIsLoadingProductionHouses(true);
        const response = await productionHouseService.getProductionHouses();
        setProductionHouses(response);
      } catch (error) {
        console.error('Error fetching production houses', error);
      } finally {
        setIsLoadingProductionHouses(false);
      }
    };

    fetchProductionHouses();
  }, [currentRole, productionHouseService]);

  // Update form when user changes
  useEffect(() => {
    if (user) {
      form.reset({
        name: `${user.firstName} ${user.lastName}`.trim(),
        role: user.role,
        status: user.status as UserStatus,
        productionHouseId: user.productionHouse?.id || '',
      });
      setCurrentRole(user.role);
    }
  }, [user, form]);

  // Reset form when modal closes
  useEffect(() => {
    if (!open) {
      form.reset();
    }
  }, [open, form]);

  const handleSubmit = async (data: UpdateUserFormData) => {
    if (!user) return;
    const payload: Partial<User> = {
      name: data.name,
      role: data.role,
      productionHouse:
        data.role === Role.ProductionAdmin
          ? {
              id: data.productionHouseId || '',
            }
          : undefined,
      status: data.status,
    };
    try {
      await updateUserMutation.mutateAsync({
        id: user.id,
        data: payload,
      });
      onOpenChange(false);
    } catch {
      // Error handling is done in the mutation
    }
  };

  const handleOpenChange = (open: boolean) => {
    if (!open) {
      form.reset();
    }
    onOpenChange(open);
  };

  if (!user) return null;

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-[20.5625rem]">
        <DialogHeader>
          <DialogTitle>Edit User</DialogTitle>
          <DialogDescription>
            Update the user&apos;s details or role.
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className="space-y-4"
          >
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>User name</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter the user's name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormItem>
              <FormLabel>Email</FormLabel>
              <FormControl>
                <Input
                  type="email"
                  disabled
                  value={user.email}
                  placeholder="Enter the user's email"
                />
              </FormControl>
              <p className="text-xs text-muted-foreground">
                Email cannot be changed
              </p>
            </FormItem>

            <FormField
              control={form.control}
              name="role"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Role</FormLabel>
                  <Select
                    onValueChange={value => {
                      field.onChange(value);
                      setCurrentRole(value as Role);
                    }}
                    value={field.value}
                  >
                    <FormControl>
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="Select the user's role" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {Object.values(Role).map(role => (
                        <SelectItem key={role} value={role}>
                          {ROLE_DISPLAY_NAMES[role]}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {currentRole === Role.ProductionAdmin && (
              <FormField
                control={form.control}
                name="productionHouseId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Production house</FormLabel>
                    <FormControl>
                      <AutocompleteInput
                        allowCreateOption
                        onCreateOption={async label => {
                          const newHouse =
                            await productionHouseService.createProductionHouse({
                              name: label,
                            });
                          setProductionHouses(prev => [newHouse, ...prev]);
                          return {
                            value: newHouse.id,
                            label: newHouse.name,
                          };
                        }}
                        options={productionHouses.map(house => ({
                          value: house.id,
                          label: house.name,
                        }))}
                        selectedValue={field.value || null}
                        onSelectedValueChange={value => {
                          field.onChange(value ?? '');
                        }}
                        placeholder="Enter the production house name"
                        isLoading={isLoadingProductionHouses}
                        emptyState={
                          isLoadingProductionHouses
                            ? 'Loading production houses...'
                            : 'No production houses found'
                        }
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}
            <FormField
              control={form.control}
              name="status"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Status</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="Select the user's status" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem
                        disabled={user?.status === UserStatus.PENDING}
                        value="active"
                      >
                        Active
                      </SelectItem>
                      <SelectItem disabled value="pending">
                        Pending
                      </SelectItem>
                      <SelectItem value="disabled">Disabled</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter className="mt-6">
              <Button
                type="button"
                variant="link"
                className="hover:no-underline"
                onClick={() => handleOpenChange(false)}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={
                  isUpdating ||
                  !form.formState.isValid ||
                  (currentRole === Role.ProductionAdmin &&
                    !watchedProductionHouseId?.trim())
                }
              >
                Save changes
                {isUpdating && <Loader2 className="ml-2 size-4 animate-spin" />}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
