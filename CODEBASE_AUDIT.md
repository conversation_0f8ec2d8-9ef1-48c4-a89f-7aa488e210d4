# Scoutr Web App - Comprehensive Codebase Audit

**Date:** 2024  
**Auditor:** AI Code Architect  
**Purpose:** Vibe-coding readiness assessment and refactoring roadmap

---

## Executive Summary

This audit identifies **inconsistencies in naming conventions, folder structure, and code organization** that hinder AI-assisted development and code predictability. The codebase shows good architectural foundations (service layer, type safety) but needs standardization to become truly "vibe-coding ready."

### Key Findings

- ✅ **Strengths:** Service layer pattern, TypeScript usage, component modularity
- ⚠️ **Issues:** Inconsistent naming (kebab-case vs PascalCase), mixed folder organization, limited React Query adoption
- 🔴 **Critical:** Hook placement confusion, API route naming inconsistency, scattered feature code

---

## 1. Naming Conventions Audit

### 1.1 Component Files

**Current State:**

- Mixed naming: `AddTagModal.tsx` (PascalCase) vs `data-table-pagination.tsx` (kebab-case)
- `breadcrumbs.tsx` (lowercase) vs `navbar.tsx` (lowercase) vs `IconButton.tsx` (PascalCase)

**Issues:**

- No consistent pattern for component file names
- Repository rule specifies PascalCase for components, but not consistently followed

**Recommendations:**

```
✅ CORRECT (PascalCase):
- AddTagModal.tsx
- UserEditModal.tsx
- DataTablePagination.tsx
- Breadcrumbs.tsx
- Navbar.tsx
- IconButton.tsx

❌ INCORRECT (should be PascalCase):
- data-table-pagination.tsx → DataTablePagination.tsx
- breadcrumbs.tsx → Breadcrumbs.tsx
- navbar.tsx → Navbar.tsx
- footer.tsx → Footer.tsx
```

### 1.2 Hook Files

**Current State:**

- `use-breadcrumbs.tsx` (kebab-case)
- `use-services.ts` (kebab-case)
- `useCommentAPI.tsx` (camelCase, wrong extension)
- `useComment.ts` (camelCase, in wrong folder)
- `use-debounce.ts` (kebab-case)
- `use-mobile.ts` (kebab-case)
- `use-page-navbar.ts` (kebab-case)
- `useFileUpload.ts` (camelCase)
- `useLocations.ts` (camelCase)

**Issues:**

- Inconsistent naming (kebab-case vs camelCase)
- `useCommentAPI.tsx` should be `.ts` (not a component)
- `useComment.ts` is in `components/comment/` but should be in `hooks/`

**Recommendations:**

```
✅ STANDARD: camelCase with .ts extension
- useBreadcrumbs.ts
- useServices.ts (or keep use-services.ts if team prefers)
- useCommentAPI.ts
- useComment.ts (move from components/comment/)
- useDebounce.ts
- useMobile.ts
- usePageNavbar.ts
- useFileUpload.ts
- useLocations.ts
```

### 1.3 API Routes

**Current State:**

- Folder: `src/app/nextapi/` (inconsistent naming)
- Files: `route.ts` (correct lowercase per Next.js convention)

**Issues:**

- `nextapi` folder name doesn't follow standard Next.js patterns
- Should be `api` or follow Next.js 13+ App Router conventions

**Recommendations:**

```
Current: src/app/nextapi/
Recommended: src/app/api/ (standard Next.js convention)

OR keep as: src/app/nextapi/ but document why
```

### 1.4 Service Files

**Current State:** ✅ **GOOD**

- Consistent: `*-service.ts` pattern
- Examples: `tag-service.ts`, `location-service.ts`, `auth-service.ts`

**Recommendations:**

- Keep current pattern (excellent consistency)

### 1.5 Type Files

**Current State:** ✅ **GOOD**

- Consistent: `*.ts` files in `src/types/`
- Examples: `tag.ts`, `user.ts`, `location.ts`

**Recommendations:**

- Keep current pattern

### 1.6 Variable/Function Names

**Issues Found:**

- `fetchTags` vs `fetchData` vs `fetchUserData` (inconsistent verb patterns)
- `handleAddTag` vs `handleSaveUser` vs `openAddUserChange` (inconsistent naming)
- `isAddTagModalOpen` vs `isEditModalOpen` (good consistency)

**Recommendations:**

```
✅ CONSISTENT PATTERNS:
- Modal state: is{Feature}ModalOpen
- Handlers: handle{Action}
- Fetch functions: fetch{Resource} or get{Resource}
- Callbacks: on{Action}
```

---

## 2. Folder & File Structure Audit

### 2.1 Component Organization

**Current Structure:**

```
src/components/
├── AddTagModal.tsx          ✅ PascalCase
├── data-table-pagination.tsx ❌ kebab-case
├── breadcrumbs.tsx           ❌ lowercase
├── navbar.tsx                ❌ lowercase
├── comment/
│   ├── useComment.ts         ❌ Hook in components folder
│   └── CommentModal.tsx      ✅ PascalCase
├── photo/
│   └── [complex nested structure]
└── ui/                       ✅ Good organization
```

**Issues:**

1. **Hook in wrong location:** `components/comment/useComment.ts` should be in `hooks/`
2. **Inconsistent component naming:** Mix of PascalCase and kebab-case
3. **Feature-specific components scattered:** Some in root, some in feature folders

**Recommended Structure:**

```
src/components/
├── ui/                       # Design system components
│   ├── button.tsx
│   ├── dialog.tsx
│   └── ...
├── features/                 # Feature-specific components
│   ├── tags/
│   │   ├── AddTagModal.tsx
│   │   ├── EditTagModal.tsx
│   │   └── TagsTable.tsx
│   ├── locations/
│   │   ├── LocationCard.tsx
│   │   ├── LocationPicker.tsx
│   │   └── LocationsTable.tsx
│   ├── comments/
│   │   ├── CommentModal.tsx
│   │   ├── CommentItem.tsx
│   │   └── CommentModalHeader.tsx
│   └── users/
│       ├── AddUserModal.tsx
│       └── UserEditModal.tsx
├── layout/                   # Layout components
│   ├── AppLayout.tsx
│   ├── PublicLayout.tsx
│   └── Navbar.tsx
└── shared/                   # Shared/common components
    ├── Breadcrumbs.tsx
    ├── ConfirmDialog.tsx
    ├── DataTablePagination.tsx
    └── IconButton.tsx
```

### 2.2 Hooks Organization

**Current Structure:**

```
src/hooks/
├── use-breadcrumbs.tsx
├── use-services.ts
├── useCommentAPI.tsx
├── useComment.ts          ❌ Actually in components/comment/
├── use-debounce.ts
└── ...
```

**Issues:**

- `useComment.ts` is in `components/comment/` instead of `hooks/`
- Mixed naming conventions

**Recommended Structure:**

```
src/hooks/
├── api/                    # API-related hooks
│   ├── useCommentAPI.ts
│   └── useLocations.ts
├── services/              # Service hooks
│   └── useServices.ts
├── ui/                    # UI-related hooks
│   ├── useBreadcrumbs.ts
│   ├── useDebounce.ts
│   ├── useMobile.ts
│   └── usePageNavbar.ts
└── features/              # Feature-specific hooks
    ├── comments/
    │   └── useComment.ts
    └── locations/
        └── useLocations.ts
```

**OR simpler flat structure:**

```
src/hooks/
├── useBreadcrumbs.ts
├── useComment.ts          # Move from components/comment/
├── useCommentAPI.ts
├── useDebounce.ts
├── useFileUpload.ts
├── useLocations.ts
├── useMobile.ts
├── usePageNavbar.ts
└── useServices.ts
```

### 2.3 App Router Structure

**Current Structure:**

```
src/app/
├── admin/
│   ├── tags/
│   │   ├── tags-container.tsx    ❌ Should be PascalCase
│   │   ├── tags-table.tsx        ❌ Should be PascalCase
│   │   └── page.tsx              ✅ Correct
│   ├── locations/
│   │   ├── locations-container.tsx
│   │   └── locations-table.tsx
│   └── users/
│       └── users.tsx             ❌ Should be PascalCase or page.tsx
├── nextapi/                      ❌ Non-standard naming
└── scout/
    └── reference/
        └── [id]/
            ├── reference-context.tsx
            └── reference-view.tsx
```

**Issues:**

1. **Container/Table files:** Should follow PascalCase or be moved to components
2. **API routes:** `nextapi` folder name is non-standard
3. **Context files:** Mixed between app routes and components

**Recommendations:**

```
src/app/
├── admin/
│   ├── tags/
│   │   ├── page.tsx              # Main page
│   │   └── loading.tsx           # Loading state
│   ├── locations/
│   │   └── page.tsx
│   └── users/
│       └── page.tsx
├── api/                          # Rename from nextapi
│   ├── auth/
│   │   └── [...nextauth]/
│   │       └── route.ts
│   └── profile/
│       └── update/
│           └── route.ts
└── scout/
    └── reference/
        └── [id]/
            ├── page.tsx
            └── loading.tsx
```

**Move to components:**

- `tags-container.tsx` → `components/features/tags/TagsContainer.tsx`
- `tags-table.tsx` → `components/features/tags/TagsTable.tsx`
- `locations-container.tsx` → `components/features/locations/LocationsContainer.tsx`
- `locations-table.tsx` → `components/features/locations/LocationsTable.tsx`
- `users.tsx` → `components/features/users/UsersTable.tsx` (or keep as page.tsx)

### 2.4 Service Layer Structure

**Current Structure:** ✅ **EXCELLENT**

```
src/lib/services/
├── tag-service.ts
├── location-service.ts
├── auth-service.ts
└── index.ts
```

**Recommendations:**

- Keep current structure (well-organized)

### 2.5 Redundant/Misplaced Files

**Issues Found:**

1. `components/examples/` - Unclear purpose, should be removed or documented
2. `components/AuthenticatedApiExample.tsx` - Example code, should be in examples or removed
3. `app/sentry-example-page/` - Example code, should be removed or moved to examples
4. `app/sentry-example-api/` - Example code, should be removed

**Recommendations:**

- Remove example files or move to dedicated `examples/` folder at root
- Document purpose if keeping

---

## 3. Code Organization & Consistency Audit

### 3.1 Component Structure

**Current Patterns:**

- ✅ Good: Props interfaces defined
- ✅ Good: TypeScript usage
- ⚠️ Issue: Some components are too large (300+ lines)
- ⚠️ Issue: Mixed export patterns (default vs named)

**Examples:**

- `TagsContainer.tsx` - 328 lines (could be split)
- `Users.tsx` - 722 lines (definitely should be split)
- `LocationsTable.tsx` - 341 lines (manageable but could be improved)

**Recommendations:**

```
Component Structure Template:
1. Imports (grouped: React, Next.js, UI, Utils, Types)
2. Types/Interfaces
3. Constants
4. Component (default export)
5. Sub-components (if any)
```

### 3.2 Hooks Usage

**Current State:**

- ✅ Good: Custom hooks for services (`use-services.ts`)
- ⚠️ Issue: Limited React Query adoption (only in comments)
- ⚠️ Issue: Manual state management in many components
- ⚠️ Issue: Inconsistent data fetching patterns

**Examples:**

- `TagsContainer.tsx` - Manual useState + useEffect + service calls
- `Users.tsx` - Manual useState + useEffect + service calls
- `useComment.ts` - Uses React Query ✅ (good example)

**Recommendations:**

```typescript
// ❌ CURRENT PATTERN (Manual)
const [tags, setTags] = useState<Tag[]>([]);
const [loading, setLoading] = useState(true);

useEffect(() => {
  fetchTags();
}, [dependencies]);

// ✅ RECOMMENDED PATTERN (React Query)
const {
  data: tags,
  isLoading,
  refetch,
} = useQuery({
  queryKey: ['tags', filters],
  queryFn: () => tagService.getTags(filters),
});
```

**Migration Priority:**

1. High: Frequently used data (tags, locations, users)
2. Medium: Less frequent but important (dashboard stats)
3. Low: One-time fetches (user profile)

### 3.3 API Integration

**Current State:**

- ✅ Excellent: Service layer abstraction
- ✅ Good: HTTP client with token refresh
- ⚠️ Issue: Inconsistent error handling
- ⚠️ Issue: No request cancellation patterns

**Recommendations:**

```typescript
// Standardize error handling
try {
  await tagService.createTag(data);
  toastSuccess('Tag created successfully');
} catch (error) {
  const message = extractErrorMessage(error);
  toastError(message);
}

// Add request cancellation for React Query
const queryClient = useQueryClient();
queryClient.cancelQueries({ queryKey: ['tags'] });
```

### 3.4 State Management

**Current Patterns:**

- ✅ Good: Local state for UI (modals, forms)
- ⚠️ Issue: No global state management (Context only for auth/navbar)
- ⚠️ Issue: Prop drilling in some areas

**Recommendations:**

- Keep Context for: Auth, Theme, Navbar
- Use React Query for: Server state (tags, locations, users)
- Use local state for: UI state (modals, form inputs)

### 3.5 Forms

**Current State:**

- ⚠️ Issue: Manual form validation in some components
- ⚠️ Issue: Mixed patterns (some use React Hook Form, some don't)
- ✅ Good: Zod available in dependencies

**Examples:**

- `AddTagModal.tsx` - Manual validation
- `PasswordForm.tsx` - Likely uses React Hook Form (need to verify)

**Recommendations:**

```typescript
// Standardize on React Hook Form + Zod
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

const tagSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  type: z.enum([TagType.RESIDENTIAL, TagType.COMMERCIAL, TagType.PUBLIC]),
  color: z.string().optional(),
});

type TagFormData = z.infer<typeof tagSchema>;

function AddTagModal() {
  const form = useForm<TagFormData>({
    resolver: zodResolver(tagSchema),
  });
  // ...
}
```

---

## 4. Vibe-Coding Preparedness Assessment

### 4.1 Separation of Concerns

**Score: 7/10**

**Strengths:**

- ✅ Service layer separates API logic
- ✅ Components are reasonably focused
- ✅ Types are well-defined

**Weaknesses:**

- ⚠️ Some components mix data fetching, UI logic, and business logic
- ⚠️ Container components could be better separated from presentational components

**Recommendations:**

```
Pattern: Container → Presentational
- Container: Handles data fetching, state, business logic
- Presentational: Pure UI components

Example:
TagsContainer.tsx (container)
  └── TagsTable.tsx (presentational)
  └── AddTagModal.tsx (presentational)
```

### 4.2 Component Size & Responsibility

**Score: 6/10**

**Issues:**

- `Users.tsx` - 722 lines (too large)
- `TagsContainer.tsx` - 328 lines (manageable but could be smaller)
- `LocationsTable.tsx` - 341 lines (manageable)

**Recommendations:**

- Split large components into smaller, focused components
- Extract custom hooks for complex logic
- Target: <200 lines per component

### 4.3 Naming Predictability

**Score: 5/10**

**Issues:**

- Inconsistent naming makes it hard for AI to predict patterns
- Mixed conventions confuse code generation

**Recommendations:**

- Establish and document naming standards
- Create ESLint rules to enforce naming
- Use consistent prefixes/suffixes

### 4.4 Type Safety

**Score: 8/10**

**Strengths:**

- ✅ TypeScript strict mode enabled
- ✅ Good type definitions
- ✅ Service methods are typed

**Weaknesses:**

- ⚠️ Some `any` types (need to audit)
- ⚠️ Error handling types could be more specific

**Recommendations:**

```typescript
// Standardize error types
export class ApiError extends Error {
  constructor(
    message: string,
    public statusCode: number,
    public response?: unknown
  ) {
    super(message);
  }
}

// Use discriminated unions for better type safety
type ApiResponse<T> =
  | { success: true; data: T }
  | { success: false; error: ApiError };
```

### 4.5 Dead Code & Ambiguity

**Issues Found:**

- `components/examples/` - Unclear if used
- `app/sentry-example-*` - Example code
- Commented code in some files
- Unused imports (ESLint should catch but verify)

**Recommendations:**

- Remove or document example code
- Remove commented code
- Run ESLint with unused import detection

### 4.6 Scaffolding Templates

**Current State:** ❌ **NONE**

**Recommendations:**
Create templates for:

1. **Component Template:**

```typescript
// components/features/{feature}/{ComponentName}.tsx
'use client';

import { ... } from '...';

interface {ComponentName}Props {
  // props
}

export function {ComponentName}({ ...props }: {ComponentName}Props) {
  // implementation
}
```

2. **Hook Template:**

```typescript
// hooks/{feature}/use{FeatureName}.ts
import { useQuery, useMutation } from '@tanstack/react-query';
import { {feature}Service } from '@/lib/services';

export function use{FeatureName}() {
  // implementation
}
```

3. **Service Template:**

```typescript
// lib/services/{feature}-service.ts
import { httpClient } from '../http-client';
import { {Feature} } from '@/types/{feature}';

export class {Feature}Service {
  async get{Feature}s(): Promise<{Feature}[]> {
    return httpClient.get<{Feature}[]>('/{feature}s');
  }
}

export const {feature}Service = new {Feature}Service();
```

---

## 5. Priority-Based Refactor Plan

### 🔴 HIGH PRIORITY (Immediate Impact)

#### 5.1 Naming Standardization

**Effort:** Medium | **Impact:** High

**Tasks:**

1. Rename component files to PascalCase:
   - `data-table-pagination.tsx` → `DataTablePagination.tsx`
   - `breadcrumbs.tsx` → `Breadcrumbs.tsx`
   - `navbar.tsx` → `Navbar.tsx`
   - `footer.tsx` → `Footer.tsx`

2. Standardize hook naming (all camelCase):
   - `use-breadcrumbs.tsx` → `useBreadcrumbs.ts`
   - `use-services.ts` → `useServices.ts` (or keep if team prefers kebab-case)
   - `use-debounce.ts` → `useDebounce.ts`

3. Move misplaced hooks:
   - `components/comment/useComment.ts` → `hooks/useComment.ts`

**Estimated Time:** 4-6 hours

#### 5.2 API Route Folder Rename

**Effort:** Low | **Impact:** Medium

**Tasks:**

1. Rename `src/app/nextapi/` → `src/app/api/`
2. Update imports if any
3. Update documentation

**Estimated Time:** 1 hour

#### 5.3 React Query Migration (Critical Data)

**Effort:** High | **Impact:** High

**Tasks:**

1. Migrate Tags data fetching to React Query
2. Migrate Locations data fetching to React Query
3. Migrate Users data fetching to React Query
4. Create query key factories for consistency

**Estimated Time:** 8-12 hours

**Benefits:**

- Automatic caching
- Background refetching
- Optimistic updates
- Better error handling
- Loading states

### 🟡 MEDIUM PRIORITY (Quality Improvements)

#### 5.4 Component Reorganization

**Effort:** Medium | **Impact:** Medium

**Tasks:**

1. Create feature-based component folders:
   - `components/features/tags/`
   - `components/features/locations/`
   - `components/features/users/`
   - `components/features/comments/`

2. Move feature components to appropriate folders
3. Create `components/shared/` for common components
4. Update imports

**Estimated Time:** 6-8 hours

#### 5.5 Large Component Refactoring

**Effort:** High | **Impact:** Medium

**Tasks:**

1. Split `Users.tsx` (722 lines):
   - Extract `UsersTable.tsx`
   - Extract `UsersFilters.tsx`
   - Extract `UsersActions.tsx`
   - Create `useUsers.ts` hook

2. Refactor `TagsContainer.tsx`:
   - Extract `TagsFilters.tsx`
   - Extract `TagsActions.tsx`

**Estimated Time:** 8-10 hours

#### 5.6 Form Standardization

**Effort:** Medium | **Impact:** Medium

**Tasks:**

1. Audit all forms
2. Migrate to React Hook Form + Zod
3. Create form validation schemas
4. Create reusable form components

**Estimated Time:** 6-8 hours

### 🟢 LOW PRIORITY (Nice to Have)

#### 5.7 Example Code Cleanup

**Effort:** Low | **Impact:** Low

**Tasks:**

1. Remove or move example files:
   - `components/examples/`
   - `app/sentry-example-*`
   - `components/AuthenticatedApiExample.tsx`

**Estimated Time:** 1-2 hours

#### 5.8 Type Safety Improvements

**Effort:** Medium | **Impact:** Low-Medium

**Tasks:**

1. Audit and remove `any` types
2. Create discriminated unions for API responses
3. Improve error type definitions

**Estimated Time:** 4-6 hours

#### 5.9 Documentation & Templates

**Effort:** Medium | **Impact:** Low-Medium

**Tasks:**

1. Create component template
2. Create hook template
3. Create service template
4. Document naming conventions
5. Create contribution guide

**Estimated Time:** 4-6 hours

---

## 6. Proposed Folder Structure

```
src/
├── app/
│   ├── (auth)/              # Auth route group
│   ├── (globals)/           # Global route group
│   ├── admin/
│   │   ├── dashboard/
│   │   │   └── page.tsx
│   │   ├── tags/
│   │   │   └── page.tsx
│   │   ├── locations/
│   │   │   └── page.tsx
│   │   └── users/
│   │       └── page.tsx
│   ├── api/                 # Renamed from nextapi
│   │   ├── auth/
│   │   └── profile/
│   ├── scout/
│   └── viewer/
├── components/
│   ├── features/            # Feature-specific components
│   │   ├── tags/
│   │   │   ├── TagsContainer.tsx
│   │   │   ├── TagsTable.tsx
│   │   │   ├── AddTagModal.tsx
│   │   │   └── EditTagModal.tsx
│   │   ├── locations/
│   │   │   ├── LocationsContainer.tsx
│   │   │   ├── LocationsTable.tsx
│   │   │   └── LocationCard.tsx
│   │   ├── users/
│   │   │   ├── UsersTable.tsx
│   │   │   ├── AddUserModal.tsx
│   │   │   └── UserEditModal.tsx
│   │   └── comments/
│   │       ├── CommentModal.tsx
│   │       ├── CommentItem.tsx
│   │       └── CommentModalHeader.tsx
│   ├── layout/              # Layout components
│   │   ├── AppLayout.tsx
│   │   ├── PublicLayout.tsx
│   │   ├── Navbar.tsx
│   │   └── Footer.tsx
│   ├── shared/              # Shared/common components
│   │   ├── Breadcrumbs.tsx
│   │   ├── ConfirmDialog.tsx
│   │   ├── DataTablePagination.tsx
│   │   └── IconButton.tsx
│   └── ui/                  # Design system components
│       ├── button.tsx
│       ├── dialog.tsx
│       └── ...
├── hooks/
│   ├── api/                 # API/data hooks
│   │   ├── useTags.ts
│   │   ├── useLocations.ts
│   │   └── useUsers.ts
│   ├── features/           # Feature-specific hooks
│   │   └── comments/
│   │       └── useComment.ts
│   ├── services/           # Service hooks
│   │   └── useServices.ts
│   └── ui/                 # UI hooks
│       ├── useBreadcrumbs.ts
│       ├── useDebounce.ts
│       └── useMobile.ts
├── lib/
│   ├── services/           # API service layer
│   │   ├── tag-service.ts
│   │   ├── location-service.ts
│   │   └── index.ts
│   ├── api-client.ts       # Server-side API client
│   ├── http-client.ts      # Client-side HTTP client
│   ├── auth.ts
│   ├── routes.ts
│   └── utils.ts
├── types/                  # TypeScript type definitions
│   ├── tag.ts
│   ├── user.ts
│   ├── location.ts
│   └── index.ts
└── contexts/              # React contexts
    ├── auth-context.tsx
    └── navbar-context.tsx
```

---

## 7. Naming Standards Guideline

### 7.1 Files

| Type       | Convention | Example                  |
| ---------- | ---------- | ------------------------ |
| Components | PascalCase | `AddTagModal.tsx`        |
| Hooks      | camelCase  | `useTags.ts`             |
| Services   | kebab-case | `tag-service.ts`         |
| Types      | kebab-case | `tag.ts`                 |
| Utils      | kebab-case | `utils.ts`               |
| API Routes | lowercase  | `route.ts`               |
| Pages      | lowercase  | `page.tsx`, `layout.tsx` |

### 7.2 Components

```typescript
// ✅ CORRECT
export function TagsContainer() { }
export default function TagsTable() { }
export const AddTagModal = () => { }

// ❌ INCORRECT
export function tags-container() { }
export default function tagsTable() { }
```

### 7.3 Hooks

```typescript
// ✅ CORRECT
export function useTags() {}
export const useCommentAPI = () => {};

// ❌ INCORRECT
export function UseTags() {}
export function use_tags() {}
```

### 7.4 Variables & Functions

```typescript
// ✅ CORRECT
const handleAddTag = () => {};
const fetchTags = () => {};
const isModalOpen = true;
const tagService = useTagService();

// ❌ INCORRECT
const HandleAddTag = () => {};
const fetch_tags = () => {};
const modalOpen = true; // Missing boolean prefix
```

### 7.5 Constants

```typescript
// ✅ CORRECT
const LIMIT = 6;
const TAG_TYPE_OPTIONS = [...];
const API_BASE_URL = '...';

// ❌ INCORRECT
const limit = 6; // Should be UPPER_CASE for constants
```

---

## 8. Recommended Roadmap

### Phase 1: Foundation (Week 1-2)

**Goal:** Establish consistent naming and structure

1. ✅ Rename component files to PascalCase
2. ✅ Standardize hook naming
3. ✅ Move misplaced hooks
4. ✅ Rename API route folder
5. ✅ Create folder structure

**Deliverables:**

- Consistent naming across codebase
- Proper folder organization
- Updated imports

### Phase 2: Data Layer (Week 3-4)

**Goal:** Standardize data fetching

1. ✅ Migrate Tags to React Query
2. ✅ Migrate Locations to React Query
3. ✅ Migrate Users to React Query
4. ✅ Create query key factories
5. ✅ Standardize error handling

**Deliverables:**

- React Query integration
- Consistent data fetching patterns
- Better caching and loading states

### Phase 3: Component Refactoring (Week 5-6)

**Goal:** Improve component organization and size

1. ✅ Reorganize components by feature
2. ✅ Split large components
3. ✅ Extract custom hooks
4. ✅ Standardize component structure

**Deliverables:**

- Feature-based component organization
- Smaller, focused components
- Reusable hooks

### Phase 4: Forms & Validation (Week 7)

**Goal:** Standardize form handling

1. ✅ Audit all forms
2. ✅ Migrate to React Hook Form + Zod
3. ✅ Create validation schemas
4. ✅ Create reusable form components

**Deliverables:**

- Consistent form patterns
- Type-safe validation
- Reusable form components

### Phase 5: Polish & Documentation (Week 8)

**Goal:** Clean up and document

1. ✅ Remove example code
2. ✅ Improve type safety
3. ✅ Create templates
4. ✅ Document conventions
5. ✅ Create contribution guide

**Deliverables:**

- Clean codebase
- Documentation
- Developer guidelines
- Code templates

---

## 9. Success Metrics

### Before Refactoring

- ❌ Inconsistent naming (mixed conventions)
- ❌ Hooks in wrong locations
- ❌ Large components (700+ lines)
- ❌ Manual data fetching (no React Query)
- ❌ Mixed form patterns

### After Refactoring

- ✅ Consistent naming (100% adherence)
- ✅ Proper folder organization
- ✅ Components <200 lines average
- ✅ React Query for all server state
- ✅ Standardized form patterns
- ✅ AI-friendly code structure

---

## 10. Conclusion

The Scoutr Web App codebase has **solid foundations** but needs **standardization** to become truly vibe-coding ready. The main issues are:

1. **Naming inconsistencies** (easily fixable)
2. **Folder organization** (needs restructuring)
3. **Limited React Query adoption** (high-value improvement)
4. **Large components** (needs splitting)

**Estimated Total Refactoring Time:** 40-50 hours

**Priority Order:**

1. Naming standardization (quick wins)
2. React Query migration (high impact)
3. Component reorganization (maintainability)
4. Form standardization (developer experience)
5. Documentation (long-term value)

Following this roadmap will result in a **clean, predictable, AI-friendly codebase** that enables faster development and better code generation.

---

**Next Steps:**

1. Review and approve this audit
2. Prioritize phases based on team capacity
3. Create tickets for each phase
4. Begin Phase 1 implementation
