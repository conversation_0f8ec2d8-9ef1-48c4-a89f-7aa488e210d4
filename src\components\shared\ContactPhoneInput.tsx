'use client';

import { Input } from '@/components/ui/input';

interface ContactPhoneInputProps {
  value: string; // 10-digit string (digits only)
  onChange: (value: string) => void; // Returns combined 10-digit string
  index?: number;
}

export function ContactPhoneInput({
  value = '',
  onChange,
  index = 0,
}: ContactPhoneInputProps) {
  // Extract only digits from value
  const digits = value.replace(/\D/g, '').slice(0, 10);

  // Split into area code (first 3) and number (last 7)
  const areaCode = digits.slice(0, 3);
  const number = digits.slice(3, 10);

  const formatAreaCode = (inputValue: string) => {
    // Remove all non-digits
    const inputDigits = inputValue.replace(/\D/g, '');
    // Limit to 3 digits
    return inputDigits.slice(0, 3);
  };

  const formatPhoneNumber = (inputValue: string) => {
    // Remove all non-digits
    const inputDigits = inputValue.replace(/\D/g, '');
    // Limit to 7 digits
    return inputDigits.slice(0, 7);
  };

  const formatDisplayNumber = (digits: string) => {
    // Format as XXX-XXXX for display
    if (digits.length <= 3) {
      return digits;
    }
    return `${digits.slice(0, 3)}-${digits.slice(3)}`;
  };

  const handleAreaCodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newAreaCode = formatAreaCode(e.target.value);
    // Combine with existing number to create full 10-digit string
    const combinedValue = newAreaCode + number;
    onChange(combinedValue);

    // Auto-focus to phone number input when area code reaches 3 digits
    if (newAreaCode.length === 3) {
      // Use setTimeout to ensure the DOM is updated before focusing
      setTimeout(() => {
        const phoneInput = document.getElementById(
          `contactPhone-${index}`
        ) as HTMLInputElement;
        phoneInput?.focus();
      }, 0);
    }
  };

  const handleNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newNumber = formatPhoneNumber(e.target.value);
    // Combine with existing area code to create full 10-digit string
    const combinedValue = areaCode + newNumber;
    onChange(combinedValue);
  };

  const displayAreaCode = areaCode ? `(${areaCode})` : '';
  const displayNumber = formatDisplayNumber(number);

  return (
    <div className="flex gap-2">
      <Input
        id={`contactPhoneAreaCode-${index}`}
        value={displayAreaCode}
        onChange={handleAreaCodeChange}
        placeholder="(000)"
        className="h-[50px] w-[75px]"
        maxLength={5} // (XXX) = 5 characters
      />
      <Input
        id={`contactPhone-${index}`}
        value={displayNumber}
        onChange={handleNumberChange}
        placeholder="Enter contact phone"
        className="h-[50px] flex-1"
        maxLength={8} // XXX-XXXX = 8 characters
      />
    </div>
  );
}
