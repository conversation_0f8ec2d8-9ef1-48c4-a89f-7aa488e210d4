import { withSentryConfig } from '@sentry/nextjs';
import type { NextConfig } from 'next';

const nextConfig: NextConfig = {
  /* config options here */
  output: 'standalone',

  // Optimize CSS loading and reduce preload warnings
  experimental: {
    optimizePackageImports: ['lucide-react'],
    optimizeCss: true,
  },

  reactStrictMode: false,

  // Configure webpack to handle CSS chunks better
  webpack: (config, { dev, isServer }) => {
    if (!isServer && dev) {
      // Optimize CSS chunk loading in development
      config.optimization = {
        ...config.optimization,
        splitChunks: {
          ...config.optimization.splitChunks,
          cacheGroups: {
            ...config.optimization.splitChunks.cacheGroups,
            styles: {
              name: 'styles',
              test: /\.(css|scss)$/,
              chunks: 'all',
              enforce: true,
            },
          },
        },
      };
      config.devtool = 'eval';
    }
    return config;
  },
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'picsum.photos',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'images.unsplash.com',
        port: '',
        pathname: '/**',
      },
      // Only add CloudFront pattern if env var is set
      ...(process.env.NEXT_PUBLIC_IMAGE_CLOUDFRONT
        ? [
            {
              protocol: 'https' as const,
              hostname: process.env.NEXT_PUBLIC_IMAGE_CLOUDFRONT,
              port: '',
              pathname: '/**',
            },
          ]
        : []),
    ],
    // Disable image optimization to prevent the "url parameter is not allowed" error
    // This is the simplest solution for AWS EC2 deployment
    unoptimized: true,
  },
};

export default withSentryConfig(nextConfig, {
  // For all available options, see:
  // https://www.npmjs.com/package/@sentry/webpack-plugin#options

  org: 'scoutr-v5',

  project: 'scouts-dev',

  // Only print logs for uploading source maps in CI
  silent: !process.env.CI,

  // For all available options, see:
  // https://docs.sentry.io/platforms/javascript/guides/nextjs/manual-setup/

  // Upload a larger set of source maps for prettier stack traces (increases build time)
  widenClientFileUpload: true,

  // Route browser requests to Sentry through a Next.js rewrite to circumvent ad-blockers.
  // This can increase your server load as well as your hosting bill.
  // Note: Check that the configured route will not match with your Next.js middleware, otherwise reporting of client-
  // side errors will fail.
  tunnelRoute: '/monitoring',

  // Automatically tree-shake Sentry logger statements to reduce bundle size
  disableLogger: true,

  // Enables automatic instrumentation of Vercel Cron Monitors. (Does not yet work with App Router route handlers.)
  // See the following for more information:
  // https://docs.sentry.io/product/crons/
  // https://vercel.com/docs/cron-jobs
  automaticVercelMonitors: true,
});
