import {
  Sidebar,
  SidebarContent,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarTrigger,
} from '@/components/ui/sidebar';
import { DashboardIcon, UsersIcon, LocationIcon, TagIcon } from '@/lib/icons';
import { Routes } from '@/lib/routes';
import { toastInfo } from '@/lib/toast';
import { cn } from '@/lib/utils';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import { useMemo, memo, useCallback } from 'react';

interface SidebarItemProps {
  href: string;
  icon: React.ReactNode;
  children: React.ReactNode;
  isActive: boolean;
  exists?: boolean;
}

const SidebarItem = memo(function SidebarItem({
  href,
  icon,
  children,
  isActive,
  exists = true,
}: SidebarItemProps) {
  const router = useRouter();

  const handleClick = useCallback(
    (e: React.MouseEvent) => {
      if (!exists) {
        e.preventDefault();
        toastInfo('This feature is coming soon!', {
          description: "We're working hard to bring you this functionality.",
        });
        // Navigate to dashboard instead
        router.push(Routes.DASHBOARD);
      }
    },
    [exists, router]
  );

  return (
    <SidebarMenuItem>
      <SidebarMenuButton
        asChild
        isActive={isActive}
        className={cn(
          'leading-5 data-[active=true]:font-normal pl-4 gap-3 h-9 font-medium transition-all duration-200 ease-linear',
          'group-data-[collapsible=offcanvas]:flex-col group-data-[collapsible=offcanvas]:h-auto group-data-[collapsible=offcanvas]:py-2 group-data-[collapsible=offcanvas]:px-[7px] group-data-[collapsible=offcanvas]:gap-2 group-data-[collapsible=offcanvas]:text-xs'
        )}
      >
        <Link href={exists ? href : '#'} onClick={handleClick}>
          {icon}
          <span>{children}</span>
        </Link>
      </SidebarMenuButton>
    </SidebarMenuItem>
  );
});

interface SidebarGroupProps {
  label: string;
  children: React.ReactNode;
}

const SidebarGroupComponent = memo(function SidebarGroupComponent({
  label,
  children,
}: SidebarGroupProps) {
  return (
    <SidebarGroup className="py-6 px-4 gap-6 h-full">
      <div className="flex items-center justify-between h-6 group-data-[collapsible=offcanvas]:justify-center">
        <SidebarGroupLabel className="p-0 text-base text-sidebar-accent-foreground leading-4 group-data-[collapsible=offcanvas]:hidden">
          {label}
        </SidebarGroupLabel>
        <SidebarTrigger />
      </div>
      <SidebarGroupContent>
        <SidebarMenu className="gap-2">{children}</SidebarMenu>
      </SidebarGroupContent>
    </SidebarGroup>
  );
});

export default function AdminSidebar() {
  const pathname = usePathname();

  const isActive = useCallback((href: string) => pathname === href, [pathname]);

  const adminItems = useMemo(
    () => [
      {
        href: Routes.DASHBOARD,
        icon: <DashboardIcon className="h-4 w-4" />,
        label: 'Dashboard',
        exists: true,
      },
      {
        href: Routes.ADMIN_USERS,
        icon: <UsersIcon className="h-4 w-4" />,
        label: 'Users',
        exists: true,
      },
      {
        href: Routes.ADMIN_LOCATIONS,
        icon: <LocationIcon className="h-4 w-4" />,
        label: 'Locations',
        exists: true,
      },
      {
        href: Routes.ADMIN_TAGS,
        icon: <TagIcon className="h-4 w-4" />,
        label: 'Tags',
        exists: true,
      },
    ],
    []
  );

  return (
    <Sidebar className="border-r border-sidebar-border sticky top-0 h-full w-[268px] group-data-[collapsible=offcanvas]:w-[111px]">
      <SidebarContent className="h-full">
        {/* Admin Navigation Group */}
        <SidebarGroupComponent label="Main menu">
          {adminItems.map(item => (
            <SidebarItem
              key={item.href}
              href={item.href}
              icon={item.icon}
              isActive={isActive(item.href)}
              exists={item.exists}
            >
              {item.label}
            </SidebarItem>
          ))}
        </SidebarGroupComponent>
      </SidebarContent>
    </Sidebar>
  );
}
