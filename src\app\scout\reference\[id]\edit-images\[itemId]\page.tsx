'use client';
import { Photo } from '@/types/location';
import AllPhotoGrid from '@/components/AllPhotoGrid';
import { ArrowLeft } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { use, useCallback, useEffect, useRef, useState } from 'react';
import { Heading } from '@/components/ui/typography';
import { Button } from '@/components/ui/button';
import { toastError, toastSuccess } from '@/lib/toast';
import Loading from './loading';
import { useLocationService, useReferenceService } from '@/hooks/use-services';
import { AddReferenceItemData } from '@/lib/services/reference-service';
import { useReference } from '../../reference-context';

export default function EditImages({
  params,
}: {
  params: Promise<{ id: string; itemId: string }>;
}) {
  const router = useRouter();
  const { id, itemId } = use(params);
  const referenceService = useReferenceService();
  const locationService = useLocationService();
  const { refreshReference } = useReference();
  const [images, setImages] = useState<Photo[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [imagesSelected, setImagesSelected] = useState<Photo[]>([]);
  const locationId = useRef<string>('');

  const fetchAllImages = useCallback(
    async (locationId: string) => {
      try {
        const response = await locationService.getLocationById(locationId);
        const result = response.images;
        setImages(result);
      } catch {
        toastError('Failed to get all photos');
      }
    },
    [locationService]
  );

  const fetchImages = useCallback(async () => {
    try {
      setLoading(true);
      const response = await referenceService.getReferenceItem({
        listId: id,
        itemId: itemId,
      });
      const result: Photo[] = response.images.map(img => img.image);
      setImagesSelected(result);
      locationId.current = response.location.id;
      await fetchAllImages(response.location.id);
    } catch {
      toastError('Failed to get photos');
    } finally {
      setLoading(false);
    }
  }, [fetchAllImages, id, itemId, referenceService]);

  const handleSelectImage = (image: Photo) => {
    setImagesSelected(prev => {
      const isExist = prev.find(p => p.id === image.id);
      return isExist ? prev.filter(p => p.id !== image.id) : [...prev, image];
    });
  };
  const handleSave = async () => {
    try {
      const payload: AddReferenceItemData = {
        locationId: locationId.current,
        imageIds: imagesSelected.map(img => img.id ?? ''),
      };
      await referenceService.updateReferenceItems(
        { listId: id, itemId },
        payload
      );
      toastSuccess('Images updated successfully.');
      // Refresh reference data in context
      await refreshReference();
      router.push(`/scout/reference/${id}`);
    } catch {
      toastError('Failed to update reference');
    }
  };

  useEffect(() => {
    if (!itemId || !id) return;
    fetchImages();
  }, [fetchImages, id, itemId]);

  return (
    <>
      {loading && <Loading />}
      {!loading && (
        <div className="w-full flex justify-center">
          <div className="space-y-11 w-full xl:w-[70.5rem] p-11 xl:px-0">
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-4">
              <div
                onClick={() => router.push(`/scout/reference/${id}`)}
                className="cursor-pointer inline-flex items-center gap-3 text-sm text-gray-600 hover:text-gray-900 transition-colors"
              >
                <ArrowLeft className="h-5 w-5" />
                <div className="flex flex-col justify-start">
                  <Heading level={3}>Edit Images</Heading>
                  <p>Update the images shown in your reference list.</p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Button
                  onClick={() => router.push(`/scout/reference/${id}`)}
                  variant="outline"
                  className="w-full sm:w-fit h-10"
                >
                  Cancel
                </Button>
                <Button
                  disabled={!imagesSelected.length}
                  onClick={handleSave}
                  className="w-full sm:w-fit h-10"
                >
                  Save Changes
                </Button>
              </div>
            </div>
            <AllPhotoGrid
              images={images}
              onClickImage={handleSelectImage}
              imagesSelected={imagesSelected}
            />
          </div>
        </div>
      )}
    </>
  );
}
