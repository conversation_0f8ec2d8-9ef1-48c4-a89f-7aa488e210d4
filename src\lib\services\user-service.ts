import { UserStatus } from '@/types';
import { httpClient } from '../http-client';
import { User, UserProfileUpdate } from '@/types/user';
const buildQueryParams = (params: {
  page?: number;
  search?: string;
  role?: string;
  status?: string;
  limit?: number;
}) => {
  const { page, search, role, status, limit } = params;
  const queryParams: string[] = [];
  if (page) {
    queryParams.push(`page=${page}`);
  }
  if (limit) {
    queryParams.push(`limit=${limit}`);
  }
  if (search && search !== '') {
    queryParams.push(`search=${search}`);
  }
  if (role && role !== 'all') {
    queryParams.push(`role=${role}`);
  }
  if (status && status !== 'all') {
    queryParams.push(`status=${status}`);
  }
  return queryParams.join('&');
};
/**
 * User Service - Handles all user-related API operations
 */
export class UserService {
  /**
   * Get current user profile
   */
  async getProfile(): Promise<User> {
    return httpClient.get<User>('/users/me');
  }

  /**
   * Update current user profile
   */
  async updateProfile(data: UserProfileUpdate): Promise<User> {
    return httpClient.put<User>('/users/me', data);
  }

  /**
   * Get all users (admin only)
   */
  async getUsers(
    page?: number,
    search?: string,
    role?: string,
    status?: string,
    limit?: number
  ): Promise<{
    data: User[];
    meta: { totalPages: number; totalItems: number };
  }> {
    const queryParams = buildQueryParams({
      page,
      search,
      role,
      status,
      limit,
    });

    return httpClient.get<{
      data: User[];
      meta: { totalPages: number; totalItems: number };
    }>(`/users?${queryParams}`);
  }

  async getUsersRequests(
    page?: number,
    search?: string,
    role?: string,
    limit?: number
  ): Promise<{
    data: User[];
    meta: { totalPages: number; totalItems: number };
  }> {
    const queryParams = buildQueryParams({
      page,
      search,
      role,
      limit,
      status: UserStatus.PENDING,
    });

    return httpClient.get<{
      data: User[];
      meta: { totalPages: number; totalItems: number };
    }>(`/users?${queryParams}`);
  }

  /**
   * Get user by ID
   */
  async getUserById(id: string): Promise<User> {
    return httpClient.get<User>(`/users/${id}`);
  }

  /**
   * Create new user (admin only)
   */
  async createUser(userData: Partial<User>): Promise<User> {
    const { productionHouse, ...rest } = userData;
    return httpClient.post<User>('/auth/invite', {
      ...rest,
      productionHouseId: productionHouse?.id || undefined,
    });
  }

  /**
   * Update user by ID (admin only)
   */
  async updateUser(id: string, userData: Partial<User>): Promise<User> {
    const { productionHouse, ...rest } = userData;
    return httpClient.patch<User>(`/users/${id}`, {
      ...rest,
      productionHouseId: productionHouse?.id || undefined,
    });
  }

  /**
   * Update user status (admin only)
   */
  async updateUserStatus(
    id: string,
    status: 'active' | 'disabled'
  ): Promise<User> {
    return httpClient.patch<User>(`/users/${id}/status`, { status });
  }

  /**
   * Accept user
   */
  async acceptUser(id: string): Promise<User> {
    return httpClient.post<User>(`/users/requests/${id}/accept`);
  }

  /**
   * Reject user
   */
  async rejectUser(id: string): Promise<User> {
    return httpClient.post<User>(`/users/requests/${id}/reject`);
  }

  /**
   * Delete user by ID (admin only)
   */
  async deleteUser(id: string): Promise<void> {
    return httpClient.delete<void>(`/users/${id}`);
  }

  /**
   * Upload user avatar
   */
  async uploadAvatar(file: File): Promise<{ url: string }> {
    return httpClient.uploadFile<{ url: string }>('/users/avatar', file);
  }

  /**
   * Change user password
   */
  async changePassword(
    currentPassword: string,
    newPassword: string
  ): Promise<void> {
    return httpClient.post<void>('/users/change-password', {
      currentPassword,
      newPassword,
    });
  }
}

// Export singleton instance
export const userService = new UserService();
