'use client';

import { ScrollArea } from '@/components/ui/scroll-area';
import AdminSidebar from '@/app/(admin)/admin-sidebar';
import { usePathname } from 'next/navigation';

interface AdminLayoutWithSidebarProps {
  children: React.ReactNode;
}

// Routes that should not show the sidebar
const withoutSidebarPathNames = [
  '/locations/create-location',
  '/locations/edit-location',
  '/locations/submit-location',
];

export function AdminLayoutWithSidebar({
  children,
}: AdminLayoutWithSidebarProps) {
  const pathname = usePathname();

  const shouldShowSidebar = !withoutSidebarPathNames.some(path =>
    pathname.startsWith(path)
  );

  return (
    <div className="flex h-full w-full">
      {shouldShowSidebar && <AdminSidebar />}

      <ScrollArea className="h-[calc(100dvh-var(--navbar-height))] w-full">
        {children}
      </ScrollArea>
    </div>
  );
}
