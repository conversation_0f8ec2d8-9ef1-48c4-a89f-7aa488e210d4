import { Heading, Body } from '@/components/ui/typography';

interface PropertyOwnerDashboardHeaderProps {
  userName?: string | null;
}

export function PropertyOwnerDashboardHeader({
  userName,
}: PropertyOwnerDashboardHeaderProps) {
  return (
    <div className="flex flex-col gap-2">
      <Heading
        level={3}
        className="font-semibold text-2xl leading-9 text-black"
      >
        Welcome back{userName ? `, ${userName}` : ''}
      </Heading>
      <Body className="text-base leading-7 text-neutral-600">
        Manage your locations here.
      </Body>
    </div>
  );
}
