import { Reference, ReferenceItem, ResponseImageItem } from '@/types';
import { httpClient } from '../http-client';
import { ProductionHouse } from './production-service';
import { Location } from '@/types/location';

export interface CreateReferenceData {
  projectName: string;
  productionHouseId: string;
  shootDateStart: string;
  shootDateEnd: string;
  internalNotes?: string;
}

export interface UpdateReferenceData extends Partial<CreateReferenceData> {
  id: string;
}

export interface ReferenceFilters {
  search?: string;
  productionHouse?: string;
  status?: string;
  dateRange?: {
    from: string;
    to: string;
  };
  page?: number;
  limit?: number;
}

export interface ReferenceListResponse {
  data: Reference[];
  meta: {
    currentPage: number;
    itemCount: number;
    itemsPerPage: number;
    totalItems: number;
    totalPages: number;
  };
}

interface ReferenceResponseItem {
  id: string;
  commentCount: number;
  createdAt: string;
  updatedAt: string;
  location: Location;
  images: ResponseImageItem[];
}
export interface ReferenceResponse {
  id: string;
  projectName: string;
  productionHouse: ProductionHouse;
  shootDateStart: string;
  shootDateEnd: string;
  internalNotes?: string;
  status: string;
  coverImageUrl?: string;
  items: ReferenceResponseItem[];
}

export interface AddReferenceItemData {
  locationId: string;
  imageIds: string[];
  comment?: string;
}

export interface QuickAddReferenceItemData {
  locationId: string;
  searchTagIds?: string[];
}

export class ReferenceService {
  async getReferences(
    filters: ReferenceFilters = {}
  ): Promise<ReferenceListResponse> {
    const params = new URLSearchParams();

    if (filters.search) params.append('search', filters.search);
    if (filters.productionHouse && filters.productionHouse !== 'all') {
      params.append('productionHouse', filters.productionHouse);
    }
    if (filters.status && filters.status !== 'all') {
      params.append('status', filters.status);
    }
    if (filters.dateRange?.from) {
      params.append('dateFrom', filters.dateRange.from);
    }
    if (filters.dateRange?.to) {
      params.append('dateTo', filters.dateRange.to);
    }
    if (filters.page) params.append('page', filters.page.toString());
    if (filters.limit) params.append('limit', filters.limit.toString());

    return httpClient.get<ReferenceListResponse>(
      `/reference-lists?${params.toString()}`
    );
  }

  async getReference(id: string, search?: string): Promise<Reference> {
    return httpClient.get<Reference>(
      `/reference-lists/${id}${search ? `?search=${search}` : ''}`
    );
  }

  async createReference(data: CreateReferenceData): Promise<Reference> {
    return httpClient.post<Reference>('/reference-lists', data);
  }

  async updateReference(data: UpdateReferenceData): Promise<Reference> {
    const { id, ...updateData } = data;
    return httpClient.patch<Reference>(`/reference-lists/${id}`, updateData);
  }

  async deleteReference(id: string): Promise<void> {
    return httpClient.delete(`/reference-lists/${id}`);
  }

  async addReferenceItems(
    listId: string,
    data: AddReferenceItemData
  ): Promise<Reference> {
    return httpClient.post<Reference>(`/reference-lists/${listId}/items`, data);
  }

  async updateReferenceItems(
    { listId, itemId }: { listId: string; itemId: string },
    data: AddReferenceItemData
  ): Promise<Reference> {
    return httpClient.patch<Reference>(
      `/reference-lists/${listId}/items/${itemId}`,
      data
    );
  }

  async quickAddReferenceItems(
    listId: string,
    data: QuickAddReferenceItemData
  ): Promise<Reference> {
    return httpClient.post<Reference>(
      `/reference-lists/${listId}/items/quick-add`,
      data
    );
  }

  async getReferenceItem({
    listId,
    itemId,
  }: {
    listId: string;
    itemId: string;
  }): Promise<ReferenceItem> {
    return httpClient.get<ReferenceItem>(
      `/reference-lists/${listId}/items/${itemId}`
    );
  }

  async deleteReferenceItem({
    listId,
    itemId,
  }: {
    listId: string;
    itemId: string;
  }): Promise<void> {
    return httpClient.delete(`/reference-lists/${listId}/items/${itemId}`);
  }

  async shareReferenceList(listId: string, emails: string[]): Promise<void> {
    return httpClient.post<void>(`/reference-lists/${listId}/share/email`, {
      emails,
    });
  }
}

export const referenceService = new ReferenceService();
