'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Plus } from 'lucide-react';

interface EmptyLocationListProps {
  onSubmitLocation: () => void;
}

export function EmptyLocationList({
  onSubmitLocation,
}: EmptyLocationListProps) {
  return (
    <div className="flex flex-col items-center justify-center py-12 px-4">
      <div className="text-center max-w-md">
        <h2 className="text-2xl font-semibold text-neutral-900 mb-2">
          No locations yet
        </h2>
        <p className="text-base text-neutral-600 mb-6">
          Get started by submitting your first location to the platform.
        </p>
        <Button
          onClick={onSubmitLocation}
          variant="default"
          className="bg-black hover:bg-black/90 text-white h-[41px] px-4 py-2 rounded-lg"
        >
          <Plus className="h-6 w-6 mr-2" />
          Submit Location
        </Button>
      </div>
    </div>
  );
}
