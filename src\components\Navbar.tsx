'use client';

import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { SunIcon, MoonIcon } from 'lucide-react';
import { useTheme } from 'next-themes';
import { memo, useState, useRef, useEffect } from 'react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuPortal,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useAuth } from '@/contexts/auth-context';
import { useNavbar } from '@/contexts/navbar-context';
import { Routes } from '@/lib/routes';
import { UserAvatarProfile } from './features/users/UserAvatarProfile';
import React from 'react';
import { useRouter } from 'next/navigation';
import { Skeleton } from './ui/skeleton';
import Image from 'next/image';
import { ChevronDownIcon } from 'lucide-react';
import { toastSuccess } from '@/lib/toast';
import { getDisplayText, getRoleDisplayName } from '@/lib/utils';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip';

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const ThemeToggle = memo(function ThemeToggle() {
  const { theme, setTheme } = useTheme();

  const handleThemeToggle = React.useCallback(
    (e: React.MouseEvent) => {
      const newTheme = theme === 'light' ? 'dark' : 'light';
      const root = document.documentElement;

      if (!document.startViewTransition) {
        setTheme(newTheme);
        return;
      }

      // Set coordinates from the click event for the ripple effect
      if (e) {
        root.style.setProperty('--x', `${e.clientX}px`);
        root.style.setProperty('--y', `${e.clientY}px`);
      }

      // Start the view transition
      document.startViewTransition(() => {
        setTheme(newTheme);
      });
    },
    [theme, setTheme]
  );

  return (
    <Button
      variant="ghost"
      size="icon"
      onClick={handleThemeToggle}
      className="relative size-8 transition-all duration-300 hover:scale-110 active:scale-95 rounded-full border border-gray-200"
    >
      <SunIcon className="h-4 w-4 rotate-0 scale-100 transition-all duration-500 dark:-rotate-90 dark:scale-0" />
      <MoonIcon className="absolute h-4 w-4 rotate-90 scale-0 transition-all duration-500 dark:rotate-0 dark:scale-100" />
      <span className="sr-only">Toggle theme</span>
    </Button>
  );
});

export function Navbar() {
  const { user, signOut, loading } = useAuth();
  const { additionalItems } = useNavbar();
  const router = useRouter();
  const [open, setOpen] = useState(false);
  const nameRef = useRef<HTMLSpanElement>(null);
  const companyRef = useRef<HTMLParagraphElement>(null);
  const [isNameTruncated, setIsNameTruncated] = useState(false);
  const [isCompanyTruncated, setIsCompanyTruncated] = useState(false);
  const roleDisplayName = user ? getRoleDisplayName(user.role) : '';

  const handleSignOut = async () => {
    try {
      await signOut();
      toastSuccess('Signed out successfully!');
    } catch (error) {
      console.error('Sign out error:', error);
      // Still redirect even if there's an error
      router.push(Routes.SIGN_IN);
    }
  };

  // Check if text is truncated
  useEffect(() => {
    const checkTruncation = () => {
      if (nameRef.current) {
        setIsNameTruncated(
          nameRef.current.scrollWidth > nameRef.current.clientWidth
        );
      }
      if (companyRef.current) {
        setIsCompanyTruncated(
          companyRef.current.scrollWidth > companyRef.current.clientWidth
        );
      }
    };

    checkTruncation();
    window.addEventListener('resize', checkTruncation);
    return () => window.removeEventListener('resize', checkTruncation);
  }, [user]);

  if (loading) {
    return (
      <header className="w-full sticky top-0 z-50 flex h-[var(--navbar-height)] px-4 sm:px-6 md:px-19 shrink-0 items-center justify-between gap-2 transition-[width,height] ease-linear border-b border-border">
        <div className="flex items-center gap-2">
          <Link href={Routes.DASHBOARD}>
            <div className="relative w-[124px] h-[40px]">
              <Image
                src="/assets/logo.svg"
                alt="Scoutr Logo"
                fill
                priority
                className="object-contain"
              />
            </div>
          </Link>
        </div>
        <div className="flex items-center gap-2">
          <Skeleton className="h-8 w-8 rounded-full" />
        </div>
      </header>
    );
  }

  return (
    <header className="w-full sticky top-0 z-50 flex h-[var(--navbar-height)] px-4 sm:px-6 md:px-19 shrink-0 items-center justify-between gap-2 transition-[width,height] ease-linear border-b border-border">
      <div className="flex items-center gap-4">
        <Link href={Routes.DASHBOARD}>
          <div className="relative w-[124px] h-[40px]">
            <Image
              src="/assets/logo.svg"
              alt="Scoutr Logo"
              fill
              priority
              className="object-contain"
            />
          </div>
        </Link>

        {/* Additional Navbar Items */}
        {additionalItems.length > 0 && (
          <div className="flex items-center gap-2 ml-12">
            {additionalItems
              .filter(item => item.align === 'left')
              .map(item => {
                if (item.href) {
                  return (
                    <Link key={item.id} href={item.href}>
                      <Button
                        variant={item.variant || 'ghost'}
                        size="sm"
                        disabled={item.disabled}
                        className={`flex items-center gap-2 font-normal text-black text-base h-11 ${item.iconPosition === 'right' ? 'flex-row-reverse' : ''}`}
                      >
                        {item.icon}
                        {item.label}
                      </Button>
                    </Link>
                  );
                }

                return (
                  <Button
                    key={item.id}
                    variant={item.variant || 'ghost'}
                    size="sm"
                    disabled={item.disabled}
                    onClick={item.onClick}
                    className={`flex items-center gap-2 font-normal text-black text-base h-11 ${item.iconPosition === 'right' ? 'flex-row-reverse' : ''}`}
                  >
                    {item.icon}
                    {item.label}
                  </Button>
                );
              })}
          </div>
        )}
      </div>
      <div className="flex items-center gap-4">
        <div className="flex items-center gap-2">
          {additionalItems
            .filter(item => item.align === 'right')
            .map(item => {
              return (
                <Button
                  key={item.id}
                  variant={item.variant || 'ghost'}
                  size="sm"
                  disabled={item.disabled}
                  onClick={item.onClick}
                  className={`flex items-center gap-2 font-normal text-black text-base h-11 ${item.iconPosition === 'right' ? 'flex-row-reverse' : ''} ${item.className}`}
                >
                  {item.icon}
                  {item.label}
                  {item.badge}
                </Button>
              );
            })}
        </div>
        {user && (
          <>
            <DropdownMenu modal={false} open={open} onOpenChange={setOpen}>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  className="flex items-center space-x-0 sm:space-x-2 p-0 relative outline-none focus-visible:ring-0 hover:bg-transparent"
                >
                  <UserAvatarProfile user={user} />
                  <div className="flex flex-col text-sub-header text-left">
                    <div className="flex items-center gap-2">
                      {isNameTruncated ? (
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span
                              ref={nameRef}
                              className="text-sm font-medium max-w-[160px] truncate cursor-help"
                            >
                              {user.firstName || getDisplayText(user.role)}{' '}
                              {user.lastName || ''}
                            </span>
                          </TooltipTrigger>
                          <TooltipContent>
                            {user.firstName || getDisplayText(user.role)}{' '}
                            {user.lastName || ''}
                          </TooltipContent>
                        </Tooltip>
                      ) : (
                        <span
                          ref={nameRef}
                          className="text-sm font-medium max-w-[160px] truncate"
                        >
                          {user.firstName || getDisplayText(user.role)}{' '}
                          {user.lastName || ''}
                        </span>
                      )}
                      <ChevronDownIcon
                        className={`h-4 w-4 transition-transform duration-300 text-primary-400 ${open ? 'rotate-180' : ''}`}
                      />
                    </div>
                    {isCompanyTruncated ? (
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <p
                            ref={companyRef}
                            className="text-sm leading-5 font-normal max-w-[160px] truncate cursor-help capitalize"
                          >
                            {roleDisplayName}
                          </p>
                        </TooltipTrigger>
                        <TooltipContent className="capitalize">
                          {roleDisplayName}
                        </TooltipContent>
                      </Tooltip>
                    ) : (
                      <p
                        ref={companyRef}
                        className="text-sm leading-5 font-normal max-w-[160px] truncate capitalize"
                      >
                        {roleDisplayName}
                      </p>
                    )}
                  </div>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuPortal>
                <DropdownMenuContent
                  align="end"
                  className="w-51 border-slate-200 p-1"
                  sideOffset={8}
                >
                  <DropdownMenuLabel className="font-semibold pl-8 pr-2">
                    {/* <div className="flex flex-col space-y-1">
                      <p className="text-sm font-medium leading-none">
                        {user.firstName || getDisplayText(user.role)}{' '}
                        {user.lastName || ''}
                      </p>
                      <p className="text-xs leading-none text-muted-foreground">
                        {user.email}
                      </p>
                    </div> */}
                    Options
                  </DropdownMenuLabel>
                  <DropdownMenuItem asChild className="pl-8 pr-2">
                    <Link href={Routes.PROFILE} className="cursor-pointer">
                      Profile
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={handleSignOut}
                    className="cursor-pointer pl-8 pr-2"
                  >
                    Log out
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenuPortal>
            </DropdownMenu>
          </>
        )}
      </div>
    </header>
  );
}
