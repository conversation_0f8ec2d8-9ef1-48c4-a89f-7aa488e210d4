'use client';

import { Skeleton } from '@/components/ui/skeleton';

export default function Loading() {
  return (
    <>
      <div className="flex flex-1 flex-col items-center py-4 overflow-y-auto">
        <div className="w-full sm:w-[unset] sm:max-w-4xl space-y-8">
          {/* Back link */}
          <div className="inline-flex items-center gap-2 w-40">
            <Skeleton className="h-4 w-4 rounded" />
            <Skeleton className="h-4 w-36" />
          </div>

          <div className="w-full sm:w-[42rem] flex flex-col gap-8">
            <div className="space-y-6">
              {/* Title & description */}
              <div className="space-y-2">
                <Skeleton className="h-7 w-44" />
                <Skeleton className="h-4 w-80" />
              </div>

              {/* Basic details heading */}
              <Skeleton className="h-6 w-40" />

              {/* Basic details form */}
              <div className="space-y-4">
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-24 w-full" />
              </div>

              {/* Tags heading & helper */}
              <Skeleton className="h-6 w-24" />
              <Skeleton className="h-4 w-[32.5rem]" />

              {/* Tags form */}
              <div className="space-y-4">
                <Skeleton className="h-10 w-full" />
                <div className="grid grid-cols-2 gap-3">
                  <Skeleton className="h-10 w-full" />
                  <Skeleton className="h-10 w-full" />
                </div>
              </div>

              {/* Photos grid */}
              <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
                {Array.from({ length: 6 }).map((_, i) => (
                  <Skeleton key={i} className="h-40 w-full" />
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
      {/* Sticky footer actions */}
      <div className="bg-white z-50 py-5 px-4 sm:px-0 border-t flex justify-center items-center">
        <div className="w-[37.875rem] flex items-center justify-between">
          <Skeleton className="h-10 w-24" />
          <Skeleton className="h-10 w-32" />
        </div>
      </div>
    </>
  );
}
