import { PropertyType, TagType } from './enum';

export const MINIMUM_IMAGES = 5;
export const TAG_SIZE_OPTIONS = ['small', 'medium', 'large'];

// File validation
export const FILE_VALIDATION = {
  MAX_SIZE: 10 * 1024 * 1024, // 10MB
  ALLOWED_TYPES: ['image/jpeg', 'image/jpg', 'image/png'],
};

export const TAG_TYPE_OPTIONS: Record<PropertyType, string> = {
  [PropertyType.RESIDENTIAL]: 'Residential',
  [PropertyType.COMMERCIAL]: 'Commercial',
  [PropertyType.PUBLIC]: 'Public',
};

export const PROPERTY_CATEGORY: Record<PropertyType, TagType[]> = {
  [PropertyType.RESIDENTIAL]: [
    TagType.BUILDING_TYPE,
    TagType.STRUCTURE_TYPE,
    TagType.STYLE,
    TagType.INTERIOR_FEATURE,
    TagType.EXTERIOR_FEATURE,
  ],
  [PropertyType.COMMERCIAL]: [
    TagType.BUILDING_TYPE,
    TagType.FUNCTION_TYPE,
    TagType.INTERIOR_FEATURE,
    TagType.EXTERIOR_FEATURE,
  ],
  [PropertyType.PUBLIC]: [
    TagType.SPACE_TYPE,
    TagType.FUNCTION_TYPE,
    TagType.EXTERIOR_FEATURE,
    TagType.INTERIOR_FEATURE,
  ],
};

// Location size display text
export const LOCATION_SIZE_TEXT: Record<string, string> = {
  small: 'Small (< 1,000 sq ft)',
  medium: 'Medium (1,000–5,000 sq ft)',
  large: 'Large (> 5,000 sq ft)',
};

export const getLocationSizeText = (size?: string): string => {
  if (!size) return '—';
  return LOCATION_SIZE_TEXT[size.toLowerCase()] || size;
};
