import { useState, useCallback, useEffect } from 'react';
import { DateRange } from 'react-day-picker';
import { Reference } from '@/types';
import { referenceService, ReferenceFilters } from '@/lib/services';
import { toastError } from '@/lib/toast';

interface UseDashboardProjectsProps {
  search: string;
  status: string;
  dateRange: DateRange | undefined;
}

export function useDashboardProjects({
  search,
  status,
  dateRange,
}: UseDashboardProjectsProps) {
  const [projectData, setProjectData] = useState<Reference[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isInitialLoading, setIsInitialLoading] = useState<boolean>(true);
  const [isLoadingMore, setIsLoadingMore] = useState<boolean>(false);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [page, setPage] = useState<number>(1);
  const [totalPages, setTotalPages] = useState<number>(0);
  const [totalCount, setTotalCount] = useState<number>(0);
  const [hasFilters, setHasFilters] = useState<boolean>(false);

  const fetchProjects = useCallback(
    async (
      pageNum: number,
      searchQuery?: string,
      dateRangeQuery?: DateRange,
      statusQuery?: string,
      isLoadMore: boolean = false
    ) => {
      try {
        if (!isLoadMore) {
          setIsLoading(true);
          setIsLoadingMore(false);
        } else {
          setIsLoadingMore(true);
        }

        const filters: ReferenceFilters = {
          page: pageNum,
          limit: 6,
          search: searchQuery || undefined,
          status: statusQuery !== 'all' ? statusQuery : undefined,
          dateRange:
            dateRangeQuery?.from && dateRangeQuery?.to
              ? {
                  from: dateRangeQuery.from.toISOString(),
                  to: dateRangeQuery.to.toISOString(),
                }
              : undefined,
        };

        const response = await referenceService.getReferences(filters);

        setProjectData(prev =>
          isLoadMore ? [...prev, ...response.data] : response.data
        );
        setTotalPages(response.meta.totalPages);
        setTotalCount(response.meta.totalItems);
        setHasMore(response.meta.currentPage < response.meta.totalPages);
        setHasFilters(
          !!(
            searchQuery ||
            (statusQuery && statusQuery !== 'all') ||
            dateRangeQuery
          )
        );

        if (pageNum === 1) {
          setIsInitialLoading(false);
        }
      } catch (error) {
        console.error('Error fetching projects:', error);
        toastError('Failed to load projects');
      } finally {
        setIsLoading(false);
        setIsLoadingMore(false);
      }
    },
    []
  );

  const fetchMoreData = useCallback(() => {
    if (page >= totalPages || !hasMore) return;
    const nextPage = page + 1;
    setPage(nextPage);
    fetchProjects(nextPage, search, dateRange, status, true);
  }, [page, totalPages, hasMore, fetchProjects, search, dateRange, status]);

  // Reset to first page when filters change
  useEffect(() => {
    if (page !== 1) {
      setPage(1);
    } else {
      fetchProjects(1, search, dateRange, status);
    }
  }, [search, dateRange, status, fetchProjects, page]);

  // Handle page changes for pagination
  useEffect(() => {
    if (page > 1) {
      fetchProjects(page, search, dateRange, status, true);
    }
  }, [page, fetchProjects, search, dateRange, status]);

  return {
    projectData,
    isLoading,
    isInitialLoading,
    isLoadingMore,
    hasMore,
    totalCount,
    hasFilters,
    fetchMoreData,
  };
}
