'use client';

import { useAuth } from '@/contexts/auth-context';
import type { Comment } from '@/types';
import { CommentModalBody } from '@/components/comment/CommentModalBody';
import { CommentModalFooter } from '@/components/comment/CommentModalFooter';
import { X } from 'lucide-react';

interface CommentOnImageProps {
  imageIndex: number; // zero-based
  comments: Comment[];
  loading: boolean;
  isCreating: boolean;
  onComment: (content: string) => void;
  onDelete: (commentId: string) => void;
  onEdit: (commentId: string, content: string) => void;
  onCloseSidebar: (data: boolean) => void;
  onBack: () => void;
}

export function CommentOnImage({
  imageIndex,
  comments,
  loading,
  isCreating,
  onComment,
  onDelete,
  onEdit,
  onCloseSidebar,
  onBack,
}: CommentOnImageProps) {
  const { user } = useAuth();

  const title = `Image ${imageIndex + 1}`;

  return (
    <aside className="w-full max-w-[29rem] border-l bg-white flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between px-4 pt-4">
        <p className="px-2 font-bold">{title}</p>
        <X onClick={onBack} className="w-4 h-4 cursor-pointer" />
      </div>
      <p className="px-6 mt-2">
        {comments.length > 1
          ? `${comments.length} comments`
          : `${comments.length} comment`}
      </p>
      {/* Body */}
      <div className="flex-1 min-h-0">
        <CommentModalBody
          comments={comments}
          user={user}
          loading={loading}
          onDelete={onDelete}
          onEdit={onEdit}
        />
      </div>

      {/* Footer */}
      <CommentModalFooter
        user={user}
        isCreating={isCreating}
        onComment={onComment}
        onCancel={() => {
          onCloseSidebar(false);
        }}
      />
    </aside>
  );
}
