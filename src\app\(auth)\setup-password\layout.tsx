import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Setup Password | Scoutr',
  description: 'Set up your password to complete your Scoutr account setup',
  openGraph: {
    title: 'Setup Password | Scoutr',
    description: 'Set up your password to complete your Scoutr account setup',
  },
};

export default function SetupPasswordLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
}
