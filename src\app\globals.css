@import url('https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap');
@import 'tailwindcss';
@import 'tw-animate-css';

@source inline("{hover:,focus:,}bg-{red,yellow,green,blue,orange,purple,pink,teal,cyan,gray,black}-{500,600}");
@source inline("{hover:,focus:,}border-{red,yellow,green,blue,orange,purple,pink,teal,cyan,gray,black}-{300,400,500,600}");

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-header: var(--header);
  --color-sub-header: var(--sub-header);
  --font-sans:
    'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
    'Helvetica Neue', Arial, sans-serif;
  --font-mono:
    'Inter', ui-monospace, SFMono-Regular, 'SF Mono', Consolas,
    'Liberation Mono', Menlo, monospace;

  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-primary-50: var(--primary-50);
  --color-primary-100: var(--primary-100);
  --color-primary-200: var(--primary-200);
  --color-primary-300: var(--primary-300);
  --color-primary-400: var(--primary-400);
  --color-primary-500: var(--primary-500);

  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-badge-secondary: var(--badge-secondary);

  --color-destructive-50: var(--destructive-50);
  --color-destructive-400: var(--destructive-400);

  --color-success-100: var(--success-100);
  --color-success-600: var(--success-600);

  /* Popover */
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);

  /* Card */
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --color-card-background: var(--card-background);
  --color-card-border: var(--card-border);
  --color-card-border-secondary: var(--card-border-secondary);

  /* Navbar */
  --color-navbar-foreground: var(--navbar-foreground);

  /* Sidebar */
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--header);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);

  /* Sonner Toaster */
  --color-sonner-foreground: var(--sonner-foreground);

  /* Input */
  --color-input-placeholder: var(--input-placeholder);
  --color-input-label: var(--input-label);

  /* Dialog */
  --color-overlay: var(--overlay);
}

:root {
  --radius: 0.625rem;
  --background: #ffffff;
  --foreground: #525252;
  --header: #171717;
  --sub-header: #404040;

  --primary: #5b0677;
  --primary-foreground: #f8fafc;
  --primary-50: #f6edf7;
  --primary-100: #ceb4d6;
  --primary-200: #ad82bb;
  --primary-300: #8c51a0;
  --primary-400: #6b1f85;
  --primary-500: #5b0677;
  --secondary: #f1f5f9;
  --secondary-foreground: #5b0677;
  --muted: #f1f5f9;
  --muted-foreground: #64748b;
  --accent: #f1f5f9;
  --accent-foreground: #5b0677;

  --border: #d4d4d4;
  --input: #94a3b8;
  --input-placeholder: #adaebc;
  --ring: #94a3b8;
  --badge-secondary: #d9d9d9;

  --destructive-50: #fef2f2;
  --destructive-400: #f87171;

  --success-100: #dcfce7;
  --success-600: #16a34a;

  /* Card */
  --card: #ffffff;
  --card-background: #fafafa;
  --card-foreground: #020617;
  --card-border: #e5e5e5;
  --card-border-secondary: #f5f5f5;

  /* Popover */
  --popover: #ffffff;
  --popover-foreground: #020617;

  /* Sidebar */
  --sidebar-width: 22.5rem;
  /* 360px */
  --sidebar-border: #d4d4d4;
  --sidebar-foreground: #525252;
  --sidebar-accent: #f6edf7;

  /* Navbar */
  --navbar-foreground: #404040;
  --navbar-height: 4rem;
  /* 64px */

  /* Sonner Toaster */
  --sonner-foreground: #020617;
  --sonner-border: #e2e8f0;

  /* Dialog */
  --overlay: #bfbfbf;

  /* Photo Gallery */
  --focused-outline-color: #4c9ffe;
  --box-shadow-border: 0 0 0 calc(1px / var(--scale-x, 1))
    rgba(63, 63, 68, 0.05);
  --box-shadow-common: 0 1px calc(3px / var(--scale-x, 1)) 0
    rgba(34, 33, 81, 0.15);
  --box-shadow: var(--box-shadow-border), var(--box-shadow-common);

  @media (min-width: 768px) {
    --navbar-height: 5.5rem;
    /* 88px */
  }
}

@layer base {
  * {
    @apply border-border outline-ring/50;
    font-family:
      'Inter',
      system-ui,
      -apple-system,
      BlinkMacSystemFont,
      'Segoe UI',
      Roboto,
      'Helvetica Neue',
      Arial,
      sans-serif;
  }

  body {
    @apply bg-background text-foreground;
    font-family:
      'Inter',
      system-ui,
      -apple-system,
      BlinkMacSystemFont,
      'Segoe UI',
      Roboto,
      'Helvetica Neue',
      Arial,
      sans-serif;
    transition:
      background-color 0.2s ease,
      color 0.2s ease;
  }

  * {
    transition:
      background-color 0.2s ease,
      border-color 0.2s ease,
      color 0.2s ease,
      box-shadow 0.2s ease;
  }

  img,
  svg,
  video,
  canvas,
  iframe {
    transition: none;
  }
}

/* Typography utilities */
@layer utilities {
  .text-header {
    color: var(--header);
  }

  .text-sub-header {
    color: var(--sub-header);
  }

  .text-outline {
    -webkit-text-stroke: 1px white;
    -moz-text-stroke: 1px white;
    text-stroke: 1px white;
  }
}

/* Body layout */
body {
  display: flex;
  flex-direction: column;
  min-height: 100dvh;
  overflow: hidden;
}

/* Radix ScrollArea fix */
[data-radix-scroll-area-viewport] > div {
  min-width: 100% !important;
  display: block !important;
  height: 100% !important;
}

/* Radix Toaster override */
[data-sonner-toast] {
  padding: 27px !important;
  border: 1px solid var(--sonner-border) !important;
  box-shadow: 0px 4px 6px -4px #0000001a !important;
  box-shadow: 0px 10px 15px -3px #0000001a !important;
}

[data-sonner-toast][data-styled='true'] {
  gap: 10px !important;
}

[data-sonner-toast] [data-close-button] {
  top: 14px !important;
  right: -2px !important;
  left: unset !important;
  border: none !important;
}

[data-sonner-toast] [data-close-button]:hover {
  background-color: transparent !important;
}

[data-sonner-toast] [data-title] {
  color: var(--sonner-foreground) !important;
  font-size: 14px !important;
  line-height: 16px !important;
  font-weight: 600 !important;
}

[data-sonner-toast] [data-content] {
  max-width: calc(100% - 20px) !important;
}

/* Radix Tooltip */
[data-radix-popper-content-wrapper] {
  max-width: 90vw; /* responsive */
  width: fit-content;
}

/* Google Maps */
.gm-style .gm-style-iw-tc::after,
.gm-style .gm-style-iw-tc::before {
  display: none !important;
}

.gm-style .gm-style-iw {
  padding: 0 !important;
  padding-bottom: 12px;
  font-family:
    'Inter',
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    'Helvetica Neue',
    Arial,
    sans-serif;
}

.gm-style.disable-map {
  pointer-events: none !important;
}

.pac-container {
  z-index: 9999 !important;
  box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
  pointer-events: auto !important;
}

/* Custom close toast button */
button[aria-label='Close toast'] svg {
  @apply w-5 h-5;
}

/* ---------- SCROLLBAR THEME ---------- */
.scroll-hidden::-webkit-scrollbar {
  display: none;
}

.scroll-hidden {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

* {
  scrollbar-width: thin; /* Firefox */
  scrollbar-color: var(--scrollbar-thumb) transparent;
}

/* Chrome, Edge, Safari */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background-color: var(--scrollbar-thumb);
  border-radius: 9999px;
  border: 2px solid transparent;
  background-clip: content-box;
  transition: background-color 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background-color: var(--scrollbar-thumb-hover);
}

::-webkit-scrollbar-corner {
  background: transparent;
}

/* ---------- LIGHT & DARK THEME VARS ---------- */
:root {
  --scrollbar-thumb: rgba(0, 0, 0, 0.25);
  --scrollbar-thumb-hover: rgba(0, 0, 0, 0.4);
}

.dark {
  --scrollbar-thumb: rgba(255, 255, 255, 0.2);
  --scrollbar-thumb-hover: rgba(255, 255, 255, 0.35);
}

[data-radix-select-viewport] {
  overflow-y: auto !important;
  scrollbar-gutter: stable;
  scrollbar-width: thin !important;
}

/* Hide browser default password reveal icons */
input[type='password']::-webkit-credentials-auto-fill-button {
  display: none !important;
}

input[type='password']::-ms-reveal {
  display: none !important;
}

input[type='password']::-ms-clear {
  display: none !important;
}
