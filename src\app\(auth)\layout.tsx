import type { Metadata } from 'next';
import Image from 'next/image';

export const metadata: Metadata = {
  title: 'Sign In | Scoutr',
  description: 'Sign in to your Scoutr account',
  openGraph: {
    title: 'Sign In | Scoutr',
    description: 'Sign in to your Scoutr account',
  },
};

export default function AuthLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="flex items-center justify-center w-full h-screen">
      <div className="flex-[56%] flex justify-center">{children}</div>
      <div className="flex-[44%] flex justify-center bg-border h-full relative">
        <Image
          src="/login-bg.jpg"
          alt="Auth Background"
          fill
          className="object-cover"
          priority
        />
      </div>
    </div>
  );
}
