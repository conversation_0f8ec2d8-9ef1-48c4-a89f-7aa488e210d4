import * as React from 'react';
import { Slot } from '@radix-ui/react-slot';
import { cva, type VariantProps } from 'class-variance-authority';

import { cn } from '@/lib/utils';

const badgeVariants = cva(
  'inline-flex items-center justify-center rounded-full border px-2.5 py-0.5 text-xs font-semibold leading-4 w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[0.1875rem] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden max-w-full h-5',
  {
    variants: {
      variant: {
        default:
          'border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90',
        secondary:
          'border-primary-50 bg-primary-50 text-[#111827] [a&]:hover:bg-primary-50',
        destructive:
          'border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60',
        outline:
          'text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground',
        success:
          'border-[#e5e7eb] bg-[#bbf7d0] text-[#1f2937] [a&]:hover:bg-[#bbf7d0]',
        warning:
          'border-transparent bg-[#fde68a] text-[#1f2937] [a&]:hover:bg-[#fde68a]',
        danger:
          'border-transparent bg-[#fecaca] text-[#1f2937] [a&]:hover:bg-[#fecaca]',
        neutral:
          'border-[#E2E8F0]/0 bg-[#F1F5F9] text-[#0F172A] font-normal px-2 py-1 h-[23px] [a&]:hover:bg-[#f5f5f5]',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  }
);

function Badge({
  className,
  variant,
  asChild = false,
  ...props
}: React.ComponentProps<'span'> &
  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {
  const Comp = asChild ? Slot : 'span';

  return (
    <Comp
      data-slot="badge"
      className={cn(badgeVariants({ variant }), className)}
      {...props}
    >
      <span
        className="inline-flex items-center justify-center gap-2 max-w-[12.5rem] overflow-hidden whitespace-nowrap"
        {...props}
      >
        {typeof props.children === 'string' ? (
          <span className="truncate">{props.children}</span>
        ) : (
          props.children
        )}
      </span>
    </Comp>
  );
}

export { Badge, badgeVariants };
