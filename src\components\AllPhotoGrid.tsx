'use client';
import { Photo } from '@/types/location';
import Image from 'next/image';
import { Badge } from './ui/badge';
import { Tooltip, TooltipContent, TooltipTrigger } from './ui/tooltip';

interface AllPhotoGridProps {
  images: Photo[];
  onClickImage: (index: Photo) => void;
  imagesSelected?: Photo[];
}

export default function AllPhotoGrid({
  images,
  onClickImage,
  imagesSelected = [],
}: AllPhotoGridProps) {
  const getImageOrder = (id: string) =>
    imagesSelected.findIndex(img => img.id === id) + 1;
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2">
      {images.map((image, index) => (
        <div
          key={index}
          className="relative w-full h-auto aspect-square rounded-xl cursor-pointer"
          onClick={() => onClickImage(image)}
        >
          <Image
            src={image.url}
            alt="location image"
            fill
            className="object-cover rounded-lg"
          />

          {image.id && getImageOrder(image.id) > 0 && (
            <div className="z-50 absolute inset-0 flex items-center justify-center">
              <span className="text-2xl text-[#020617] font-semibold shadow-2xl">
                {getImageOrder(image.id)}
              </span>
            </div>
          )}

          {/* Tags */}
          {image.tags && (
            <div className="absolute bottom-2 left-2 right-2 flex justify-end flex-wrap gap-1">
              {image.tags
                .slice(0, image.tags.length > 4 ? 3 : image.tags.length)
                .map((tag, index) => (
                  <Badge
                    key={index}
                    variant="secondary"
                    className={`max-w-1/3 ${tag.color ? `bg-${tag.color} text-white` : ''}`}
                  >
                    <span className="truncate">{tag.name}</span>
                  </Badge>
                ))}
              {image.tags.length > 4 && (
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="text-right">
                      <Badge variant="secondary">
                        +{image.tags.length - 3} more
                        {image.tags.length - 3 > 1 ? 's' : ''}
                      </Badge>
                    </div>
                  </TooltipTrigger>
                  <TooltipContent
                    className="w-auto flex flex-col gap-2 bg-accent-foreground"
                    side="right"
                  >
                    {image.tags?.slice(3).map(tag => (
                      <Badge
                        key={tag.id}
                        variant="secondary"
                        className={`${tag.color ? `bg-${tag.color} text-white` : ''}`}
                      >
                        {tag.name}
                      </Badge>
                    ))}
                  </TooltipContent>
                </Tooltip>
              )}
            </div>
          )}
        </div>
      ))}
    </div>
  );
}
