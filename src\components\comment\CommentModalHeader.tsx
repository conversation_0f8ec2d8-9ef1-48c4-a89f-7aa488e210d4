import { DialogDescription, DialogHeader, DialogTitle } from '../ui/dialog';
import { Skeleton } from '../ui/skeleton';

interface CommentModalHeaderProps {
  title: string;
  commentCount: number;
  loading: boolean;
}

export const CommentModalHeader = ({
  title = 'Comment',
  commentCount = 0,
  loading = false,
}: CommentModalHeaderProps) => {
  return (
    <DialogHeader>
      <div className="flex items-center gap-3">
        <div className="w-12 h-12 flex items-center justify-center bg-[#A3A3A3] rounded-lg text-xs text-white">
          Location
        </div>
        <div className="">
          <DialogTitle className="text-base font-normal text-header leading-6">
            {title}
          </DialogTitle>
          {loading ? (
            <Skeleton className="w-24 h-5" />
          ) : (
            <DialogDescription className="text-sm font-normal text-[#737373] leading-5">
              {commentCount} {commentCount === 1 ? 'comment' : 'comments'}
            </DialogDescription>
          )}
        </div>
      </div>
    </DialogHeader>
  );
};
