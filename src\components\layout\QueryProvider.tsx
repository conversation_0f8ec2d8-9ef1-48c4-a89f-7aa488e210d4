'use client';
import React from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

export default function QueryProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const [qc] = React.useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            retry: 1,
            staleTime: 1000 * 10, // keep comments stale for 10s
            gcTime: 1000 * 60 * 5, // garbage collect comments after 5min
          },
        },
      })
  );

  return <QueryClientProvider client={qc}>{children}</QueryClientProvider>;
}
