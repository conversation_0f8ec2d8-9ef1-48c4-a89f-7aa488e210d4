'use client';

import * as React from 'react';
import { X, Check } from 'lucide-react';
import { SearchIcon } from '@/lib/icons';
import { cn } from '@/lib/utils';
import { Input } from '@/components/ui/input';
import {
  Popover,
  PopoverContent,
  PopoverAnchor,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from '@/components/ui/command';

export type SearchBoxOption = {
  value: string;
  label: string;
  disabled?: boolean;
  description?: string;
  group?: string;
};

export type SearchBoxGroup = {
  label: string;
  options: SearchBoxOption[];
};

export interface SearchBoxProps {
  /**
   * Options to display in the dropdown (flat array)
   */
  options?: SearchBoxOption[];
  /**
   * Grouped options to display in the dropdown (alternative to options)
   */
  groups?: SearchBoxGroup[];
  /**
   * Current search value
   */
  value?: string;
  /**
   * Callback when search value changes
   */
  onSearchChange?: (value: string) => void;
  /**
   * Callback when an option is selected (single select mode)
   */
  onSelect?: (option: SearchBoxOption) => void;
  /**
   * Enable multi-select mode
   */
  multiSelect?: boolean;
  /**
   * Selected values for multi-select mode (array of option values)
   */
  selectedValues?: string[];
  /**
   * Callback when options are selected/deselected (multi-select mode)
   */
  onMultiSelect?: (selectedOptions: SearchBoxOption[]) => void;
  /**
   * Placeholder text for the input
   */
  placeholder?: string;
  /**
   * Message to show when no results are found
   */
  emptyMessage?: string;
  /**
   * Whether the dropdown is open (controlled)
   */
  open?: boolean;
  /**
   * Callback when open state changes (controlled)
   */
  onOpenChange?: (open: boolean) => void;
  /**
   * Additional className for the root container
   */
  className?: string;
  /**
   * Additional className for the input
   */
  inputClassName?: string;
  /**
   * Additional className for the popover content
   */
  popoverClassName?: string;
  /**
   * Debounce delay in milliseconds for search
   */
  debounceMs?: number;
  /**
   * Whether to show clear button
   */
  showClearButton?: boolean;
  /**
   * Custom trigger element (for popover mode)
   */
  trigger?: React.ReactNode;
}

export default function SearchBox({
  options,
  groups,
  value = '',
  onSearchChange,
  onSelect,
  multiSelect = false,
  selectedValues = [],
  onMultiSelect,
  placeholder = 'Search...',
  emptyMessage = 'No results found',
  open: controlledOpen,
  onOpenChange,
  className,
  inputClassName,
  popoverClassName,
  debounceMs = 300,
  showClearButton = true,
  trigger,
}: SearchBoxProps) {
  // Get grouped structure for rendering
  const groupedOptions = React.useMemo(() => {
    if (groups) {
      return groups.map(group => ({
        label: group.label,
        options: group.options,
      }));
    }
    if (options) {
      // Group by group property if present
      const grouped = options.reduce(
        (acc, option) => {
          const groupLabel = option.group || 'Default';
          if (!acc[groupLabel]) {
            acc[groupLabel] = [];
          }
          acc[groupLabel].push(option);
          return acc;
        },
        {} as Record<string, SearchBoxOption[]>
      );
      return Object.entries(grouped).map(([label, opts]) => ({
        label,
        options: opts,
      }));
    }
    return [];
  }, [options, groups]);
  const [internalOpen, setInternalOpen] = React.useState(false);
  const [searchValue, setSearchValue] = React.useState(value);
  const inputRef = React.useRef<HTMLInputElement>(null);
  const blurTimeoutRef = React.useRef<NodeJS.Timeout | null>(null);

  const isControlled = controlledOpen !== undefined;
  const open = isControlled ? controlledOpen : internalOpen;
  const setOpen = isControlled
    ? (onOpenChange ?? setInternalOpen)
    : setInternalOpen;

  // Sync internal value with external value prop
  React.useEffect(() => {
    setSearchValue(value);
  }, [value]);

  // Cleanup timeout on unmount
  React.useEffect(() => {
    return () => {
      if (blurTimeoutRef.current) {
        clearTimeout(blurTimeoutRef.current);
      }
    };
  }, []);

  const handleSearchChange = (newValue: string) => {
    setSearchValue(newValue);
    onSearchChange?.(newValue);
    // Clear any pending blur timeout
    if (blurTimeoutRef.current) {
      clearTimeout(blurTimeoutRef.current);
      blurTimeoutRef.current = null;
    }
  };

  const handleSelect = (option: SearchBoxOption) => {
    if (multiSelect) {
      // Multi-select mode: toggle selection
      const isSelected = selectedValues.includes(option.value);
      const newSelectedValues = isSelected
        ? selectedValues.filter(v => v !== option.value)
        : [...selectedValues, option.value];

      // Find selected options
      const allOptions = groupedOptions.flatMap(g => g.options);
      const newSelectedOptions = allOptions.filter(opt =>
        newSelectedValues.includes(opt.value)
      );

      onMultiSelect?.(newSelectedOptions);
      // Don't close popover or update input in multi-select mode
      return;
    }

    // Single select mode
    setSearchValue(option.label);
    onSelect?.(option);
    // Clear blur timeout
    if (blurTimeoutRef.current) {
      clearTimeout(blurTimeoutRef.current);
      blurTimeoutRef.current = null;
    }
    setOpen(false);
    inputRef.current?.blur();
  };

  const handleClear = () => {
    setSearchValue('');
    onSearchChange?.('');
    inputRef.current?.focus();
  };

  const filteredGroupedOptions = React.useMemo(() => {
    if (!searchValue.trim()) {
      return groupedOptions;
    }
    const searchLower = searchValue.toLowerCase();
    return groupedOptions
      .map(group => ({
        label: group.label,
        options: group.options.filter(
          option =>
            option.label.toLowerCase().includes(searchLower) ||
            option.value.toLowerCase().includes(searchLower) ||
            option.description?.toLowerCase().includes(searchLower)
        ),
      }))
      .filter(group => group.options.length > 0);
  }, [groupedOptions, searchValue]);

  const filteredOptions = React.useMemo(() => {
    return filteredGroupedOptions.flatMap(group => group.options);
  }, [filteredGroupedOptions]);

  // Open popover when filtered options change and input is focused
  React.useEffect(() => {
    if (
      filteredOptions.length > 0 &&
      document.activeElement === inputRef.current
    ) {
      setOpen(true);
    }
  }, [filteredOptions.length, setOpen]);

  const selectedValuesSet = React.useMemo(
    () => new Set(selectedValues),
    [selectedValues]
  );

  // If trigger is provided, use popover trigger mode (like TagSelector)
  if (trigger) {
    return (
      <Popover open={open} onOpenChange={setOpen} modal={false}>
        {trigger && <PopoverTrigger asChild>{trigger}</PopoverTrigger>}
        <PopoverContent
          className={cn('w-[329px] p-0 py-1 rounded-lg', popoverClassName)}
          align="end"
          onOpenAutoFocus={e => e.preventDefault()}
        >
          <Command className="rounded-lg">
            <CommandInput
              placeholder={placeholder}
              value={searchValue}
              onValueChange={handleSearchChange}
            />
            <CommandList>
              <CommandEmpty className="py-6 text-center text-sm text-muted-foreground">
                {emptyMessage}
              </CommandEmpty>
              {filteredGroupedOptions.map((group, groupIndex) => (
                <React.Fragment key={group.label}>
                  <CommandGroup className="p-0">
                    {group.label !== 'Default' && (
                      <div className="px-2 py-1.5">
                        <span className="text-xs font-medium text-muted-foreground leading-5">
                          {group.label}
                        </span>
                      </div>
                    )}
                    {group.options.map(option => {
                      const isSelected = selectedValuesSet.has(option.value);
                      return (
                        <CommandItem
                          key={option.value}
                          disabled={option.disabled}
                          value={`${option.label} ${option.value} ${option.description || ''}`}
                          onSelect={() => handleSelect(option)}
                          className={cn(
                            'cursor-pointer rounded px-2 py-1.5 text-sm leading-5 mx-1 data-[selected=true]:bg-primary-50 data-[selected=true]:text-accent-foreground',
                            isSelected && 'bg-primary-50'
                          )}
                        >
                          {multiSelect && (
                            <div
                              className={cn(
                                'mr-2 flex h-[10.67px] w-[10.67px] items-center justify-center',
                                isSelected ? 'opacity-100' : 'opacity-0'
                              )}
                            >
                              <Check className="h-[10.67px] w-[10.67px] text-slate-900" />
                            </div>
                          )}
                          <span
                            className={cn('text-sm font-normal text-slate-900')}
                          >
                            {option.label}
                          </span>
                        </CommandItem>
                      );
                    })}
                  </CommandGroup>
                  {groupIndex < filteredGroupedOptions.length - 1 && (
                    <CommandSeparator className="my-1" />
                  )}
                </React.Fragment>
              ))}
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    );
  }

  return (
    <div className={cn('relative w-full', className)}>
      <Popover open={open} onOpenChange={setOpen} modal={false}>
        <PopoverAnchor asChild>
          <div
            className={cn(
              'relative w-full border border-slate-200 overflow-hidden transition-all',
              open ? 'rounded-t-lg rounded-b-none' : 'rounded-lg'
            )}
          >
            <div
              className={cn('bg-popover flex items-center gap-2 px-2 py-2.5')}
            >
              <SearchIcon className="h-4 w-4 shrink-0 pointer-events-none text-slate-950" />
              <Input
                ref={inputRef}
                type="text"
                placeholder={placeholder}
                value={searchValue}
                onChange={e => handleSearchChange(e.target.value)}
                onFocus={() => {
                  // Clear any pending blur timeout
                  if (blurTimeoutRef.current) {
                    clearTimeout(blurTimeoutRef.current);
                    blurTimeoutRef.current = null;
                  }
                  if (filteredOptions.length > 0) {
                    setOpen(true);
                  }
                }}
                onBlur={e => {
                  // Don't close if clicking inside the popover
                  const relatedTarget = e.relatedTarget as HTMLElement;
                  if (relatedTarget?.closest('[data-slot="popover-content"]')) {
                    return;
                  }
                  // Small delay to allow click events on items to fire first
                  blurTimeoutRef.current = setTimeout(() => {
                    setOpen(false);
                    blurTimeoutRef.current = null;
                  }, 200);
                }}
                className={cn(
                  'border-0 bg-transparent px-0 py-0 h-auto text-sm placeholder:text-muted-foreground placeholder:opacity-50 focus-visible:ring-0 focus-visible:ring-offset-0 shadow-none',
                  inputClassName
                )}
                debounceMs={debounceMs}
              />
              {showClearButton && searchValue && (
                <button
                  type="button"
                  onClick={handleClear}
                  className="text-muted-foreground transition-colors z-10 shrink-0"
                  aria-label="Clear search"
                >
                  <X className="h-4 w-4" />
                </button>
              )}
            </div>
          </div>
        </PopoverAnchor>
        <PopoverContent
          className={cn(
            'p-1 w-[var(--radix-popover-trigger-width)] max-w-none border border-slate-200 border-t-0 rounded-t-none rounded-b-lg',
            popoverClassName
          )}
          align="start"
          sideOffset={0}
          onOpenAutoFocus={e => e.preventDefault()}
          onInteractOutside={e => {
            // Prevent closing when interacting with the input or its container
            const target = e.target as HTMLElement;
            const anchorElement = inputRef.current?.closest(
              '[data-slot="popover-anchor"]'
            );
            if (
              target === inputRef.current ||
              target.closest('[data-slot="input"]') ||
              (anchorElement && anchorElement.contains(target))
            ) {
              e.preventDefault();
            }
          }}
          onEscapeKeyDown={() => {
            setOpen(false);
            inputRef.current?.blur();
          }}
        >
          <Command loop>
            <CommandList className="max-h-[300px]">
              <CommandEmpty className="py-6 text-center text-sm text-muted-foreground">
                {emptyMessage}
              </CommandEmpty>
              {filteredGroupedOptions.map((group, groupIndex) => (
                <React.Fragment key={group.label}>
                  <CommandGroup className="p-0">
                    {group.label !== 'Default' && (
                      <div className="px-2 py-1.5">
                        <span className="text-xs font-medium text-muted-foreground leading-5">
                          {group.label}
                        </span>
                      </div>
                    )}
                    {group.options.map(option => {
                      const isSelected =
                        multiSelect && selectedValuesSet.has(option.value);
                      return (
                        <CommandItem
                          key={option.value}
                          disabled={option.disabled}
                          value={`${option.label} ${option.value} ${option.description || ''}`}
                          onSelect={() => handleSelect(option)}
                          className={cn(
                            'cursor-pointer px-2 py-1.5 rounded-[4px] text-sm leading-5 text-popover-foreground',
                            multiSelect &&
                              isSelected &&
                              'bg-primary-50 data-[selected=true]:bg-primary-50',
                            multiSelect &&
                              !isSelected &&
                              'data-[selected=true]:bg-accent data-[selected=true]:text-accent-foreground',
                            !multiSelect &&
                              'data-[selected=true]:bg-accent data-[selected=true]:text-accent-foreground'
                          )}
                        >
                          {multiSelect && (
                            <div
                              className={cn(
                                'mr-2 flex h-[10.67px] w-[10.67px] items-center justify-center',
                                isSelected ? 'opacity-100' : 'opacity-0'
                              )}
                            >
                              <Check className="h-[10.67px] w-[10.67px] text-slate-900" />
                            </div>
                          )}
                          <div className="flex flex-col gap-0.5">
                            <span className="truncate">{option.label}</span>
                            {option.description && (
                              <span className="text-xs text-muted-foreground truncate">
                                {option.description}
                              </span>
                            )}
                          </div>
                        </CommandItem>
                      );
                    })}
                  </CommandGroup>
                  {groupIndex < filteredGroupedOptions.length - 1 && (
                    <div className="h-px bg-secondary my-0" />
                  )}
                </React.Fragment>
              ))}
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
}
