'use client';

import { useReference } from '../reference-context';
import ReferenceListPreview from '@/components/project/ReferenceListPreview';

export default function ScoutReferenceListPreview() {
  const { reference } = useReference();

  if (!reference) {
    return <div>Loading...</div>;
  }

  return (
    <div className="space-y-11 w-full xl:w-[70.5rem] p-11 xl:px-0 mx-auto">
      <ReferenceListPreview reference={reference} isViewer={false} />
    </div>
  );
}
