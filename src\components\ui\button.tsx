import * as React from 'react';
import { Slot } from '@radix-ui/react-slot';
import { cva, type VariantProps } from 'class-variance-authority';

import { cn } from '@/lib/utils';

const buttonVariants = cva(
  "cursor-pointer inline-flex items-center justify-center gap-2.5 whitespace-nowrap rounded-md text-sm font-medium leading-5 transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[0.1875rem] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",
  {
    variants: {
      variant: {
        default: 'bg-primary text-primary-foreground hover:bg-primary-300',
        destructive:
          'bg-destructive text-white hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60',
        outline:
          'border border-primary-300 text-primary bg-background hover:bg-primary-50 hover:border-primary-50 dark:bg-input/30 dark:border-input dark:hover:bg-input/50',
        secondary:
          'bg-[#E5E7EB]/80 text-neutral-900 hover:bg-[#E5E7EB] hover:text-primary',
        ghost:
          'hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50',
        link: 'text-primary-300 hover:text-primary hover:underline',
      },
      size: {
        default: 'h-9 px-4 py-2 has-[>svg]:px-3',
        sm: 'h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5',
        lg: 'h-10 rounded-md px-6 has-[>svg]:px-4',
        icon: 'size-9',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
);

function Button({
  className,
  variant,
  size,
  asChild = false,
  isLoading = false,
  children,
  disabled,
  ...props
}: React.ComponentProps<'button'> &
  VariantProps<typeof buttonVariants> & {
    asChild?: boolean;
    isLoading?: boolean;
  }) {
  const Comp = asChild ? Slot : 'button';

  const getSpinnerColor = () => {
    switch (variant) {
      case 'outline':
      case 'secondary':
      case 'ghost':
        return 'text-primary';
      case 'link':
        return 'text-primary-300';
      default:
        return 'text-white';
    }
  };

  return (
    <Comp
      data-slot="button"
      className={cn(
        buttonVariants({ variant, size, className }),
        isLoading && 'opacity-50 gap-2'
      )}
      disabled={disabled || isLoading}
      {...props}
    >
      {isLoading && (
        <svg
          className={cn('w-4 h-4 shrink-0', getSpinnerColor())}
          style={{
            animation: 'spin 1s linear infinite',
            transformOrigin: 'center',
          }}
          width="16"
          height="16"
          viewBox="0 0 16 16"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <mask id="path-1-inside-1_3200_56895" fill="white">
            <path d="M15.1263 11.6354C15.9536 10.0136 16.203 8.15804 15.8333 6.37533C15.4635 4.59261 14.4968 2.98934 13.0927 1.83034C11.6886 0.671338 9.93112 0.0259453 8.11064 0.00076514C6.29016 -0.024415 4.51554 0.572124 3.07992 1.69185C1.6443 2.81157 0.633553 4.38749 0.214642 6.1593C-0.20427 7.9311 -0.00628785 9.7928 0.775885 11.4369C1.55806 13.081 2.87763 14.4091 4.51663 15.2018C6.15564 15.9946 8.01602 16.2045 9.79049 15.7971L9.5142 14.5939C8.01355 14.9385 6.44025 14.7609 5.05417 14.0905C3.66808 13.4201 2.55214 12.2969 1.89067 10.9065C1.22919 9.51615 1.06176 7.94174 1.41603 6.44334C1.7703 4.94495 2.62507 3.61222 3.83916 2.66528C5.05324 1.71835 6.55401 1.21386 8.09357 1.23516C9.63313 1.25645 11.1194 1.80225 12.3068 2.7824C13.4942 3.76255 14.3118 5.11842 14.6245 6.62604C14.9372 8.13365 14.7263 9.70284 14.0266 11.0744L15.1263 11.6354Z" />
          </mask>
          <path
            d="M15.1263 11.6354C15.9536 10.0136 16.203 8.15804 15.8333 6.37533C15.4635 4.59261 14.4968 2.98934 13.0927 1.83034C11.6886 0.671338 9.93112 0.0259453 8.11064 0.00076514C6.29016 -0.024415 4.51554 0.572124 3.07992 1.69185C1.6443 2.81157 0.633553 4.38749 0.214642 6.1593C-0.20427 7.9311 -0.00628785 9.7928 0.775885 11.4369C1.55806 13.081 2.87763 14.4091 4.51663 15.2018C6.15564 15.9946 8.01602 16.2045 9.79049 15.7971L9.5142 14.5939C8.01355 14.9385 6.44025 14.7609 5.05417 14.0905C3.66808 13.4201 2.55214 12.2969 1.89067 10.9065C1.22919 9.51615 1.06176 7.94174 1.41603 6.44334C1.7703 4.94495 2.62507 3.61222 3.83916 2.66528C5.05324 1.71835 6.55401 1.21386 8.09357 1.23516C9.63313 1.25645 11.1194 1.80225 12.3068 2.7824C13.4942 3.76255 14.3118 5.11842 14.6245 6.62604C14.9372 8.13365 14.7263 9.70284 14.0266 11.0744L15.1263 11.6354Z"
            stroke="currentColor"
            strokeWidth="1"
            mask="url(#path-1-inside-1_3200_56895)"
          />
        </svg>
      )}
      {children}
    </Comp>
  );
}

export { Button, buttonVariants };
