'use client';

import * as React from 'react';
import {
  Di<PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import SimpleMultiSelect from './Select/SimpleMultiSelect';
import { TagOption } from '@/types/tag';
import { FormEvent, useCallback, useEffect, useState } from 'react';
import { useTagService } from '@/hooks/use-services';
import { toastError } from '@/lib/toast';
interface EditImageTagsModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSave: (tags: TagOption[]) => void;
  tags?: TagOption[];
}

export default function EditImageTagsModal({
  open,
  onOpenChange,
  onSave,
  tags: originTags,
}: EditImageTagsModalProps) {
  const tagService = useTagService();
  const [tagIds, setTagIds] = useState<string[]>([]);
  const [tagOption, setTagOption] = useState<TagOption[]>([]);

  const handleSubmit = (e: FormEvent) => {
    e.preventDefault();
    if (!tagIds.length) return;
    const result = tagIds.map(tagId => {
      const tag = tagOption.find(tag => tag.value === tagId);
      return tag
        ? { value: tag.value, label: tag.label, color: tag.color }
        : ({} as TagOption);
    });
    onSave(result);
    onOpenChange(false);
  };

  useEffect(() => {
    if (!open) {
      setTagIds([]);
      return;
    }
    setTagIds(originTags?.map(tag => tag.value) ?? []);
  }, [originTags, open]);

  const fetchStyleTags = useCallback(async () => {
    try {
      const styles = await tagService.getAllTags({ type: 'style' });
      setTagOption(
        styles.map(style => ({
          value: style.id,
          label: style.name,
          color: style.color,
        }))
      );
    } catch {
      toastError('Failed to get tag styles');
    }
  }, [tagService]);

  useEffect(() => {
    fetchStyleTags();
  }, [fetchStyleTags]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className="sm:max-w-[20.5625rem]"
        onInteractOutside={e => e.preventDefault()}
        showCloseButton={false}
      >
        <DialogHeader>
          <DialogTitle>Add/Edit tags</DialogTitle>
          <DialogDescription className="text-sm">
            Add tags to better describe your photo.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label
              htmlFor="type"
              className="text-sm font-medium text-foreground"
            >
              Tags
            </Label>
            <SimpleMultiSelect
              options={tagOption}
              value={tagIds}
              onChange={setTagIds}
              placeholder="Select tags"
              groupLabel="Tags"
              className="w-[17.4375rem]"
            />
          </div>

          <DialogFooter className="mt-6">
            <Button
              type="button"
              variant="ghost"
              onClick={() => onOpenChange(false)}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={tagIds.length === 0}>
              Select tags
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
