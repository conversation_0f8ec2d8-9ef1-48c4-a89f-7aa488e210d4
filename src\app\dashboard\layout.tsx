'use client';

import { useAuth } from '@/contexts/auth-context';
import { Role } from '@/types';
import { AdminLayoutWithSidebar } from '@/components/layout/admin-layout-with-sidebar';

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { user } = useAuth();

  // Only show sidebar for SuperAdmin
  if (user?.role === Role.SuperAdmin) {
    return <AdminLayoutWithSidebar>{children}</AdminLayoutWithSidebar>;
  }

  // For other roles, render without sidebar
  return <>{children}</>;
}
