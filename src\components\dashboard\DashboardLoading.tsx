'use client';

import Image from 'next/image';
import { Loader2 } from 'lucide-react';

/**
 * Dashboard loading component
 * Shows a beautiful full-page spinner with logo for all roles
 * Used in both loading.tsx and page.tsx when user is not yet loaded
 */
export function DashboardLoading() {
  return (
    <div className="flex items-center justify-center min-h-[calc(100vh-var(--navbar-height))] w-full">
      <div className="flex flex-col items-center justify-center gap-6">
        {/* Logo with fade-in animation */}
        <div className="relative w-[124px] h-[40px] animate-pulse">
          <Image
            src="/assets/logo.svg"
            alt="Scoutr Logo"
            fill
            priority
            className="object-contain"
          />
        </div>

        {/* Spinner with smooth animation */}
        <div className="relative">
          <Loader2 className="h-8 w-8 animate-spin text-primary-500" />
          {/* Outer ring animation */}
          <div
            className="absolute inset-0 border-4 border-primary-100 border-t-primary-500 rounded-full animate-spin"
            style={{ animationDuration: '1.5s' }}
          />
        </div>

        {/* Loading text with fade animation */}
        <p className="text-sm text-muted-foreground animate-pulse">
          Loading...
        </p>
      </div>
    </div>
  );
}
