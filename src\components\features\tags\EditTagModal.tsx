'use client';

import * as React from 'react';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Tag } from '@/types/tag';
import { colorOptions } from './AddTagModal';
import { Loader2 } from 'lucide-react';
import { updateTagSchema, UpdateTagFormData } from '@/lib/validations';
import { PROPERTY_CATEGORY } from '@/types/constant';
import { PropertyType } from '@/types/enum';
import { formatCategoryLabel } from './TagsForm';
import { useUpdateTag } from '@/hooks/api/useTags';

interface EditTagModalProps {
  tag: Tag | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export default function EditTagModal({
  tag,
  open,
  onOpenChange,
}: EditTagModalProps) {
  const form = useForm<UpdateTagFormData>({
    resolver: zodResolver(updateTagSchema),
    defaultValues: {
      name: '',
      type: undefined,
      color: '',
    },
  });
  // Mutations
  const updateTagMutation = useUpdateTag();
  const tagTypeOptions =
    tag?.propertyTypes?.[0] &&
    PROPERTY_CATEGORY[tag.propertyTypes?.[0] as PropertyType]
      ? PROPERTY_CATEGORY[tag.propertyTypes?.[0] as PropertyType]
      : [];

  // Update form when tag changes
  useEffect(() => {
    if (tag) {
      form.reset({
        name: tag.name,
        type: tag.type as UpdateTagFormData['type'],
        color: tag.color || '',
      });
    }
  }, [tag, form]);

  // Reset form when modal closes
  useEffect(() => {
    if (!open) {
      form.reset();
    }
  }, [open, form]);

  const handleSubmit = async (data: UpdateTagFormData) => {
    if (!tag) return;
    await updateTagMutation.mutateAsync({
      id: tag.id,
      data,
    });
    onOpenChange(false);
  };

  const handleOpenChange = (open: boolean) => {
    if (!open) {
      form.reset();
    }
    onOpenChange(open);
  };

  if (!tag) return null;

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-[20.5625rem]">
        <DialogHeader>
          <DialogTitle>Edit Tag</DialogTitle>
          <DialogDescription>
            Edit the tag&apos;s details below.
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className="space-y-4"
          >
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Tag name</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter tag name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="type"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Tag type</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl className="">
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="Select tag's type" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {tagTypeOptions.map(category => (
                        <SelectItem
                          className="capitalize"
                          key={category}
                          value={category}
                        >
                          {formatCategoryLabel(category)}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="color"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Color (optional)</FormLabel>
                  <div className="relative">
                    {field.value && (
                      <div
                        className={`rounded-full border-2 border-${field.value} w-3 h-3 bg-transparent absolute translate-y-1/2 top-2 right-10`}
                      ></div>
                    )}
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger className="w-full">
                          <SelectValue placeholder="Pick a color for this tag" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {colorOptions.map(color => (
                          <SelectItem
                            className="capitalize"
                            key={color.value}
                            value={color.value}
                          >
                            {color.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter className="mt-6">
              <Button
                type="button"
                variant="outline"
                onClick={() => handleOpenChange(false)}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={
                  updateTagMutation.isPending || !form.formState.isValid
                }
              >
                Save Changes
                {updateTagMutation.isPending && (
                  <Loader2 className="ml-2 size-4 animate-spin" />
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
