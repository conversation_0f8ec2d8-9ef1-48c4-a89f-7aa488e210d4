'use client';

import { useEffect, useRef, useState } from 'react';
import { useMapsLibrary } from '@vis.gl/react-google-maps';
import { Input } from '@/components/ui/input';

interface LocationAutocompleteProps {
  onSelect: (place: google.maps.places.PlaceResult | null) => void;
  placeholder?: string;
  value?: string;
  onValueChange?: (next: string) => void;
  fullAddressMode?: boolean;
}

function AutocompleteInput({
  onSelect,
  placeholder,
  value: valueProp,
  fullAddressMode = true,
}: LocationAutocompleteProps) {
  const inputRef = useRef<HTMLInputElement>(null);
  const placesLib = useMapsLibrary('places');
  const autocompleteRef = useRef<google.maps.places.Autocomplete | null>(null);
  const [value, setValue] = useState<string>(valueProp ?? '');

  const handleSetValue = (text: string) => {
    setValue(text);
    if (!text) {
      onSelect(null);
    }
  };

  useEffect(() => {
    if (!placesLib || !inputRef.current) return;
    inputRef.current.setAttribute('autocomplete', 'off');

    const autocomplete = new placesLib.Autocomplete(inputRef.current, {
      types: fullAddressMode ? ['geocode'] : ['(cities)'],
      fields: [
        'address_components',
        'formatted_address',
        'geometry',
        'name',
        'place_id',
      ],
    });
    autocompleteRef.current = autocomplete;

    const handler = () => {
      const place = autocomplete.getPlace();
      if (!place) return;

      let displayName = place.formatted_address;

      if (!fullAddressMode) {
        const components = place.address_components || [];

        // Only pick city/region/country parts — skip streets, routes, postal codes
        const city = components.find(c =>
          ['locality', 'postal_town'].some(t => c.types.includes(t))
        )?.long_name;
        const region = components.find(c =>
          c.types.includes('administrative_area_level_1')
        )?.long_name;
        const country = components.find(c =>
          c.types.includes('country')
        )?.long_name;

        // Build a clean human-readable string
        displayName =
          [city, region, country].filter(Boolean).join(', ') || place.name;
      }

      setValue(displayName || '');
      onSelect({ ...place, formatted_address: displayName });
    };
    autocomplete.addListener('place_changed', handler);

    // Mutation observer to allow interaction with pac-container
    const observer = new MutationObserver(() => {
      const pacContainer =
        document.querySelector<HTMLElement>('.pac-container');
      if (!pacContainer) return;

      // Ensure it stays on top of the modal
      pacContainer.style.zIndex = '99999';
      pacContainer.style.pointerEvents = 'auto';

      // Also disable pointer-events blocking from Radix overlay
      const overlay = document.querySelector<HTMLElement>(
        '[data-radix-dialog-overlay]'
      );
      if (overlay) overlay.style.pointerEvents = 'none';
    });

    observer.observe(document.body, { childList: true, subtree: true });

    return () => {
      google.maps.event.clearInstanceListeners(autocomplete);
      observer.disconnect();
    };
  }, [placesLib, onSelect, setValue, fullAddressMode]);

  useEffect(() => {
    const observer = new MutationObserver(() => {
      const pacVisible = document.querySelectorAll(
        '.pac-container:has(.pac-item)'
      );
      const gm = document.querySelector<HTMLElement>('.gm-style');
      if (gm) {
        const hasVisiblePac = Array.from(pacVisible).some(
          element => element.clientHeight > 0
        );

        if (hasVisiblePac) {
          gm.classList.add('disable-map');
        } else {
          gm.classList.remove('disable-map');
        }
      }
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true,
    });

    return () => {
      observer.disconnect();
    };
  }, []);

  useEffect(() => {
    setValue(valueProp ?? '');
  }, [valueProp]);

  return (
    <Input
      ref={inputRef}
      onChange={e => handleSetValue(e.target.value)}
      value={value}
      placeholder={placeholder || 'Enter the full address'}
      className="w-full pr-10 h-12.5"
    />
  );
}

export default function LocationAutocomplete({
  onSelect,
  value,
  onValueChange,
  placeholder,
  fullAddressMode = true,
}: {
  onSelect: (place: google.maps.places.PlaceResult | null) => void;
  value?: string;
  onValueChange?: (next: string) => void;
  placeholder?: string;
  fullAddressMode?: boolean;
}) {
  return (
    <AutocompleteInput
      onSelect={onSelect}
      value={value}
      onValueChange={onValueChange}
      placeholder={placeholder}
      fullAddressMode={fullAddressMode}
    />
  );
}
