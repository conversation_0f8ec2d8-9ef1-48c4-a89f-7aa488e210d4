import { FileErrorCode, FileStatusCode } from './enum';
import { Tag } from './tag';

export interface FileError {
  code: FileErrorCode;
  status: FileStatusCode;
  message: string;
}

export interface PresignedUrlRequest {
  fileName: string;
  fileType: string;
  fileSize?: number;
  metadata?: Record<string, string>;
}

export interface PresignedUrlResponse {
  url: string;
  fields: object;
  key: string;
}

export interface UploadResult {
  success: boolean;
  data?: unknown;
  error?: string;
}

export interface UploadProgress {
  loaded: number;
  total: number;
  percentage: number;
}

export type UploadProgressCallback = (progress: UploadProgress) => void;

export interface FileCompleteRequest {
  key: string;
}

export interface FileCompleteResponse {
  url: string;
  key: string;
}

export interface FileUpload {
  uuid: string;
  id?: string;
  url?: string;
  imageSrc?: string;
  key?: string;
  order?: number;
  tags?: Tag[];
  file?: File;
  isChecked?: boolean;
  isUploading?: boolean;
  isUploaded?: boolean;
  isUploadError?: boolean;
  isUploadSuccess?: boolean;
}
