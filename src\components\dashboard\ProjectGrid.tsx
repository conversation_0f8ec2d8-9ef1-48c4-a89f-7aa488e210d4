import InfiniteScroll from 'react-infinite-scroll-component';
import { Reference } from '@/types';
import ProjectCard from '@/components/project/ProjectCard';
import { ProjectSkeletonGrid } from './ProjectSkeleton';

interface ProjectGridProps {
  projects: Reference[];
  isLoading: boolean;
  isLoadingMore: boolean;
  hasMore: boolean;
  onLoadMore: () => void;
}

export function ProjectGrid({
  projects,
  isLoading,
  isLoadingMore,
  hasMore,
  onLoadMore,
}: ProjectGridProps) {
  if (isLoading && !isLoadingMore) {
    return <ProjectSkeletonGrid count={3} />;
  }

  return (
    <div id="scrollableDiv" className="h-[72dvh] overflow-auto">
      <InfiniteScroll
        scrollableTarget="scrollableDiv"
        dataLength={projects.length}
        next={onLoadMore}
        hasMore={hasMore}
        loader={
          isLoadingMore ? (
            <div className="text-center py-4">Loading...</div>
          ) : null
        }
      >
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {projects.map((project, index) => (
            <ProjectCard key={project.id || index} project={project} />
          ))}
        </div>
      </InfiniteScroll>
    </div>
  );
}
