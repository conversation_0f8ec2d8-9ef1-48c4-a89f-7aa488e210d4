'use client';
import { Location } from '@/types';
import { Button } from '@/components/ui/button';
import { CommentIcon, DeleteIcon, EditIcon, PencilIcon } from '@/lib/icons';
import { Badge } from '@/components/ui/badge';
import { EyeIcon, Plus } from 'lucide-react';
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from '@/components/ui/carousel';
import Image from 'next/image';
import { Heading } from '@/components/ui/typography';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { Skeleton } from '@/components/ui/skeleton';
import { useState } from 'react';
import { IconButton } from '@/components/shared';

interface LocationCardProps {
  location: Location;
  onDelete?: (data: Location) => void;
  onAddLocation?: (data: Location) => void;
  onViewDetail?: (data: Location) => void;
  onEdit?: (data: Location) => void;
  onAddComment?: (data: Location) => void;
  hoverAction?: boolean;
  showQuickAdd?: boolean;
  statusBadgeText?: string[];
  showEditDeleteInTitle?: boolean;
  viewButtonText?: string;
  showAddCommentButton?: boolean;
}

export default function LocationCard({
  location,
  onDelete,
  onAddLocation,
  onViewDetail,
  onEdit,
  onAddComment,
  hoverAction = true,
  showQuickAdd = true,
  statusBadgeText = [],
  showEditDeleteInTitle = false,
  viewButtonText = 'View details',
  showAddCommentButton = false,
}: LocationCardProps) {
  const [loading, setLoading] = useState(false);

  const handleAddLocation = async () => {
    if (loading) return;
    setLoading(true);
    try {
      await onAddLocation?.(location);
    } finally {
      setLoading(false);
    }
    setLoading(false);
  };

  return (
    <div
      className={`relative border rounded-lg shadow-sm bg-white flex flex-col group`}
    >
      <div className="relative bg-gray-200 flex items-center justify-center rounded-t-lg">
        {statusBadgeText.length > 0 && (
          <div className="absolute top-1 right-2 z-10">
            {statusBadgeText.map((text, index) => (
              <Badge key={index} variant="secondary" className="capitalize">
                {text}
              </Badge>
            ))}
          </div>
        )}
        <Carousel className="relative w-full h-full">
          <CarouselContent>
            {location.images.map((image, index) => (
              <CarouselItem key={index}>
                <div className="relative w-full h-[12.5rem]">
                  <Image
                    src={image.url}
                    alt="Location"
                    fill
                    className="w-full h-full object-cover rounded-t-lg"
                  />
                </div>
              </CarouselItem>
            ))}
            {location.images?.length === 0 && (
              <div className="relative w-full h-[12.5rem] bg-[#E5E7EB] rounded-t-lg"></div>
            )}
          </CarouselContent>
          <CarouselPrevious className="absolute top-1/2 left-2 -translate-y-1/2 z-10 w-8.5 h-10 disabled:opacity-0 opacity-0 transition-opacity group-hover:opacity-100 group-hover:disabled:opacity-50 bg-white shadow-[0_0.625rem_0.9375rem_0_rgba(0,0,0,0.1),0_0.25rem_0.375rem_0_rgba(0,0,0,0.1)]" />
          <CarouselNext className="absolute top-1/2 right-2 -translate-y-1/2 z-10 w-8.5 h-10 disabled:opacity-0 opacity-0 transition-opacity group-hover:opacity-100 group-hover:disabled:opacity-50 bg-white shadow-[0_0.625rem_0.9375rem_0_rgba(0,0,0,0.1),0_0.25rem_0.375rem_0_rgba(0,0,0,0.1)]" />
          {showEditDeleteInTitle && (
            <PencilIcon
              onClick={() => onEdit?.(location)}
              className="w-5 cursor-pointer absolute top-1/2 right-1/2 -translate-y-1/2 z-10 disabled:opacity-0 opacity-0 transition-opacity group-hover:opacity-100 group-hover:disabled:opacity-50"
            />
          )}
        </Carousel>
      </div>
      <div className="px-4 pt-4 pb-3 flex-1 flex flex-col gap-1">
        <div className="flex justify-between items-center">
          <Heading level={6} className="mb-1 line-clamp-2">
            {location.title}
          </Heading>
          {showEditDeleteInTitle && (
            <div
              className={`flex items-center transition-opacity duration-200 ${
                hoverAction
                  ? 'opacity-0 group-hover:opacity-100 pointer-events-none group-hover:pointer-events-auto'
                  : 'opacity-100'
              }`}
            >
              <IconButton
                size="small"
                color="primary"
                onClick={e => {
                  e.stopPropagation();
                  onEdit?.(location);
                }}
              >
                <EditIcon />
              </IconButton>
              <IconButton
                size="small"
                color="primary"
                onClick={e => {
                  e.stopPropagation();
                  onDelete?.(location);
                }}
              >
                <DeleteIcon />
              </IconButton>
            </div>
          )}
        </div>
        <p className="text-sm text-gray-600 line-clamp-2">{location.address}</p>
        {location.tags && (
          <div className="flex items-center gap-2 flex-wrap mt-2 mb-4">
            {location.tags
              .slice(0, location.tags.length > 4 ? 3 : location.tags.length)
              .map((tag, index) => (
                <Badge
                  key={index}
                  variant="secondary"
                  className={`max-w-1/2 ${tag.color ? `bg-${tag.color} text-white` : ''}`}
                >
                  <span className="truncate">{tag.name}</span>
                </Badge>
              ))}
            {location.tags.length > 4 && (
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="text-right">
                    <Badge variant="secondary">
                      +{location.tags.length - 3} more
                      {location.tags.length - 3 > 1 ? 's' : ''}
                    </Badge>
                  </div>
                </TooltipTrigger>
                <TooltipContent
                  className="w-auto flex flex-col gap-2 bg-accent-foreground max-h-80 overflow-y-auto dark"
                  side="right"
                >
                  <div className="flex flex-col gap-2">
                    {location.tags?.slice(3).map(tag => (
                      <Badge
                        key={tag.id}
                        variant="secondary"
                        className={`${tag.color ? `bg-${tag.color} text-white` : ''}`}
                      >
                        {tag.name}
                      </Badge>
                    ))}
                  </div>
                </TooltipContent>
              </Tooltip>
            )}
          </div>
        )}

        <div
          className={`mt-auto flex flex-wrap items-center justify-between gap-2 group-hover:opacity-100 transition-opacity bg-white z-50 ${hoverAction ? 'opacity-0' : 'opacity-100'}`}
        >
          {showAddCommentButton ? (
            <Button
              onClick={() => onAddComment?.(location)}
              variant="default"
              className="h-[2.25rem] flex-1"
            >
              <CommentIcon />{' '}
              {location.commentCount ? 'View Comment' : 'Add Comment'}
            </Button>
          ) : (
            showQuickAdd && (
              <Button
                onClick={handleAddLocation}
                variant="default"
                className="h-[2.25rem] flex-1"
                disabled={loading}
              >
                <Plus /> {loading ? 'Adding...' : 'Quick add'}
              </Button>
            )
          )}
          <Button
            onClick={() => onViewDetail?.(location)}
            variant="secondary"
            className="m-w-[8.9375rem] flex-1 h-[2.25rem] text-neutral-900"
          >
            <EyeIcon className="w-4 h-4 text-primary" />
            {viewButtonText}
          </Button>
        </div>
      </div>
      {showAddCommentButton && (
        <span className="absolute left-0 bottom-6 ml-4 text-sm">
          {location.commentCount
            ? location.commentCount > 1
              ? `${location.commentCount} Comments`
              : `${location.commentCount} Comment`
            : ''}
        </span>
      )}
    </div>
  );
}

export function LocationCardSkeleton() {
  return (
    <div className="relative border border-[#E5E5E5] rounded-lg shadow-sm flex flex-col group">
      <Skeleton className="w-full h-[12rem] rounded-t-lg bg-gray-200" />
      <div className="h-[10rem] p-4 flex flex-col gap-2">
        <Skeleton className="w-3/4 h-6 bg-gray-200" />
        <Skeleton className="w-3/4 h-5 bg-gray-200" />
        <Skeleton className="w-1/2 h-5 bg-gray-200" />
      </div>
    </div>
  );
}
