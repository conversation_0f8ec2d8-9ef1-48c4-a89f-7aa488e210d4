import * as React from 'react';
import { useState, useEffect } from 'react';

import { cn } from '@/lib/utils';
import { useDebouncedCallback } from '@/hooks/useDebounce';

export interface InputProps extends Omit<
  React.ComponentProps<'input'>,
  'onChange'
> {
  /**
   * Debounce delay in milliseconds. If provided, the onChange callback will be debounced.
   */
  debounceMs?: number;
  /**
   * Callback that will be debounced if debounceMs is provided.
   * This will be called instead of the regular onChange when debouncing is enabled.
   */
  onChange?: (event: React.ChangeEvent<HTMLInputElement>) => void;
}

function Input({
  className,
  type,
  value,
  debounceMs,
  onChange,
  ...props
}: InputProps) {
  // Check if component is controlled (value prop provided) or uncontrolled
  const isControlled = value !== undefined;

  // Only use internal state when debouncing or when uncontrolled
  const [internalValue, setInternalValue] = useState(() => value ?? '');

  // Sync internal value with external value prop when controlled
  useEffect(() => {
    if (isControlled) {
      setInternalValue(value ?? '');
    }
  }, [value, isControlled]);

  const debouncedOnChange = useDebouncedCallback((...args: unknown[]) => {
    const event = args[0] as React.ChangeEvent<HTMLInputElement>;
    onChange?.(event);
  }, debounceMs || 0);

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = event.target.value;

    // Update internal value immediately for responsive UI (when debouncing or uncontrolled)
    if (debounceMs || !isControlled) {
      setInternalValue(newValue);
    }

    // If debouncing is enabled, call the debounced onChange
    if (debounceMs && onChange) {
      debouncedOnChange(event);
    } else if (!debounceMs && onChange) {
      // If no debouncing, call onChange immediately
      onChange(event);
    }
  };

  // Determine which value to use:
  // - If debouncing: always use internal state
  // - If controlled: use value prop
  // - If uncontrolled: use internal state
  const inputValue =
    debounceMs || !isControlled ? internalValue : (value ?? '');

  return (
    <input
      type={type}
      data-slot="input"
      className={cn(
        'flex h-10 w-full min-w-0 rounded-md border border-slate-200 bg-white px-4 py-3 text-sm text-foreground placeholder:text-muted-foreground shadow-[0_1px_2px_rgba(16,24,40,0.05)] transition-all outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50',
        'focus-visible:border-slate-500 focus-visible:ring-1 focus-visible:ring-slate-500',
        'aria-invalid:border-destructive aria-invalid:ring-destructive/20',
        className
      )}
      value={inputValue}
      onChange={handleChange}
      {...props}
    />
  );
}

export { Input };
