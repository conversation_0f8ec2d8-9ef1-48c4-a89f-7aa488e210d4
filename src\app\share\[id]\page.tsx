'use client';

import ReferenceListPreview from '@/components/project/ReferenceListPreview';
import { useShareReference } from './share-reference-provider';
import Loading from '@/app/loading';

export default function PreviewReferenceList() {
  const { reference } = useShareReference();

  if (!reference) {
    return <Loading />;
  }

  return (
    <div className="space-y-11 w-full xl:w-[70.5rem] p-11 xl:px-0 mx-auto">
      <ReferenceListPreview reference={reference} isViewer={true} />
    </div>
  );
}
