'use client';

import { useState, useRef, useMemo } from 'react';
import { Photo } from '@/types/location';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { cn } from '@/lib/utils';
import Image from 'next/image';
import TagDropdown from './TagDropdown';
import { SortableThumbnailBar } from '@/components/shared';
import { CheckIcon, EditIcon, UploadIcon } from '@/lib/icons';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip';

const MAX_TAGS_TO_SHOW = 3;
const gridMap: Record<number, string> = {
  2: 'grid-cols-2',
  3: 'grid-cols-3',
  4: 'grid-cols-4',
  5: 'grid-cols-5',
  6: 'grid-cols-6',
};

interface ImageGalleryProps {
  images: Photo[];
  gridCol?: number;
  onReorder?: (reorderedImages: Photo[]) => void;
  onImageSelect?: (imageId: string, selected: boolean) => void;
  selectedImageIds?: string[];
  onImageTagsChange?: (imageId: string, tags: Photo['tags']) => void;
  onBulkTagsChange?: (imageIds: string[], tags: Photo['tags']) => void;
  onFilesSelect?: (files: FileList | null) => void;
  canEditTags?: boolean;
  canEditImages?: boolean;
  orderAllImages?: boolean;
}

export default function ImageGallery({
  images,
  gridCol = 3,
  onImageSelect,
  selectedImageIds = [],
  onImageTagsChange,
  onBulkTagsChange,
  onFilesSelect,
  onReorder,
  canEditTags = true,
  canEditImages = true,
  orderAllImages = true,
}: ImageGalleryProps) {
  const [isDragging, setIsDragging] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Calculate if all images are selected by checking each image ID
  const allSelected = useMemo(() => {
    if (images.length === 0) return false;
    return images.every((image, index) => {
      const imageId = image.id || `image-${index}`;
      return selectedImageIds.includes(imageId);
    });
  }, [images, selectedImageIds]);

  const someSelected =
    selectedImageIds.length > 0 && selectedImageIds.length < images.length;

  const handleSelectAll = (checked: boolean) => {
    images.forEach((image, index) => {
      const imageId = image.id || `image-${index}`;
      onImageSelect?.(imageId, checked);
    });
  };

  const handleImageClick = (imageId: string) => {
    const isSelected = selectedImageIds.includes(imageId);
    onImageSelect?.(imageId, !isSelected);
  };

  const handleCardClick = (imageId: string) => {
    if (canEditTags) return;

    // Toggle the selected state of the image
    const isSelected = selectedImageIds.includes(imageId);
    onImageSelect?.(imageId, !isSelected);
  };

  const handleUploadClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onFilesSelect?.(e.target.files);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
    onFilesSelect?.(e.dataTransfer.files);
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  return (
    <div className="w-full flex flex-col gap-4">
      {/* Header */}
      <div className="flex flex-col gap-3">
        <h1 className="text-2xl font-semibold leading-9 text-neutral-900">
          {images.length} {images.length === 1 ? 'image' : 'images'}
        </h1>
        <p className="text-base leading-7 text-neutral-600">
          Drag to reorder your images and add tags for context.
        </p>
      </div>

      {/* Thumbnail Row */}
      {images.length > 0 && onReorder && (
        <SortableThumbnailBar
          images={images}
          onReorder={onReorder}
          selectedImageIds={orderAllImages ? undefined : selectedImageIds}
          orderAllImages={orderAllImages}
        />
      )}

      {/* Select All and Bulk Actions */}
      {canEditTags && (
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Checkbox
              id="select-all-checkbox"
              checked={allSelected}
              onCheckedChange={handleSelectAll}
              className={cn(
                someSelected && 'data-[state=checked]:bg-primary-500'
              )}
            />
            <label
              htmlFor="select-all-checkbox"
              className="text-base font-medium text-neutral-900 cursor-pointer"
              onClick={e => {
                e.preventDefault();
                handleSelectAll(!allSelected);
              }}
            >
              Select all
            </label>
          </div>
          {onBulkTagsChange && (
            <TagDropdown
              bulkMode={true}
              selectedImageIds={selectedImageIds}
              onBulkTagsChange={onBulkTagsChange}
              trigger={
                <Button
                  variant="outline"
                  className="border-primary-300 text-primary-500 hover:bg-primary-50"
                  disabled={selectedImageIds.length === 0}
                >
                  Bulk add tags
                </Button>
              }
            />
          )}
        </div>
      )}

      {/* Image Grid - Keep original upload order */}
      <div className={`grid ${gridMap[gridCol] || 'grid-cols-3'} gap-4`}>
        {images.map((image, index) => {
          const imageId = image.id || `image-${index}`;
          const isSelected = selectedImageIds.includes(imageId);
          return (
            <div
              key={imageId}
              className={cn(
                'relative group rounded-md overflow-hidden aspect-square'
              )}
              onClick={() => handleCardClick(imageId)}
            >
              {/* Image */}
              {image.url && (
                <Image
                  src={image.url}
                  alt={`Image ${index + 1}`}
                  fill
                  className="object-cover"
                  unoptimized
                />
              )}

              {/* Overlay on Hover */}
              <div
                className={cn(
                  'absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors flex items-start p-3',
                  canEditTags ? 'justify-between' : 'justify-end'
                )}
              >
                {/* Checkbox */}
                {canEditTags && (
                  <Checkbox
                    checked={isSelected}
                    onCheckedChange={() => handleImageClick(imageId)}
                    className="bg-white"
                  />
                )}

                {/* Add Tag Button */}
                {canEditTags && onImageTagsChange && (
                  <div className="flex flex-col items-end gap-2">
                    <TagDropdown
                      imageId={imageId}
                      currentTags={image.tags}
                      onTagsChange={(id, tags) => onImageTagsChange(id, tags)}
                      trigger={
                        (image.tags?.length ?? 0) > 0 ? (
                          <Badge
                            variant="neutral"
                            className="font-semibold cursor-pointer"
                          >
                            {image.tags?.[0]?.name}
                            <EditIcon className="w-4 h-4" />
                          </Badge>
                        ) : null
                      }
                    />
                    {/* Tags */}
                    {image.tags && image.tags.length > 0 && (
                      <>
                        {image.tags?.slice(1, MAX_TAGS_TO_SHOW).map(tag => (
                          <Badge
                            key={tag.id}
                            variant="neutral"
                            className="font-semibold"
                          >
                            {tag.name}
                          </Badge>
                        ))}
                        {image.tags?.length > MAX_TAGS_TO_SHOW && (
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Badge
                                variant="neutral"
                                className="font-semibold"
                              >
                                +{image.tags?.length - MAX_TAGS_TO_SHOW} more
                              </Badge>
                            </TooltipTrigger>
                            <TooltipContent className="flex flex-col gap-2 bg-accent-foreground max-h-80 dark">
                              <div className="flex flex-col gap-1">
                                {image.tags
                                  ?.slice(MAX_TAGS_TO_SHOW)
                                  .map(tag => (
                                    <Badge
                                      key={tag.id}
                                      variant="neutral"
                                      className="font-semibold"
                                    >
                                      {tag.name}
                                    </Badge>
                                  ))}
                              </div>
                            </TooltipContent>
                          </Tooltip>
                        )}
                      </>
                    )}
                  </div>
                )}
                {!canEditTags && (
                  <div className="flex flex-col items-end gap-2">
                    {image.tags?.slice(0, MAX_TAGS_TO_SHOW).map(tag => (
                      <Badge
                        key={tag.id}
                        variant="neutral"
                        className="font-semibold"
                      >
                        {tag.name}
                      </Badge>
                    ))}
                    {(image.tags?.length ?? 0) > MAX_TAGS_TO_SHOW && (
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Badge variant="neutral" className="font-semibold">
                            +{(image.tags?.length ?? 0) - MAX_TAGS_TO_SHOW} more
                          </Badge>
                        </TooltipTrigger>
                        <TooltipContent className="flex flex-col gap-2 bg-accent-foreground max-h-80 dark">
                          <div className="flex flex-col gap-1">
                            {image.tags?.slice(MAX_TAGS_TO_SHOW).map(tag => (
                              <Badge
                                key={tag.id}
                                variant="neutral"
                                className="font-semibold"
                              >
                                {tag.name}
                              </Badge>
                            ))}
                          </div>
                        </TooltipContent>
                      </Tooltip>
                    )}
                  </div>
                )}
              </div>

              {/* Check icon centered in the image if selected and can not edit tags */}
              {isSelected && !canEditTags && (
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="w-10 h-10 rounded-full bg-white flex items-center justify-center">
                    <CheckIcon className="w-7 h-5 text-[#1C1B1F]" />
                  </div>
                </div>
              )}
            </div>
          );
        })}

        {/* Upload Card */}
        {canEditImages && onFilesSelect && (
          <div
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
            onClick={handleUploadClick}
            className={cn(
              'relative rounded-md overflow-hidden border-2 aspect-square cursor-pointer transition-colors flex flex-col items-center justify-center',
              isDragging
                ? 'border-primary-300 bg-primary-50'
                : 'border-dashed border-neutral-300 bg-neutral-50 hover:border-primary-200 hover:bg-neutral-100'
            )}
          >
            {/* Upload Icon */}
            <div className="flex flex-col items-center gap-2">
              <div className="w-16 h-16 rounded-full bg-neutral-200 flex items-center justify-center">
                <UploadIcon className="w-8 h-6 text-[#737373]" />
              </div>
              <p className="text-sm font-medium text-neutral-700 text-center">
                Add images
              </p>
            </div>

            {/* Hidden File Input */}
            <input
              ref={fileInputRef}
              type="file"
              accept="image/jpeg,image/jpg,image/png"
              multiple
              onChange={handleFileChange}
              className="hidden"
            />
          </div>
        )}
      </div>
    </div>
  );
}
