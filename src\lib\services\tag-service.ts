import { CreateTagData, Tag, UpdateTagData } from '@/types/tag';
import { httpClient } from '../http-client';

const buildQueryParams = (params: {
  page?: number;
  search?: string;
  propertyType?: string;
  limit?: number;
}) => {
  const { page, search, propertyType, limit } = params;
  const queryParams: string[] = [];
  if (page) {
    queryParams.push(`page=${page}`);
  }
  if (limit) {
    queryParams.push(`limit=${limit}`);
  }
  if (search && search !== '') {
    queryParams.push(`search=${search}`);
  }
  if (propertyType && propertyType !== 'all') {
    queryParams.push(`propertyType=${propertyType}`);
  }
  return queryParams.join('&');
};
export class TagService {
  async getAllTags(params?: {
    search?: string;
    type?: string;
  }): Promise<Tag[]> {
    let queryParams = '';
    if (params) {
      const { search, type } = params;
      queryParams = buildQueryParams({
        search,
        propertyType: type,
      });
    }

    return httpClient.get<Tag[]>(
      `/tags/all${queryParams ? `?${queryParams}` : ''}`
    );
  }

  async getTags(params?: {
    page?: number;
    search?: string;
    propertyType?: string;
    limit?: number;
  }): Promise<{ data: Tag[]; meta: { totalPages: number } }> {
    let queryParams = '';
    if (params) {
      const { page, search, propertyType, limit } = params;
      queryParams = buildQueryParams({
        page,
        search,
        propertyType,
        limit,
      });
    }

    return httpClient.get<{ data: Tag[]; meta: { totalPages: number } }>(
      `/tags${queryParams ? `?${queryParams}` : ''}`
    );
  }

  /**
   * Create new tag (admin only)
   */
  async createTag(tagData: Partial<CreateTagData>): Promise<Tag> {
    return httpClient.post<Tag>('/tags', tagData);
  }

  /**
   * Update tag by ID (admin only)
   */
  async updateTag(id: string, tagData: Partial<UpdateTagData>): Promise<Tag> {
    return httpClient.patch<Tag>(`/tags/${id}`, tagData);
  }
  /**
   * Delete tag by ID (admin only)
   */
  async deleteTag(id: string): Promise<void> {
    return httpClient.delete<void>(`/tags/${id}`);
  }

  async bulkDeleteTags(tagIds: string[]): Promise<void> {
    return httpClient.delete<void>(`/tags/bulk`, { tagIds });
  }
}

// Export singleton instance
export const tagService = new TagService();
