'use client';

import { createContext, useContext, ReactNode } from 'react';
import { Reference } from '@/types';

interface ShareReferenceContextType {
  reference: Reference | null;
}

const ShareReferenceContext = createContext<
  ShareReferenceContextType | undefined
>(undefined);

export function useShareReference() {
  const context = useContext(ShareReferenceContext);
  if (context === undefined) {
    throw new Error(
      'useShareReference must be used within ShareReferenceProvider'
    );
  }
  return context;
}

interface ShareReferenceProviderProps {
  children: ReactNode;
  reference: Reference | null;
}

export default function ShareReferenceProvider({
  children,
  reference,
}: ShareReferenceProviderProps) {
  return (
    <ShareReferenceContext.Provider value={{ reference }}>
      {children}
    </ShareReferenceContext.Provider>
  );
}
