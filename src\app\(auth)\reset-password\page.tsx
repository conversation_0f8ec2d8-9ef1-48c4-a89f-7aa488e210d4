'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useSearchParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Routes } from '@/lib/routes';
import Image from 'next/image';
import PasswordForm from '@/components/PasswordForm';
import { Heading, Body } from '@/components/ui/typography';
import { toastError, toastSuccess } from '@/lib/toast';
import { authService } from '@/lib/services/auth-service';
import { handleError } from '@/lib/error-handler';

export default function ResetPasswordPage() {
  const [loading, setLoading] = useState(false);
  const router = useRouter();
  const searchParams = useSearchParams();
  const token = searchParams.get('token');

  const onSubmit = async (password: string) => {
    if (!token) {
      toastError(
        'Invalid or missing reset token. Please use the link from your email.'
      );
      return;
    }

    setLoading(true);
    try {
      await authService.resetPasswordConfirm({
        token,
        password,
      });

      // Show success toast
      toastSuccess('Password updated successfully.');
      router.push(Routes.SIGN_IN);
    } catch (err) {
      const { message, logError } = handleError(
        err,
        'An error occurred while resetting password'
      );

      logError();
      toastError(message);
    } finally {
      setLoading(false);
    }
  };

  if (!token) {
    return (
      <div className="space-y-6 max-w-[28.5rem] w-full">
        <div className="text-center md:text-left">
          <div className="flex items-center justify-center">
            <Image
              src="/assets/logo.svg"
              alt="Scoutr Logo"
              width={138}
              height={0}
            />
          </div>
          <div className="space-y-2 mt-8">
            <Heading
              level={3}
              className="font-semibold text-center text-red-600"
            >
              Error
            </Heading>
            <Body className="text-center max-w-[24.625rem] mx-auto">
              Invalid or missing reset token. Please use the link from your
              email. If you didn&apos;t receive an email, please check your spam
              folder or request a new password reset link.
            </Body>
          </div>
        </div>
        <Button
          onClick={() => router.push(Routes.FORGOT_PASSWORD)}
          className="w-full"
        >
          Request New Link
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-12 max-w-[28.5rem] w-full">
      <div className="text-center md:text-left space-y-12">
        <div className="flex items-center justify-center">
          <Image
            src="/assets/logo.svg"
            alt="Scoutr Logo"
            width={138}
            height={0}
          />
        </div>
        <div className="space-y-2 mt-2">
          <Heading level={3} className="text-header font-semibold text-center">
            Reset your password
          </Heading>
          <Body className="text-center max-w-[24.625rem] mx-auto">
            Choose a strong password to secure your account.
          </Body>
        </div>
      </div>

      <PasswordForm
        termEnable={false}
        buttonText="Reset Password"
        onSubmit={onSubmit}
        isLoading={loading}
        showSignInLink={false}
        isResetPassword={true}
      />
    </div>
  );
}
