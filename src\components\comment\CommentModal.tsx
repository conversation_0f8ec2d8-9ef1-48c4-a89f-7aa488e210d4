import { Dialog, DialogContent, DialogHeader } from '../ui/dialog';
import { Location, Reference, ReferenceItem } from '@/types';
import { CommentModalHeader } from './CommentModalHeader';
import { CommentModalFooter } from './CommentModalFooter';
import { useAuth } from '@/contexts/auth-context';
import { CommentModalBody } from './CommentModalBody';
import { useComment } from '@/hooks/useComment';
import { toastSuccess, toastError } from '@/lib/toast';
import { useEffect } from 'react';

interface CommentModalProps {
  open: boolean;
  location?: Location;
  reference?: Reference;
  referenceItem?: ReferenceItem;
  onOpenChange: (open: boolean) => void;
}

export const CommentModal = ({
  open,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  location,
  reference,
  referenceItem,
  onOpenChange,
}: CommentModalProps) => {
  const { user } = useAuth();
  const {
    comments,
    isLoading,
    isCreating,
    createComment,
    deleteComment,
    updateComment,
    error,
  } = useComment(open, reference?.id, referenceItem?.id);

  console.log('reference, referenceItem', reference, referenceItem);

  // Show error toast if comment creation fails
  useEffect(() => {
    if (error && open) {
      toastError(
        referenceItem
          ? 'Failed to add comment. Please try again.'
          : 'Comments can only be added to project locations, not the project itself.'
      );
    }
  }, [error, open, referenceItem]);

  const handleComment = (content: string) => {
    if (!referenceItem) {
      toastError(
        'Comments can only be added to project locations, not the project itself.'
      );
      return;
    }
    createComment(content, {
      onSuccess: () => {
        toastSuccess('Comment posted.');
      },
      onError: () => {
        toastError('Failed to add comment. Please try again.');
      },
    });
  };

  const handleCancel = () => {
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="p-0 gap-0" showCloseButton={false}>
        <DialogHeader className="p-4 border-b">
          <CommentModalHeader
            title={reference?.projectName ?? ''}
            commentCount={comments.length ?? 0}
            loading={isLoading}
          />
        </DialogHeader>
        <CommentModalBody
          comments={comments}
          loading={isLoading}
          user={user}
          onDelete={deleteComment}
          onEdit={(commentId, content) => updateComment({ commentId, content })}
        />
        <CommentModalFooter
          user={user}
          onComment={handleComment}
          onCancel={handleCancel}
          isCreating={isCreating}
        />
      </DialogContent>
    </Dialog>
  );
};
