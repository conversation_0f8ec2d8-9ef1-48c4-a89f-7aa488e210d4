import * as React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';

const iconButtonVariants = cva(
  'inline-flex items-center justify-center rounded-full transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 cursor-pointer',
  {
    variants: {
      size: {
        small: 'h-8 w-8',
        medium: 'h-10 w-10',
        large: 'h-12 w-12',
      },
      color: {
        default: 'hover:bg-accent hover:text-accent-foreground',
        primary: 'text-primary hover:bg-primary/10 focus-visible:ring-primary',
        secondary:
          'text-secondary-foreground hover:bg-secondary focus-visible:ring-secondary',
        error:
          'text-destructive hover:bg-destructive/10 focus-visible:ring-destructive',
        info: 'text-blue-600 hover:bg-blue-50 focus-visible:ring-blue-500 dark:text-blue-400 dark:hover:bg-blue-950',
        success:
          'text-green-600 hover:bg-green-50 focus-visible:ring-green-500 dark:text-green-400 dark:hover:bg-green-950',
        warning:
          'text-yellow-600 hover:bg-yellow-50 focus-visible:ring-yellow-500 dark:text-yellow-400 dark:hover:bg-yellow-950',
      },
      edge: {
        false: '',
        start: 'ml-[-8px]',
        end: 'mr-[-8px]',
      },
    },
    defaultVariants: {
      size: 'medium',
      color: 'default',
      edge: false,
    },
  }
);

export interface IconButtonProps
  extends
    Omit<React.ButtonHTMLAttributes<HTMLButtonElement>, 'color'>,
    VariantProps<typeof iconButtonVariants> {
  /**
   * The icon to display inside the button
   */
  children: React.ReactNode;
  /**
   * If true, the button will have edge positioning
   */
  edge?: 'start' | 'end' | false;
  /**
   * The size of the button
   */
  size?: 'small' | 'medium' | 'large';
  /**
   * The color variant of the button
   */
  color?:
    | 'default'
    | 'primary'
    | 'secondary'
    | 'error'
    | 'info'
    | 'success'
    | 'warning';
}

export const IconButton = React.forwardRef<HTMLButtonElement, IconButtonProps>(
  ({ className, size, color, edge, children, disabled, ...props }, ref) => {
    return (
      <button
        ref={ref}
        type="button"
        className={cn(iconButtonVariants({ size, color, edge, className }))}
        disabled={disabled}
        {...props}
      >
        {children}
      </button>
    );
  }
);

IconButton.displayName = 'IconButton';
