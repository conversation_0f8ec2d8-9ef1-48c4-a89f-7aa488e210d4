import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardTitle } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { EditIcon, LocationIcon, TrashIcon } from '@/lib/icons';
import { Routes } from '@/lib/routes';
import { useRouter } from 'next/navigation';
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  SortingState,
  PaginationState,
  useReactTable,
} from '@tanstack/react-table';
import { useCallback, useMemo, useState } from 'react';
import ConfirmDialog from '@/components/shared/ConfirmDialog';
import Image from 'next/image';
import { Location } from '@/types';
import {
  useDeleteLocation,
  useUpdateLocationStatus,
} from '@/hooks/api/useLocations';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { Loader2Icon } from 'lucide-react';
import { IconButton } from '@/components/shared/IconButton';
import LocationsTableLoading from './locations-table-loading';

interface LocationTableProps {
  data: Location[];
  loading: boolean;
  refreshing?: boolean;
  isLocationsView?: boolean;
  pagination: PaginationState;
  pageCount: number;
  onPaginationChange: (pagination: PaginationState) => void;
  onActionSuccess: () => void;
}

export default function LocationsTable({
  data,
  loading,
  refreshing = false,
  isLocationsView = true,
  pagination,
  pageCount,
  onPaginationChange,
  onActionSuccess,
}: LocationTableProps) {
  const router = useRouter();
  const [sorting, setSorting] = useState<SortingState>([]);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState<boolean>(false);
  const [isRejectDialogOpen, setIsRejectDialogOpen] = useState<boolean>(false);
  const [isAcceptDialogOpen, setIsAcceptDialogOpen] = useState<boolean>(false);
  const [locationSelected, setLocationSelected] = useState<Location>();

  // Mutations
  const deleteLocationMutation = useDeleteLocation();
  const updateLocationStatusMutation = useUpdateLocationStatus();

  const rejectLocationRequest = useCallback(async () => {
    if (locationSelected) {
      try {
        await updateLocationStatusMutation.mutateAsync({
          id: locationSelected.id,
          type: 'reject',
        });
        setIsRejectDialogOpen(false);
        setLocationSelected(undefined);
      } catch {
        console.error('Error rejecting location request');
      }
    }
  }, [locationSelected, updateLocationStatusMutation]);

  const acceptLocationRequest = useCallback(async () => {
    if (locationSelected) {
      try {
        await updateLocationStatusMutation.mutateAsync({
          id: locationSelected.id,
          type: 'accept',
        });
        setIsAcceptDialogOpen(false);
        setLocationSelected(undefined);
      } catch {
        console.error('Error accepting location request');
      }
    }
  }, [locationSelected, updateLocationStatusMutation]);

  const columns: ColumnDef<Location>[] = useMemo(() => {
    const locationsColumns: ColumnDef<Location>[] = [
      {
        accessorKey: 'location',
        header: ({ column }) => (
          <Button
            className="p-0"
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            Scoutr location
          </Button>
        ),
        cell: ({ row }) => {
          const coverImageUrl = row.original.coverImageUrl;
          return (
            <div className="flex items-center space-x-4">
              <div className="relative w-15 h-12">
                {!coverImageUrl ? (
                  <div className="w-full h-full flex items-center justify-center bg-border rounded-lg text-white">
                    Photo
                  </div>
                ) : (
                  <Image
                    src={coverImageUrl}
                    alt="Cover Image"
                    fill
                    className="rounded-lg object-cover"
                  />
                )}
              </div>
              {isLocationsView ? (
                <div className="flex-1">
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div className="text-sm font-medium break-normal whitespace-normal line-clamp-3">
                        {row.original.title}
                      </div>
                    </TooltipTrigger>
                    <TooltipContent className="whitespace-pre-wrap">
                      <span className="block text-wrap max-w-[20rem]">
                        {row.original.title}
                      </span>
                    </TooltipContent>
                  </Tooltip>
                </div>
              ) : (
                <Button
                  onClick={() => router.push(`/location/${row.original.id}`)}
                  variant="link"
                >
                  View Location
                </Button>
              )}
            </div>
          );
        },
        meta: {
          width: '25%',
        },
      },
      {
        accessorKey: 'address',
        header: ({ column }) => (
          <Button
            className="p-0"
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            Address
          </Button>
        ),
        cell: ({ row }) => (
          <p className="whitespace-break-spaces">{row.getValue('address')}</p>
        ),
        meta: {
          width: '20%',
        },
      },
      {
        accessorKey: 'tags',
        header: ({ column }) => (
          <div className="w-32 whitespace-nowrap">
            <Button
              className="p-0"
              variant="ghost"
              onClick={() =>
                column.toggleSorting(column.getIsSorted() === 'asc')
              }
            >
              Tags
            </Button>
          </div>
        ),
        cell: ({ row }) => {
          const tagCount = row.original.tags.length;
          return (
            <div className="flex items-center gap-2 flex-wrap">
              {row.original.tags
                .slice(0, tagCount > 2 ? 1 : tagCount)
                .map((tag, index) => (
                  <Badge
                    key={index}
                    variant="secondary"
                    className={`${tag.color ? `bg-${tag.color} text-white` : ''}`}
                  >
                    {tag.name}
                  </Badge>
                ))}
              {tagCount > 2 && (
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="text-right">
                      <Badge variant="secondary">
                        +{tagCount - 1} more{tagCount - 1 > 1 ? 's' : ''}
                      </Badge>
                    </div>
                  </TooltipTrigger>
                  <TooltipContent className="flex flex-col gap-2 bg-accent-foreground max-h-80 dark">
                    <div className="flex flex-col gap-2">
                      {row.original.tags?.slice(1).map(tag => (
                        <Badge
                          key={tag.id}
                          variant="secondary"
                          className={`${tag.color ? `bg-${tag.color} text-white` : ''}`}
                        >
                          <span className="truncate">{tag.name}</span>
                        </Badge>
                      ))}
                    </div>
                  </TooltipContent>
                </Tooltip>
              )}
            </div>
          );
        },
        meta: {
          width: '20%',
        },
      },
      {
        accessorKey: 'contacts',
        header: ({ column }) => (
          <Button
            className="p-0"
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          >
            Contact info
          </Button>
        ),
        cell: ({ row }) => (
          <>
            <p className="whitespace-break-spaces">
              {row.getValue('contacts.email')}
            </p>
            <p className="whitespace-break-spaces">
              {row.getValue('contact.phone')}
            </p>
          </>
        ),
        meta: {
          width: '20%',
        },
      },
      {
        id: 'actions',
        header: () => {
          return (
            <div className="flex justify-end items-center">
              {(refreshing || deleteLocationMutation.isPending) && (
                <Loader2Icon className="h-6 w-6 animate-spin text-primary" />
              )}
            </div>
          );
        },
        cell: ({ row }) => {
          if (isLocationsView) {
            return (
              <div className="flex justify-end gap-1">
                <IconButton
                  color="primary"
                  size="small"
                  onClick={() => {
                    router.push(
                      `${Routes.ADMIN_EDIT_LOCATION}/${row.original.id}`
                    );
                  }}
                >
                  <EditIcon />
                </IconButton>
                <IconButton
                  color="primary"
                  size="small"
                  onClick={() => {
                    setLocationSelected(row.original);
                    setIsDeleteDialogOpen(true);
                  }}
                >
                  <TrashIcon />
                </IconButton>
              </div>
            );
          }
          // For requests view, show Accept/Reject buttons with confirm dialogs
          return (
            <div className="flex justify-end gap-3">
              <Button
                onClick={() => {
                  setLocationSelected(row.original);
                  setIsRejectDialogOpen(true);
                }}
                size="sm"
                variant="outline"
              >
                Reject
              </Button>
              <Button
                onClick={() => {
                  setLocationSelected(row.original);
                  setIsAcceptDialogOpen(true);
                }}
                size="sm"
              >
                Accept
              </Button>
            </div>
          );
        },
        meta: {
          width: '200px',
        },
      },
    ];

    return locationsColumns;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [refreshing, router, isLocationsView]);
  const handleConfirmDelete = async () => {
    if (locationSelected) {
      try {
        await deleteLocationMutation.mutateAsync(locationSelected.id);
        onActionSuccess();
        setIsDeleteDialogOpen(false);
      } catch {
        console.error('Error deleting location');
      }
    }
  };

  const table = useReactTable({
    data,
    columns,
    pageCount,
    state: {
      sorting,
      pagination,
    },
    onSortingChange: setSorting,
    onPaginationChange: updaterOrValue => {
      // onPaginationChange may expect either a PaginationState or an Updater function.
      // This ensures compatibility with OnChangeFn<PaginationState>.
      if (typeof updaterOrValue === 'function') {
        onPaginationChange(updaterOrValue(pagination));
      } else {
        onPaginationChange(updaterOrValue);
      }
    },
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    manualPagination: true,
    manualSorting: true,
  });

  return (
    <>
      <Card className="py-0 rounded-md shadow-sm gap-0">
        <CardTitle className="h-[3.875rem] flex items-center px-6 bg-[#FAFAFA] rounded-md">
          {isLocationsView ? 'Active Locations' : 'Location Requests'}
        </CardTitle>
        <CardContent className="px-0">
          <div className="border-t">
            <Table className="table-fixed">
              <TableHeader>
                {table.getHeaderGroups().map(headerGroup => (
                  <TableRow key={headerGroup.id}>
                    {headerGroup.headers.map(header => (
                      <TableHead
                        className="px-6 bg-[#FAFAFA]"
                        key={header.id}
                        style={{
                          width: (
                            header.column.columnDef.meta as { width: string }
                          )?.width,
                        }}
                      >
                        {header.isPlaceholder
                          ? null
                          : flexRender(
                              header.column.columnDef.header,
                              header.getContext()
                            )}
                      </TableHead>
                    ))}
                  </TableRow>
                ))}
              </TableHeader>
              <TableBody>
                {loading ? (
                  <LocationsTableLoading />
                ) : table.getRowModel().rows?.length ? (
                  table.getRowModel().rows.map(row => (
                    <TableRow key={row.id} className="group/row">
                      {row.getVisibleCells().map(cell => (
                        <TableCell className="px-6" key={cell.id}>
                          {flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext()
                          )}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell
                      colSpan={columns.length}
                      className="h-[32.375rem] text-center"
                    >
                      <div className="h-full flex flex-col items-center justify-center gap-4">
                        <div className="w-29 h-29 bg-[#E5E7EB] flex items-center justify-center rounded-full">
                          <LocationIcon color="#D1D5DB" className="w-15 h-13" />
                        </div>
                        <span>No locations yet.</span>
                      </div>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
      <ConfirmDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
        title="Remove location?"
        description="This location will be permanently removed from the platform. It will no longer be available in reference lists."
        confirmText="Remove"
        cancelText="Keep location"
        onConfirm={handleConfirmDelete}
        loading={deleteLocationMutation.isPending}
      />
      <ConfirmDialog
        open={isRejectDialogOpen}
        onOpenChange={setIsRejectDialogOpen}
        title="Reject this location?"
        description="This location will be marked as rejected and the property owner will be notified."
        confirmText="Reject"
        cancelText="Cancel"
        onConfirm={rejectLocationRequest}
        loading={updateLocationStatusMutation.isPending}
      />
      <ConfirmDialog
        open={isAcceptDialogOpen}
        onOpenChange={setIsAcceptDialogOpen}
        title="Approve this location?"
        description="Once approved, the location will be visible on the platform and accessible to all assigned users."
        confirmText="Approve"
        cancelText="Cancel"
        onConfirm={acceptLocationRequest}
        loading={updateLocationStatusMutation.isPending}
      />
    </>
  );
}
