import { Button } from '@/components/ui/button';
import { Heading, Body } from '@/components/ui/typography';
import { Plus } from 'lucide-react';
import { UndrawEnterPassword } from '@/components/illustrations/Illustrations';

interface EmptyProjectListProps {
  onCreateProject: () => void;
}

export function EmptyProjectList({ onCreateProject }: EmptyProjectListProps) {
  return (
    <div className="flex flex-col items-center justify-center flex-1 py-20">
      <div className="flex flex-col items-center gap-12 max-w-[27.5625rem]">
        <UndrawEnterPassword width={229} height={192} />
        <div className="flex flex-col gap-2 items-center text-center">
          <Heading level={3} className="font-semibold">
            No projects yet
          </Heading>
          <Body className="text-base text-neutral-500">
            <p className="mb-0">
              Start organizing your location references by creating your
            </p>
            <p>first project.</p>
          </Body>
        </div>
        <Button
          onClick={onCreateProject}
          variant="default"
          className="bg-[#5b0677] hover:bg-[#6B1F85] text-white h-[41px] px-4 py-2 rounded-md"
        >
          <Plus className="h-3 w-3 mr-2" />
          Submit Location
        </Button>
      </div>
    </div>
  );
}
