'use client';

import { useMemo } from 'react';
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';
import { cn } from '@/lib/utils';

type PaginationItemType = number | 'ellipsis';

interface DataTablePaginationProps {
  page: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  className?: string;
}

const buildPaginationItems = (
  page: number,
  totalPages: number
): PaginationItemType[] => {
  const items: PaginationItemType[] = [];
  const safeTotal = Math.max(totalPages, 0);
  const current = Math.min(Math.max(page, 1), safeTotal);

  if (safeTotal <= 1) {
    if (safeTotal === 1) {
      items.push(1);
    }
    return items;
  }

  if (safeTotal <= 7) {
    for (let i = 1; i <= safeTotal; i++) {
      items.push(i);
    }
    return items;
  }

  items.push(1);

  const showLeftEllipsis = current > 4;
  const showRightEllipsis = current < safeTotal - 3;

  let left = Math.max(2, current - 1);
  let right = Math.min(safeTotal - 1, current + 1);

  if (!showLeftEllipsis) {
    right = 5;
  }

  if (!showRightEllipsis) {
    left = safeTotal - 4;
  }

  if (showLeftEllipsis) {
    items.push('ellipsis');
  }

  for (let i = left; i <= right; i++) {
    items.push(i);
  }

  if (showRightEllipsis) {
    items.push('ellipsis');
  }

  items.push(safeTotal);

  return items;
};

export function DataTablePagination({
  page,
  totalPages,
  onPageChange,
  className,
}: DataTablePaginationProps) {
  const items = useMemo(
    () => buildPaginationItems(page, totalPages),
    [page, totalPages]
  );

  const safeTotal = Math.max(totalPages, 0);
  const canGoPrevious = page > 1;
  const canGoNext = page < safeTotal;

  if (safeTotal <= 1) {
    return null;
  }

  const handlePageChange = (nextPage: number) => {
    if (nextPage < 1 || nextPage > safeTotal || nextPage === page) {
      return;
    }
    onPageChange(nextPage);
  };

  return (
    <Pagination className={cn('mt-6', className)}>
      <PaginationContent>
        <PaginationItem>
          <PaginationPrevious
            onClick={() => handlePageChange(page - 1)}
            className={cn(!canGoPrevious && 'pointer-events-none opacity-50')}
          />
        </PaginationItem>
        {items.map((item, index) => {
          if (item === 'ellipsis') {
            return (
              <PaginationItem key={`ellipsis-${index}`}>
                <PaginationEllipsis />
              </PaginationItem>
            );
          }

          return (
            <PaginationItem key={`page-${item}`}>
              <PaginationLink
                href="#"
                onClick={event => {
                  event.preventDefault();
                  handlePageChange(item as number);
                }}
                isActive={page === (item as number)}
                className="cursor-pointer"
              >
                {item}
              </PaginationLink>
            </PaginationItem>
          );
        })}
        <PaginationItem>
          <PaginationNext
            onClick={() => handlePageChange(page + 1)}
            className={cn(!canGoNext && 'pointer-events-none opacity-50')}
          />
        </PaginationItem>
      </PaginationContent>
    </Pagination>
  );
}

export default DataTablePagination;
