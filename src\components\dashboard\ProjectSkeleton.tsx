export function ProjectSkeleton() {
  return (
    <div className="flex flex-col items-start rounded-tl-[12px] rounded-tr-[12px] rounded-bl-[12px] rounded-br-[12px] overflow-hidden border border-neutral-200 bg-white">
      {/* Image Skeleton */}
      <div className="relative w-full h-[192px] bg-neutral-300">
        <div className="w-full h-full bg-gray-200 animate-pulse" />
      </div>
      {/* Content Skeleton */}
      <div className="border border-neutral-200 border-solid box-border flex flex-col gap-4 items-start p-4 w-full rounded-bl-[12px] rounded-br-[12px]">
        <div className="flex flex-col gap-1 items-start w-full">
          <div className="h-6 bg-gray-200 rounded w-3/4 animate-pulse" />
          <div className="h-5 bg-gray-200 rounded w-1/2 animate-pulse" />
          <div className="h-5 bg-gray-200 rounded w-2/3 animate-pulse" />
        </div>
        {/* Action Buttons Skeleton - Always reserve space */}
        <div
          className="flex gap-2 items-center w-full"
          style={{ minHeight: '36px' }}
        >
          <div className="flex-1 h-9 bg-gray-200 rounded-md animate-pulse" />
          <div className="flex-1 h-9 bg-gray-200 rounded-md animate-pulse" />
        </div>
      </div>
    </div>
  );
}

export function ProjectSkeletonGrid({ count = 3 }: { count?: number }) {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
      {Array.from({ length: count }).map((_, index) => (
        <ProjectSkeleton key={index} />
      ))}
    </div>
  );
}
