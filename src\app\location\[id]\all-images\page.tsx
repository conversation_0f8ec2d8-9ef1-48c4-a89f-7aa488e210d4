'use client';
import ImageFullScreen from '@/components/ImageFullScreen';
import { locationService } from '@/lib/services';
import { toastError } from '@/lib/toast';
import { Photo } from '@/types/location';
import { useRouter } from 'next/navigation';
import { use, useCallback, useEffect, useState } from 'react';

export default function AllImagesPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = use(params);
  const [images, setImages] = useState<Photo[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [isExpanded, setIsExpanded] = useState<boolean>(false);
  const router = useRouter();

  const fetchImages = useCallback(async () => {
    try {
      setLoading(true);
      const response = await locationService.getLocationById(id);
      setImages(response.images);
    } catch {
      toastError('Failed to get images');
    } finally {
      setLoading(false);
    }
  }, [id]);

  useEffect(() => {
    if (!id) return;
    fetchImages();
  }, [fetchImages, id]);

  return (
    <ImageFullScreen
      loading={loading}
      open={true}
      images={images}
      isExpanded={isExpanded}
      onOpenChange={setIsExpanded}
      onBack={() => router.push(`/location/${id}`)}
      onToggleZoom={() => setIsExpanded(!isExpanded)}
    />
  );
}
