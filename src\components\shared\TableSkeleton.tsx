import { Skeleton } from '@/components/ui/skeleton';
import { TableCell, TableRow } from '@/components/ui/table';

interface TableSkeletonProps {
  /**
   * Number of skeleton rows to display
   * @default 6
   */
  rows?: number;
  /**
   * Number of columns
   * @default 4
   */
  columns?: number;
  /**
   * Custom cell renderer for each column
   */
  renderCell?: (columnIndex: number, rowIndex: number) => React.ReactNode;
}

/**
 * Standardized table skeleton loading component
 */
export function TableSkeleton({
  rows = 6,
  columns = 4,
  renderCell,
}: TableSkeletonProps) {
  return (
    <>
      {Array.from({ length: rows }).map((_, rowIndex) => (
        <TableRow key={`skeleton-row-${rowIndex}`}>
          {Array.from({ length: columns }).map((_, colIndex) => (
            <TableCell
              key={`skeleton-cell-${rowIndex}-${colIndex}`}
              className="px-6"
            >
              {renderCell ? (
                renderCell(colIndex, rowIndex)
              ) : (
                <Skeleton className="h-4 w-full" />
              )}
            </TableCell>
          ))}
        </TableRow>
      ))}
    </>
  );
}

/**
 * Standardized table row skeleton with avatar and text
 */
export function TableRowSkeleton({
  rows = 6,
  showAvatar = false,
  showActions = false,
}: {
  rows?: number;
  showAvatar?: boolean;
  showActions?: boolean;
}) {
  return (
    <>
      {Array.from({ length: rows }).map((_, i) => (
        <TableRow key={`skeleton-${i}`}>
          <TableCell className="px-6">
            <div className="flex items-center gap-4">
              {showAvatar && <Skeleton className="h-8 w-8 rounded-full" />}
              <Skeleton className="h-4 w-32" />
            </div>
          </TableCell>
          <TableCell className="px-6">
            <Skeleton className="h-4 w-48" />
          </TableCell>
          <TableCell className="px-6">
            <Skeleton className="h-4 w-20" />
          </TableCell>
          {showActions && (
            <TableCell className="px-6">
              <div className="flex items-center justify-end gap-2">
                <Skeleton className="h-4 w-10" />
                <Skeleton className="h-4 w-10" />
              </div>
            </TableCell>
          )}
        </TableRow>
      ))}
    </>
  );
}
