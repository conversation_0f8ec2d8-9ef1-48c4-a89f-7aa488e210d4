import { ReferenceItem } from '@/types';
import Image from 'next/image';
import { Heading, Body, Small } from '../ui/typography';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { ThumbsUp } from 'lucide-react';
import { ThumbsDown } from 'lucide-react';

interface ReferencePreviewItemProps {
  item: ReferenceItem & {
    like: boolean;
    dislike: boolean;
  };
  isViewer?: boolean;
  isSubmitted?: boolean;
  onLike?: (itemId: string) => void;
  onDislike?: (itemId: string) => void;
  onSelect?: (itemId: string, index: number) => void;
  onOpenComment?: (itemId: string) => void;
}

export default function ReferencePreviewItem({
  item,
  isViewer = false,
  isSubmitted = false,
  onLike,
  onDislike,
  onSelect,
  onOpenComment,
}: ReferencePreviewItemProps) {
  return (
    <div className="flex justify-between">
      <div className="flex flex-wrap items-center w-full max-w-[34.5rem] gap-6">
        {item.images?.slice(0, 2).map((image, index) => (
          <div
            key={image.id}
            className="relative h-[21.0625rem] w-[calc(50%-0.75rem)] only:w-full rounded-lg overflow-hidden"
            onClick={() => onSelect?.(item.id, index)}
          >
            <Image
              src={image.image.url}
              alt={image.image.url ?? ''}
              fill
              className="object-cover"
            />
          </div>
        ))}
      </div>
      <div className="w-full max-w-[28.5rem]">
        <div className="flex flex-col">
          <Heading level={3}>{item.location.title}</Heading>
          <Body className="text-[#737373] text-sm leading-5">
            {item.location.address}
          </Body>
          <div className="flex flex-wrap gap-1.5 my-4 max-h-24 overflow-y-auto">
            {item.location.tags.map(tag => (
              <Badge
                key={tag.id}
                variant="secondary"
                className={`${tag.color ? `bg-${tag.color} text-white` : ''}`}
              >
                {tag.name}
              </Badge>
            ))}
          </div>
          <Body className="text-foreground">{item.location.description}</Body>
          <Small
            className="text-sub-header mt-4 cursor-pointer"
            onClick={() => onOpenComment?.(item.id)}
          >
            {item.commentCount}{' '}
            {item.commentCount === 1 ? 'comment' : 'comments'}
          </Small>
          {isViewer && (
            <div className="flex items-center gap-4 mt-7">
              <Button
                variant="outline"
                disabled={isSubmitted}
                className={`w-10 h-10 rounded-full border-black group ${item.like ? 'bg-primary hover:bg-primary/90' : ''}`}
                onClick={() => onLike?.(item.id)}
              >
                <ThumbsUp
                  className={`${item.like ? 'stroke-white group-hover:stroke-white/90' : ''} size-6`}
                />
              </Button>
              <Button
                variant="outline"
                disabled={isSubmitted}
                className={`w-10 h-10 rounded-full border-black group ${item.dislike ? 'bg-primary hover:bg-primary/90' : ''}`}
                onClick={() => onDislike?.(item.id)}
              >
                <ThumbsDown
                  className={`${item.dislike ? 'stroke-white group-hover:stroke-white/90' : ''} size-6`}
                />
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
