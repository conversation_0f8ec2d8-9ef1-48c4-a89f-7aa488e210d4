'use client';

import { useMemo } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { Eye, EyeOff, Loader2, Check } from 'lucide-react';
import { Body } from '@/components/ui/typography';
import { CircleCheckOutlineIcon, CircleXOutlineIcon } from '@/lib/icons';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { passwordFormSchema, PasswordFormData } from '@/lib/validations';
import { useState } from 'react';

interface PasswordFormProps {
  termEnable: boolean;
  buttonText: string;
  onSubmit: (password: string, confirmPassword: string) => Promise<void>;
  isLoading?: boolean;
  error?: string | null;
  showSignInLink?: boolean;
  signInLinkText?: string;
  signInLinkHref?: string;
  isResetPassword?: boolean;
}

export default function PasswordForm({
  termEnable,
  buttonText,
  onSubmit,
  isLoading = false,
  error = null,
  showSignInLink = true,
  signInLinkText = 'Already have an account?',
  signInLinkHref = '/sign-in',
  isResetPassword = false,
}: PasswordFormProps) {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const form = useForm<PasswordFormData>({
    resolver: zodResolver(passwordFormSchema),
    defaultValues: {
      password: '',
      confirmPassword: '',
      acceptTerms: false,
    },
  });

  const password = form.watch('password');
  const confirmPassword = form.watch('confirmPassword');
  const acceptTerms = form.watch('acceptTerms');

  const getPasswordRequirements = (passwordValue: string) => {
    const minLength = 8;
    const hasUpperCase = /[A-Z]/.test(passwordValue);
    const hasLowerCase = /[a-z]/.test(passwordValue);
    const hasNumbers = /\d/.test(passwordValue);
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(passwordValue);

    return [
      {
        text: 'At least 8 characters',
        met: passwordValue.length >= minLength,
      },
      {
        text: 'One uppercase letter',
        met: hasUpperCase,
      },
      {
        text: 'One lowercase letter',
        met: hasLowerCase,
      },
      {
        text: 'One number',
        met: hasNumbers,
      },
      {
        text: 'One special character',
        met: hasSpecialChar,
      },
    ];
  };

  const passwordRequirements = useMemo(
    () => getPasswordRequirements(password),
    [password]
  );

  const allRequirementsMet = useMemo(
    () => passwordRequirements.every(req => req.met),
    [passwordRequirements]
  );

  const canSubmit = useMemo(() => {
    return (
      password.length > 0 &&
      confirmPassword.length > 0 &&
      (termEnable ? acceptTerms : true) &&
      allRequirementsMet &&
      password === confirmPassword
    );
  }, [password, confirmPassword, termEnable, acceptTerms, allRequirementsMet]);

  const handleSubmit = async (data: PasswordFormData) => {
    await onSubmit(data.password, data.confirmPassword);
  };

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(handleSubmit)}
        className={`w-full ${isResetPassword ? 'space-y-12' : 'space-y-6'}`}
      >
        <div className="space-y-6">
          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-header">
                  {isResetPassword ? 'New Password' : 'Password'}*
                </FormLabel>
                <FormControl>
                  <div className="relative">
                    <Input
                      type={showPassword ? 'text' : 'password'}
                      placeholder={
                        isResetPassword
                          ? 'Create a new password'
                          : 'Create a password'
                      }
                      className={`pr-10 focus-visible:ring-0 ${
                        form.formState.errors.password ||
                        (password.length > 0 && !allRequirementsMet)
                          ? 'border-red-500 focus-visible:border-red-500'
                          : password.length > 0 && allRequirementsMet
                            ? 'border-green-500 focus-visible:border-green-500'
                            : 'border-border'
                      }`}
                      {...field}
                    />
                    <div className="absolute right-0 top-0 h-full flex items-center gap-1">
                      {password.length > 0 && allRequirementsMet && (
                        <Check className="h-4 w-4 text-green-600" />
                      )}
                      <Button
                        type="button"
                        variant="ghost"
                        size="icon"
                        className="h-full px-3 hover:bg-transparent"
                        onClick={() => setShowPassword(!showPassword)}
                        tabIndex={-1}
                      >
                        {showPassword ? (
                          <EyeOff className="h-4 w-4 text-foreground" />
                        ) : (
                          <Eye className="h-4 w-4 text-border" />
                        )}
                      </Button>
                    </div>
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="confirmPassword"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-header">Confirm Password*</FormLabel>
                <FormControl>
                  <div className="relative">
                    <Input
                      type={showConfirmPassword ? 'text' : 'password'}
                      placeholder="Confirm your password"
                      className={`pr-20 focus-visible:ring-0 ${
                        form.formState.errors.confirmPassword ||
                        (confirmPassword.length > 0 &&
                          (!allRequirementsMet || password !== confirmPassword))
                          ? 'border-red-500 focus-visible:border-red-500'
                          : confirmPassword.length > 0 &&
                              !form.formState.errors.confirmPassword &&
                              allRequirementsMet &&
                              password === confirmPassword
                            ? 'border-green-500 focus-visible:border-green-500'
                            : 'border-border'
                      }`}
                      {...field}
                    />
                    <div className="absolute right-0 top-0 h-full flex items-center gap-1">
                      {confirmPassword.length > 0 &&
                        !form.formState.errors.confirmPassword &&
                        allRequirementsMet &&
                        password === confirmPassword && (
                          <Check className="h-4 w-4 text-green-600" />
                        )}
                      <Button
                        type="button"
                        variant="ghost"
                        size="icon"
                        className="h-full px-3 hover:bg-transparent"
                        onClick={() =>
                          setShowConfirmPassword(!showConfirmPassword)
                        }
                        tabIndex={-1}
                      >
                        {showConfirmPassword ? (
                          <EyeOff className="h-4 w-4 text-foreground" />
                        ) : (
                          <Eye className="h-4 w-4 text-border" />
                        )}
                      </Button>
                    </div>
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Password Requirements */}
          <Card className="shadow-none border-none bg-destructive-50 gap-3 py-4">
            <CardHeader className="px-4 gap-0">
              <CardTitle className="text-sub-header text-sm font-normal">
                Password Requirements
              </CardTitle>
            </CardHeader>
            <CardContent className="px-4">
              <div className="space-y-2">
                {passwordRequirements.map((requirement, index) => (
                  <div key={index} className="flex items-center gap-2">
                    {requirement.met ? (
                      <CircleCheckOutlineIcon className="h-4 w-4 text-success-600 fill-success-100" />
                    ) : (
                      <CircleXOutlineIcon className="h-4 w-4 text-destructive-400" />
                    )}
                    <span className="text-sm">{requirement.text}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Terms and Conditions */}
          {termEnable && (
            <FormField
              control={form.control}
              name="acceptTerms"
              render={({ field }) => (
                <FormItem>
                  <div className="flex gap-2">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                        className="mt-1 data-[state=checked]:bg-primary"
                      />
                    </FormControl>
                    <FormLabel className="text-sm text-foreground leading-relaxed">
                      Accept
                      <span className="underline cursor-pointer text-primary">
                        terms and conditions
                      </span>
                    </FormLabel>
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}
        </div>

        <Button
          type="submit"
          className="w-full bg-primary hover:bg-primary/90 text-primary-foreground py-3 h-12"
          disabled={isLoading || !canSubmit}
        >
          {isLoading ? (
            <>
              <Loader2 className="h-5 w-5 animate-spin mr-2" />
              {buttonText}...
            </>
          ) : (
            buttonText
          )}
        </Button>

        {error && (
          <div className="text-sm text-red-600 text-center">{error}</div>
        )}

        {showSignInLink && (
          <Body className="text-sm text-foreground text-center">
            {signInLinkText}{' '}
            <a
              href={signInLinkHref}
              className="underline cursor-pointer text-primary"
            >
              Sign in
            </a>
          </Body>
        )}
      </form>
    </Form>
  );
}
