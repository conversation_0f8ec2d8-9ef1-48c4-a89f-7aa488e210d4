import { withAuth } from 'next-auth/middleware';
import { NextResponse } from 'next/server';
import {
  AUTH_ROUTES,
  PROTECTED_ROUTES,
  RESET_PASSWORD_ROUTES,
  PUBLIC_ROUTES,
  Routes,
} from './lib/routes';
import { Role } from './types/enum';

export default withAuth(
  function middleware(req) {
    const token = req.nextauth.token;
    const isAuth = !!token;
    const userRole = token?.role as string;
    const pathname = req.nextUrl.pathname;

    // Redirect old /api paths to new /nextapi paths
    if (pathname.startsWith('/api/')) {
      const newPath = pathname.replace('/api/', '/nextapi/');
      const newUrl = new URL(newPath, req.url);
      newUrl.search = req.nextUrl.search;
      return NextResponse.redirect(newUrl);
    }

    // Fast path for static assets and API routes
    if (pathname.startsWith('/_next') || pathname.startsWith('/nextapi')) {
      return NextResponse.next();
    }

    const isAuthPage = AUTH_ROUTES.some(path => pathname.startsWith(path));
    const isResetPasswordPage = RESET_PASSWORD_ROUTES.some(path =>
      pathname.startsWith(path)
    );
    const isProtectedPage = PROTECTED_ROUTES.some(path =>
      pathname.startsWith(path)
    );
    const isPublicPage = PUBLIC_ROUTES.some(path => pathname.startsWith(path));

    // Don't interfere with NextAuth callbacks
    if (
      req.nextUrl.searchParams.has('callbackUrl') ||
      req.nextUrl.searchParams.has('error') ||
      req.nextUrl.searchParams.has('state')
    ) {
      return NextResponse.next();
    }

    // Allow public pages
    if (isPublicPage) {
      return NextResponse.next();
    }

    // Redirect authenticated users away from auth pages
    if (isAuth && isAuthPage && !isResetPasswordPage) {
      return NextResponse.redirect(new URL(Routes.DASHBOARD, req.url));
    }

    // Protect routes that require authentication
    if (isProtectedPage && !isAuth) {
      return NextResponse.redirect(new URL(Routes.SIGN_IN, req.url));
    }

    // Protect admin-only routes (flattened routes: /users, /locations, /tags)
    const adminOnlyRoutes = [
      Routes.ADMIN_USERS,
      Routes.ADMIN_LOCATIONS,
      Routes.ADMIN_TAGS,
    ];
    const isAdminOnlyRoute = adminOnlyRoutes.some(
      route => pathname === route || pathname.startsWith(`${route}/`)
    );

    if (isAdminOnlyRoute && isAuth && userRole !== Role.SuperAdmin) {
      return NextResponse.redirect(new URL(Routes.NO_PERMISSION, req.url));
    }

    // Handle root path
    if (pathname === '/') {
      const redirectUrl = isAuth ? Routes.DASHBOARD : Routes.SIGN_IN;
      return NextResponse.redirect(new URL(redirectUrl, req.url));
    }

    return NextResponse.next();
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        const { pathname } = req.nextUrl;
        const isProtectedPage = PROTECTED_ROUTES.some(path =>
          pathname.startsWith(path)
        );
        const isPublicPage = PUBLIC_ROUTES.some(path =>
          pathname.startsWith(path)
        );

        if (isPublicPage) return true;
        if (isProtectedPage && !token) return false;
        return true;
      },
    },
    pages: {
      signIn: '/sign-in',
    },
  }
);

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - assets (public assets like logos, images, etc.)
     */
    '/((?!api|_next/static|_next/image|favicon.ico|assets).*)',
  ],
};
