import { FILE_VALIDATION } from '@/types/constant';
/**
 * Validate a list of files
 * @param files - The list of files to validate
 * @returns The list of accepted files
 */
export const filesValidator = (files: File[]) => {
  const { ALLOWED_TYPES, MAX_SIZE } = FILE_VALIDATION;
  const acceptedFiles: File[] = [];

  for (const file of files) {
    // Check file extension
    const fileExtension = file.name.split('.').pop()?.toLowerCase();
    const allowedExtensions = ALLOWED_TYPES.map(type =>
      type.split('/')[1].toLowerCase().replace('jpeg', 'jpg')
    );

    // Check file type
    if (
      file.size <= MAX_SIZE &&
      ALLOWED_TYPES.length > 0 &&
      fileExtension &&
      allowedExtensions.includes(fileExtension)
    ) {
      acceptedFiles.push(file);
    }
  }

  return acceptedFiles;
};
