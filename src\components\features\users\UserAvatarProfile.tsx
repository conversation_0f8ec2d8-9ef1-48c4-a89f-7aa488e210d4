import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { cn, getUserInitials } from '@/lib/utils';
import { User } from 'next-auth';

interface UserAvatarProfileProps {
  className?: string;
  showInfo?: boolean;
  user: User | null;
}

export function UserAvatarProfile({
  className,
  showInfo = false,
  user,
}: UserAvatarProfileProps) {
  return (
    <div className="flex items-center gap-2">
      <Avatar className={cn('size-10', className)}>
        <AvatarImage
          src={user?.avatar || ''}
          alt={`${user?.firstName} ${user?.lastName}` || ''}
        />
        <AvatarFallback className="rounded-lg bg-primary-50 text-primary-300 text-base leading-7">
          {getUserInitials(`${user?.firstName} ${user?.lastName}`) || 'SC'}
        </AvatarFallback>
      </Avatar>

      {showInfo && (
        <div className="grid flex-1 text-left text-sm leading-tight">
          <span className="truncate font-semibold">
            {user?.firstName || ''}
          </span>
          <span className="truncate font-semibold">{user?.lastName || ''}</span>
          <span className="truncate text-xs">{user?.email || ''}</span>
        </div>
      )}
    </div>
  );
}
