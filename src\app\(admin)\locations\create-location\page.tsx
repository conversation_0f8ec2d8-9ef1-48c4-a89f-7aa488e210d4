'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Routes } from '@/lib/routes';
import { Loader2 } from 'lucide-react';
import { useEffect, useMemo, useState } from 'react';
import { Step } from '@/components/StepIndicator';
import { ArrowRightIcon } from '@/lib/icons';
import { isEmpty } from 'lodash';
import { useRouter } from 'next/navigation';
import ConfirmDialog from '@/components/shared/ConfirmDialog';
import ImagesForm from '@/components/features/locations/ImagesForm';
import { MINIMUM_IMAGES, PROPERTY_CATEGORY } from '@/types/constant';
import BasicDetailsForm from '@/components/BasicDetailsForm';
import TagsForm from '@/components/features/tags/TagsForm';
import { ProcessBar } from '@/components/features/locations/ProcessBar';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import { ChevronRight } from 'lucide-react';
import { useLocationService, useTagService } from '@/hooks/use-services';
import { TagOption, Tag } from '@/types/tag';
import { CreateLocationData } from '@/lib/services/location-service';
import { LocationRequest, Photo } from '@/types/location';
import { toastError, toastSuccess, toastWarning } from '@/lib/toast';
import { Body, Heading } from '@/components/ui/typography';
import { TagType } from '@/types/enum';

enum StepValue {
  BASIC_DETAIL = 'basic-details',
  TAGS = 'tags',
  IMAGES = 'images',
}
const stepOrder = Object.values(StepValue);
const steps: (Step & { flex?: string })[] = [
  { label: 'Basic Details', value: StepValue.BASIC_DETAIL, flex: 'flex-3' },
  { label: 'Tags', value: StepValue.TAGS, flex: 'flex-2' },
  { label: 'Images', value: StepValue.IMAGES },
];

export default function CreateLocationPage() {
  const router = useRouter();
  const tagService = useTagService();
  const locationService = useLocationService();
  const initialFormData: LocationRequest = {
    address: '',
    description: '',
    contactEmail: '',
    contactPhones: [],
    propertyType: '',
    tags: {},
    size: '',
    images: [],
  };
  const [formData, setFormData] = useState<LocationRequest>(initialFormData);
  const [currentStep, setCurrentStep] = useState<string>(
    StepValue.BASIC_DETAIL
  );
  const [discardDialogOpen, setDiscardDialogOpen] = useState<boolean>(false);
  const [tagsByType, setTagsByType] = useState<
    Partial<Record<TagType, TagOption[]>>
  >({});
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [hasUploadingFiles, setHasUploadingFiles] = useState<boolean>(false);

  // Preload all tags and organize by category
  useEffect(() => {
    const fetchAllTags = async () => {
      try {
        const allTags = await tagService.getAllTags();

        // Convert tags to TagOption format and organize by category
        const organizedTags: Partial<Record<TagType, TagOption[]>> = {};

        allTags.forEach((tag: Tag) => {
          const tagOption: TagOption = {
            value: tag.id,
            label: tag.name,
            color: tag.color,
            type: tag.type as TagType,
          };

          // Only include tags with valid TagCategory types
          if (Object.values(TagType).includes(tag.type as TagType)) {
            const type = tag.type as TagType;
            if (!organizedTags[type]) {
              organizedTags[type] = [];
            }
            organizedTags[type]!.push(tagOption);
          }
        });

        setTagsByType(organizedTags);
      } catch (error) {
        console.error('Error fetching tags:', error);
      }
    };

    fetchAllTags();
  }, [tagService]);

  const isDirty = useMemo(() => {
    // check if the formData is different from the initial formData
    console.log('formData', formData);
    return JSON.stringify(formData) !== JSON.stringify(initialFormData);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [formData]);

  const handleContinue = () => {
    switch (currentStep) {
      case StepValue.BASIC_DETAIL:
        setCurrentStep(StepValue.TAGS);
        break;
      case StepValue.TAGS:
        setCurrentStep(StepValue.IMAGES);
        break;
      default:
        break;
    }
  };

  const buildCreateLocationPayload = (
    data: LocationRequest
  ): CreateLocationData => {
    const {
      address,
      latitude,
      longitude,
      description,
      tags,
      size,
      images,
      placeId,
      city,
      state,
      country,
      postalCode,
    } = data;

    // Flatten tags object into array of tag IDs
    const tagIds = Object.values(tags || {}).flat();
    return {
      address,
      latitude,
      longitude,
      description,
      placeId,
      city,
      state,
      country,
      postalCode,
      tagIds,
      size,
      images: images.map(image => ({
        url: image.url,
        order: image.order,
        key: image.key || '',
        tagIds: (image.tags ?? []).map(tag => tag.id),
      })),
    };
  };

  const handleCreateLocation = async () => {
    setIsLoading(true);
    try {
      if (formData.images.some(image => isEmpty(image.tags))) {
        toastWarning('Not all images have a tag.');
      } else {
        const payload = buildCreateLocationPayload(formData);
        await locationService.createLocation(payload);
        toastSuccess('Location created successfully.');
        router.push(Routes.ADMIN_LOCATIONS);
      }
    } catch {
      toastError('Failed to create location');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    if (isDirty) {
      setDiscardDialogOpen(true);
    } else {
      router.push(Routes.ADMIN_LOCATIONS);
    }
  };

  const handleChange = (data: Partial<LocationRequest>) => {
    if (data) {
      setFormData(prev => ({
        ...prev,
        ...data,
      }));
    }
  };

  const handleImagesChange = (newImages: Photo[]) => {
    setFormData(prev => ({
      ...prev,
      images: newImages,
    }));
  };

  const validateForm = (data: Partial<LocationRequest>) => {
    if (!data) {
      return true;
    }
    if (currentStep === StepValue.BASIC_DETAIL) {
      // Check required fields
      if (!data.address || !data.description || !data.contactEmail) {
        return true;
      }
      // Check if at least one phone has 10 digits
      const hasValidPhone =
        data.contactPhones &&
        data.contactPhones.some(
          phone => phone.replace(/\D/g, '').length === 10
        );
      return !hasValidPhone;
    }
    if (currentStep === StepValue.TAGS) {
      // Validate that at least one tag is selected for each required category
      if (!data.propertyType) return true;
      const requiredCategories = PROPERTY_CATEGORY[data.propertyType] || [];
      const hasAllRequiredTags = requiredCategories.every(
        category => data.tags?.[category] && data.tags[category]!.length > 0
      );
      return !hasAllRequiredTags || !data.size;
    }
    if (currentStep === StepValue.IMAGES) {
      return (
        !data.images ||
        (Array.isArray(data.images) && data.images.length < MINIMUM_IMAGES)
      );
    }
  };

  return (
    <div className="flex flex-col h-full">
      <div
        className={`flex flex-1 flex-col items-center pb-20 py-4 sm:py-6 overflow-y-scroll ${currentStep !== StepValue.IMAGES ? 'sm:mb-0' : ''}`}
      >
        <div className="max-w-282 w-full">
          {/* Breadcrumb */}
          <div className="mb-8 py-4">
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem>
                  <BreadcrumbLink
                    href={Routes.ADMIN_LOCATIONS}
                    className="text-primary-200 hover:text-primary-300"
                  >
                    Locations
                  </BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator>
                  <ChevronRight className="h-4 w-4 text-primary-400" />
                </BreadcrumbSeparator>
                <BreadcrumbItem>
                  <BreadcrumbPage className="text-primary-400">
                    Create Location
                  </BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>

          <div className="max-w-152 mx-auto mb-12">
            <div className="w-[413px]">
              <ProcessBar
                currentStep={
                  stepOrder.findIndex(step => step === currentStep) + 1
                }
                steps={steps.map(step => ({
                  label: step.label,
                  value: step.value,
                  flex: step.flex,
                }))}
                onStepClick={stepValue => {
                  setCurrentStep(stepValue);
                }}
              />
            </div>
          </div>
          <div className="w-full mx-auto">
            <div className="space-y-8">
              <div className="w-full max-w-152 mx-auto flex flex-col gap-8">
                {currentStep === StepValue.BASIC_DETAIL && (
                  <div className="space-y-6">
                    <div>
                      <Heading level={3} className="leading-9">
                        Submit location
                      </Heading>
                      <Body className="mt-3 leading-7">
                        Start by adding the basic details for the location.
                      </Body>
                    </div>
                    <BasicDetailsForm data={formData} onChange={handleChange} />
                  </div>
                )}
                {currentStep === StepValue.TAGS && (
                  <div className="space-y-6">
                    <div>
                      <Heading level={3} className="leading-9">
                        What best describes this location?
                      </Heading>
                      <Body className="mt-3 leading-7">
                        Select the tags that capture the look and feel of the
                        location. These tags make it easier for scouts and prod.
                        admins to discover the right fit.
                      </Body>
                    </div>
                    <TagsForm
                      data={formData}
                      tagsByType={tagsByType}
                      onChange={handleChange}
                    />
                  </div>
                )}
              </div>

              {currentStep === StepValue.IMAGES && (
                <div
                  className={`w-full mx-auto flex flex-col gap-8 ${formData.images.length > 0 ? '' : 'max-w-152'}`}
                >
                  <ImagesForm
                    data={formData.images}
                    onChange={handleImagesChange}
                    onUploadStatusChange={setHasUploadingFiles}
                  />
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
      <div className="bg-white z-50 h-22 px-4 sm:px-0 mr-2 border-t flex justify-center items-center shadow-sm">
        <div
          className={`w-full flex items-center justify-between ${
            currentStep !== StepValue.IMAGES || formData.images.length === 0
              ? 'max-w-152'
              : 'max-w-282'
          }`}
        >
          <Button
            variant="outline"
            onClick={handleCancel}
            className="px-7 py-6"
          >
            Cancel
          </Button>
          {currentStep === StepValue.IMAGES && formData.images.length > 0 ? (
            <Button
              onClick={handleCreateLocation}
              disabled={
                validateForm(formData) || isLoading || hasUploadingFiles
              }
              className="px-12 py-6"
            >
              {isLoading ? (
                <>
                  Creating ... <Loader2 className="w-4 h-4 animate-spin" />
                </>
              ) : (
                'Confirm'
              )}
            </Button>
          ) : (
            <Button
              onClick={handleContinue}
              disabled={validateForm(formData)}
              className="px-12 py-6"
            >
              Continue
              <ArrowRightIcon color="white" className="h-4 w-4 ml-2" />
            </Button>
          )}
        </div>
      </div>
      <ConfirmDialog
        open={discardDialogOpen}
        onOpenChange={setDiscardDialogOpen}
        title="Discard reference?"
        description="You’ve started filling in this reference’s details. If you cancel now, the information will be lost."
        confirmText="Discard"
        cancelText="Keep editing"
        onConfirm={() => router.push(Routes.ADMIN_LOCATIONS)}
        onCancel={() => setDiscardDialogOpen(false)}
      />
    </div>
  );
}
