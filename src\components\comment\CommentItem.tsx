import { getRelativeTime, getUserInitials } from '@/lib/utils';
import { Avatar, AvatarFallback, AvatarImage } from '../ui/avatar';
import { Body } from '../ui/typography';
import { Comment } from '@/types';
import { Button } from '../ui/button';
import { TrashIcon } from 'lucide-react';
import { useState } from 'react';
import { PencilIcon } from 'lucide-react';
import { Textarea } from '../ui/textarea';
import { IconButton } from '@/components/shared/IconButton';
import ConfirmDialog from '@/components/shared/ConfirmDialog';
import { toastSuccess } from '@/lib/toast';

interface CommentItemProps {
  comment: Comment;
  isOwnComment: boolean;
  onDelete: (commentId: string) => void;
  onEdit: (commentId: string, content: string) => void;
}

export const CommentItem = ({
  comment,
  isOwnComment,
  onDelete,
  onEdit,
}: CommentItemProps) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editedContent, setEditedContent] = useState(comment.content);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

  const handleDelete = () => {
    onDelete(comment.id);
  };

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleSave = () => {
    onEdit(comment.id, editedContent);
    setIsEditing(false);
    toastSuccess('Comment updated.');
  };

  return (
    <div key={comment.id} className="flex items-start gap-3 group">
      <Avatar>
        <AvatarImage src={comment.author.avatar} alt={comment.author.name} />
        <AvatarFallback>
          {getUserInitials(
            `${comment.author.firstName} ${comment.author.lastName}`
          )}
        </AvatarFallback>
      </Avatar>
      <div className="flex flex-col items-start gap-2 w-full">
        <div className="flex items-center justify-between w-full">
          <Body className="text-sm leading-5">
            {comment.author.firstName} {comment.author.lastName}
            <span className="text-muted-foreground text-xs ml-2">
              {getRelativeTime(comment.createdAt)}
            </span>
          </Body>
          {isOwnComment && (
            <div className="flex items-center opacity-0 group-hover:opacity-100 transition-opacity">
              <IconButton size="small" onClick={handleEdit}>
                <PencilIcon className="w-4 h-4" />
              </IconButton>
              <IconButton
                size="small"
                onClick={() => setDeleteDialogOpen(true)}
              >
                <TrashIcon className="w-4 h-4" />
              </IconButton>
            </div>
          )}
        </div>
        {isEditing ? (
          <div className="flex flex-col items-start gap-2 w-full">
            <Textarea
              id="edit-comment"
              value={editedContent}
              onChange={e => setEditedContent(e.target.value)}
              placeholder="Edit your comment..."
              className="w-full min-h-[100px] flex-1"
            />
            <div className="flex gap-4 justify-end w-full">
              <Button variant="outline" onClick={() => setIsEditing(false)}>
                Cancel
              </Button>
              <Button variant="default" onClick={handleSave}>
                Save Changes
              </Button>
            </div>
          </div>
        ) : (
          <Body className="whitespace-pre-wrap break-words">
            {comment.content.split('\n').map((line, index, array) => (
              <span key={index}>
                {line}
                {index < array.length - 1 && <br />}
              </span>
            ))}
          </Body>
        )}
      </div>
      <ConfirmDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        title="Delete this comment?"
        description="Are you sure you want to delete this comment? This action cannot be undone."
        confirmText="Delete"
        cancelText="Cancel"
        onConfirm={handleDelete}
        onCancel={() => setDeleteDialogOpen(false)}
      />
    </div>
  );
};
