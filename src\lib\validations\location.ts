import { z } from 'zod';

/**
 * Validation schema for location basic details
 */
export const locationBasicDetailsSchema = z.object({
  address: z.string().min(1, 'Address is required').trim(),
  placeId: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  country: z.string().optional(),
  postalCode: z.string().optional(),
  latitude: z.number().optional(),
  longitude: z.number().optional(),
  description: z
    .string()
    .min(1, 'Description is required')
    .trim()
    .max(5000, 'Description must be less than 5000 characters'),
  contactEmail: z
    .string()
    .min(1, 'Contact email is required')
    .email('Invalid email address')
    .trim(),
  contactPhones: z
    .array(z.string())
    .min(1, 'At least one contact phone is required')
    .refine(
      phones => phones.some(phone => phone.replace(/\D/g, '').length === 10),
      'At least one phone number must be complete (10 digits)'
    ),
});

export type LocationBasicDetailsFormData = z.infer<
  typeof locationBasicDetailsSchema
>;
